"""Vision Tools for Unreal MCP.

This module provides tools for creating and managing vision systems in Unreal Engine,
including fog of war, line of sight, and multilayer vision interactions.
"""

import logging
from typing import Dict, List, Any, Optional
from mcp.server.fastmcp import FastMCP, Context

# Get logger
logger = logging.getLogger("UnrealMCP")

def register_vision_tools(mcp: FastMCP):
    """Register Vision tools with the MCP server."""

    @mcp.tool()
    def create_ward_system(
        ctx: Context,
        ward_config: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Create the comprehensive ward system with all ward types.

        Args:
            ctx: MCP Context
            ward_config: Ward configuration as JSON object containing:
                - enable_stealth_ward: Enable Stealth Ward (default: True)
                - enable_control_ward: Enable Control Ward (default: True)
                - enable_farsight_ward: Enable Farsight Ward (default: True)

        Returns:
            Dict[str, Any]: Operation result
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            # Default configuration for Ward System
            default_config = {
                "system_name": "comprehensive_ward_system",
                "ward_types": {
                    "stealth_ward": {
                        "name": "Stealth Ward",
                        "enabled": True,
                        "health": 3,
                        "duration": 150,
                        "vision_range": 900,
                        "cost": 0,
                        "charges": 2,
                        "charge_recharge_time": 240,
                        "placement_range": 600,
                        "stealth": True,
                        "reveals_stealth": False,
                        "special_mechanics": [
                            "invisible_to_enemies",
                            "destroyed_by_3_auto_attacks",
                            "grants_gold_when_killed",
                            "trinket_item"
                        ]
                    },
                    "control_ward": {
                        "name": "Control Ward",
                        "enabled": True,
                        "health": 4,
                        "duration": "infinite",
                        "vision_range": 900,
                        "cost": 75,
                        "charges": "unlimited",
                        "placement_range": 600,
                        "stealth": False,
                        "reveals_stealth": True,
                        "disable_range": 900,
                        "special_mechanics": [
                            "visible_to_enemies",
                            "reveals_enemy_wards",
                            "disables_enemy_wards",
                            "destroyed_by_4_auto_attacks",
                            "grants_gold_when_killed",
                            "purchasable_item"
                        ]
                    },
                    "farsight_ward": {
                        "name": "Farsight Ward",
                        "enabled": True,
                        "health": 1,
                        "duration": "infinite",
                        "vision_range": 1200,
                        "cost": 0,
                        "charges": 1,
                        "charge_recharge_time": 198,
                        "placement_range": 4000,
                        "stealth": False,
                        "reveals_stealth": False,
                        "special_mechanics": [
                            "visible_to_enemies",
                            "extremely_long_placement_range",
                            "destroyed_by_1_auto_attack",
                            "grants_gold_when_killed",
                            "trinket_upgrade"
                        ]
                    }
                },
                "ward_mechanics": {
                    "max_wards_per_player": 3,
                    "ward_limit_stealth": 2,
                    "ward_limit_control": 1,
                    "ward_limit_farsight": 1,
                    "vision_score_calculation": True,
                    "ward_kill_gold": 30,
                    "ward_placement_delay": 0.5,
                    "ward_vision_delay": 1.0
                },
                "vision_interactions": {
                    "stealth_detection": {
                        "control_ward_reveals": True,
                        "sweeper_reveals": True,
                        "champion_abilities_reveal": True
                    },
                    "ward_interactions": {
                        "control_ward_disables_others": True,
                        "wards_grant_vision_score": True,
                        "destroying_wards_grants_gold": True
                    }
                }
            }

            # Merge provided config with defaults
            if ward_config:
                default_config.update(ward_config)

            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            logger.info(f"Creating ward system with config: {default_config}")
            response = unreal.send_command("create_ward_system", default_config)

            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}

            logger.info(f"Ward system creation response: {response}")
            return response

        except Exception as e:
            error_msg = f"Error creating ward system: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}

    @mcp.tool()
    def create_multilayer_vision_system(
        ctx: Context,
        vision_config: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Create multilayer vision system for the three-layer map.

        Args:
            ctx: MCP Context
            vision_config: Vision configuration as JSON object containing:
                - enable_layer_specific_vision: Enable layer-specific vision mechanics (default: True)
                - enable_cross_layer_vision: Enable limited cross-layer vision (default: True)

        Returns:
            Dict[str, Any]: Operation result
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            # Default configuration for Multilayer Vision
            default_config = {
                "system_name": "multilayer_vision_system",
                "layer_vision_mechanics": {
                    "planicie_radiante": {
                        "layer_name": "Planície Radiante",
                        "base_vision_range": 1200,
                        "fog_of_war_enabled": True,
                        "special_vision_mechanics": [
                            "light_crystals_extend_vision_range",
                            "solar_towers_provide_area_vision",
                            "clear_terrain_standard_vision"
                        ],
                        "vision_modifiers": {
                            "near_light_crystals": 1.2,
                            "near_solar_towers": 1.5,
                            "in_jungle": 0.8
                        }
                    },
                    "firmamento_zephyr": {
                        "layer_name": "Firmamento Zephyr",
                        "base_vision_range": 1000,
                        "fog_of_war_enabled": True,
                        "special_vision_mechanics": [
                            "wind_currents_create_vision_corridors",
                            "mobile_platforms_dynamic_vision",
                            "elevation_provides_extended_vision"
                        ],
                        "vision_modifiers": {
                            "on_wind_currents": 1.3,
                            "on_mobile_platforms": 1.1,
                            "at_high_elevation": 1.4
                        }
                    },
                    "abismo_umbral": {
                        "layer_name": "Abismo Umbral",
                        "base_vision_range": 800,
                        "fog_of_war_enabled": True,
                        "special_vision_mechanics": [
                            "shadow_zones_reduce_vision_50_percent",
                            "shadow_fog_conceals_movements",
                            "labyrinthine_layout_blocks_vision"
                        ],
                        "vision_modifiers": {
                            "in_shadow_zones": 0.5,
                            "in_shadow_fog": 0.3,
                            "in_corridors": 0.7,
                            "in_chambers": 1.0
                        }
                    }
                },
                "cross_layer_vision": {
                    "enabled": True,
                    "vision_range_between_layers": 400,
                    "requires_line_of_sight": True,
                    "special_locations": {
                        "portals": "provide_vision_to_connected_layer",
                        "elevators": "provide_vision_during_travel",
                        "bridges": "provide_vision_across_layers"
                    }
                },
                "vision_score_system": {
                    "enabled": True,
                    "ward_placement_score": 1.0,
                    "ward_duration_score_per_minute": 0.5,
                    "enemy_ward_destruction_score": 1.0,
                    "vision_denial_score": 0.5
                }
            }

            # Merge provided config with defaults
            if vision_config:
                default_config.update(vision_config)

            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            logger.info(f"Creating multilayer vision system with config: {default_config}")
            response = unreal.send_command("create_multilayer_vision_system", default_config)

            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}

            logger.info(f"Multilayer vision system creation response: {response}")
            return response

        except Exception as e:
            error_msg = f"Error creating multilayer vision system: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def create_fog_of_war_layer(
        ctx: Context,
        layer_name: str,
        layer_height: float,
        fog_settings: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Cria uma nova camada de Fog of War para visão tridimensional."""
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            params = {
                "layer_name": layer_name,
                "layer_height": float(layer_height),
                "fog_settings": fog_settings
            }
            
            logger.info(f"Creating fog of war layer with params: {params}")
            response = unreal.send_command("create_fog_of_war_layer", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Create fog of war layer response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error creating fog of war layer: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def configure_vision_range_layer(
        ctx: Context,
        layer_name: str,
        vision_settings: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Configura alcances de visão específicos para uma camada."""
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            params = {
                "layer_name": layer_name,
                "vision_settings": vision_settings
            }
            
            logger.info(f"Configuring vision range layer with params: {params}")
            response = unreal.send_command("configure_vision_range", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Configure vision range layer response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error configuring vision range layer: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def setup_line_of_sight_system(
        ctx: Context,
        layer_name: str,
        los_settings: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Configura sistema de Line of Sight para uma camada específica."""
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            params = {
                "layer_name": layer_name,
                "los_settings": los_settings
            }
            
            logger.info(f"Setting up line of sight system with params: {params}")
            response = unreal.send_command("setup_line_of_sight", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Setup line of sight system response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error setting up line of sight system: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def create_vision_blocking_volumes(
        ctx: Context,
        layer_name: str,
        volumes_data: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Cria volumes que bloqueiam visão em uma camada específica."""
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            params = {
                "layer_name": layer_name,
                "volumes_data": volumes_data
            }
            
            logger.info(f"Creating vision blocking volumes with params: {params}")
            response = unreal.send_command("create_vision_blocking_volumes", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Create vision blocking volumes response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error creating vision blocking volumes: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def configure_dynamic_fog_updates(
        ctx: Context,
        layer_name: str,
        update_settings: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Configura atualizações dinâmicas do Fog of War."""
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            params = {
                "layer_name": layer_name,
                "update_settings": update_settings
            }
            
            logger.info(f"Configuring dynamic fog updates with params: {params}")
            response = unreal.send_command("update_fog_visibility", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Configure dynamic fog updates response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error configuring dynamic fog updates: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    # Função removida - não implementada no C++
    
    # Função removida - não implementada no C++
    
    @mcp.tool()
    def configure_vision_occlusion_system(
        ctx: Context,
        layer_name: str,
        occlusion_settings: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Configura sistema de oclusão para otimização de visão."""
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            params = {
                "layer_name": layer_name,
                "occlusion_settings": occlusion_settings
            }
            
            logger.info(f"Configuring vision occlusion system with params: {params}")
            response = unreal.send_command("configure_occlusion_system", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Configure vision occlusion system response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error configuring vision occlusion system: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    # Função removida - não implementada no C++
    
    # Função removida - não implementada no C++
    
    @mcp.tool()
    def optimize_vision_performance(
        ctx: Context,
        layer_name: str,
        optimization_settings: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Otimiza performance do sistema de visão."""
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            params = {
                "layer_name": layer_name,
                "optimization_settings": optimization_settings
            }
            
            logger.info(f"Optimizing vision performance with params: {params}")
            response = unreal.send_command("optimize_vision_performance", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Optimize vision performance response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error optimizing vision performance: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def debug_vision_system(
        ctx: Context,
        layer_name: str,
        debug_options: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Ativa visualização de debug para o sistema de visão."""
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            params = {
                "layer_name": layer_name,
                "debug_options": debug_options
            }
            
            logger.info(f"Debugging vision system with params: {params}")
            response = unreal.send_command("debug_vision_system", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Debug vision system response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error debugging vision system: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def validate_vision_setup(
        ctx: Context,
        layers_to_validate: List[str]
    ) -> Dict[str, Any]:
        """Valida a configuração do sistema de visão multicamada."""
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            params = {
                "layers_to_validate": layers_to_validate
            }
            
            logger.info(f"Validating vision setup with params: {params}")
            response = unreal.send_command("validate_vision_setup", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Validate vision setup response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error validating vision setup: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def get_vision_system_status(
        ctx: Context,
        include_performance_metrics: bool = False
    ) -> Dict[str, Any]:
        """Obtém o status do sistema de visão tridimensional."""
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            params = {
                "include_performance_metrics": include_performance_metrics
            }
            
            logger.info(f"Getting vision system status with params: {params}")
            response = unreal.send_command("get_vision_system_status", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Get vision system status response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error getting vision system status: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    # === Ferramentas Específicas da Arquitetura Auracron ===
    
    @mcp.tool()
    def configure_auracron_vision_layers(
        ctx: Context
    ) -> Dict[str, Any]:
        """Configura camadas de visão específicas da arquitetura Auracron (Planície, Firmamento, Abismo)."""
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            params = {}
            
            logger.info(f"Configuring Auracron vision layers with params: {params}")
            response = unreal.send_command("configure_auracron_vision_layers", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Configure Auracron vision layers response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error configuring Auracron vision layers: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def setup_multilayer_vision_system(
        ctx: Context,
        vision_update_frequency: float = 30.0,
        max_actors_per_frame: int = 50,
        enable_fog_of_war: bool = True,
        enable_cross_layer_vision: bool = True
    ) -> Dict[str, Any]:
        """Configura sistema de visão multicamada para a arquitetura Auracron."""
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            params = {
                "vision_update_frequency": float(vision_update_frequency),
                "max_actors_per_frame": int(max_actors_per_frame),
                "enable_fog_of_war": bool(enable_fog_of_war),
                "enable_cross_layer_vision": bool(enable_cross_layer_vision)
            }
            
            logger.info(f"Setting up multilayer vision system with params: {params}")
            response = unreal.send_command("setup_multilayer_vision_system", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Setup multilayer vision system response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error setting up multilayer vision system: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def configure_layer_vision_ranges(
        ctx: Context,
        layer_name: str,
        vision_range: Optional[float] = None
    ) -> Dict[str, Any]:
        """Configura alcances de visão específicos por camada (Planície: 1800u, Firmamento: 2200u, Abismo: 1400u)."""
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            params = {
                "layer_name": str(layer_name)
            }
            
            if vision_range is not None:
                params["vision_range"] = float(vision_range)
            
            logger.info(f"Configuring layer vision ranges with params: {params}")
            response = unreal.send_command("configure_layer_vision_ranges", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Configure layer vision ranges response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error configuring layer vision ranges: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def setup_vertical_connector_vision(
        ctx: Context,
        connector_vision_range: float = 800.0,
        vertical_vision_penalty: float = 0.4,
        enable_cross_layer_sight: bool = True
    ) -> Dict[str, Any]:
        """Configura visão para conectores verticais entre camadas da arquitetura Auracron."""
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            params = {
                "connector_vision_range": float(connector_vision_range),
                "vertical_vision_penalty": float(vertical_vision_penalty),
                "enable_cross_layer_sight": bool(enable_cross_layer_sight)
            }
            
            logger.info(f"Setting up vertical connector vision with params: {params}")
            response = unreal.send_command("setup_vertical_connector_vision", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Setup vertical connector vision response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error setting up vertical connector vision: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    # === Funções Auxiliares Removidas ===
    # As funções auxiliares foram removidas pois não têm correspondência no C++
    
    logger.info("Vision tools registered successfully")