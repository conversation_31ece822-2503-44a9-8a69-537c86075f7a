// Copyright Epic Games, Inc. All Rights Reserved.

#include "Commands/UnrealMCPVerticalNavigationCommands.h"

// ========================================================================
// Constantes
// ========================================================================

// Tipos de resposta
const FString FUnrealMCPVerticalNavigationCommands::RESPONSE_SUCCESS = TEXT("success");
const FString FUnrealMCPVerticalNavigationCommands::RESPONSE_ERROR = TEXT("error");
const FString FUnrealMCPVerticalNavigationCommands::RESPONSE_WARNING = TEXT("warning");
const FString FUnrealMCPVerticalNavigationCommands::RESPONSE_INFO = TEXT("info");

// Tipos de navegação
const FString FUnrealMCPVerticalNavigationCommands::NAVIGATION_TYPE_PORTAL = TEXT("portal");
const FString FUnrealMCPVerticalNavigationCommands::NAVIGATION_TYPE_ELEVATOR = TEXT("elevator");
const FString FUnrealMCPVerticalNavigationCommands::NAVIGATION_TYPE_BRIDGE = TEXT("bridge");

// Nomes das camadas
const FString FUnrealMCPVerticalNavigationCommands::LAYER_PLANICIE_RADIANTE = TEXT("planicie_radiante");
const FString FUnrealMCPVerticalNavigationCommands::LAYER_FIRMAMENTO_ZEPHYR = TEXT("firmamento_zephyr");
const FString FUnrealMCPVerticalNavigationCommands::LAYER_ABISMO_UMBRAL = TEXT("abismo_umbral");

// ========================================================================
// Construtor e Destrutor
// ========================================================================

FUnrealMCPVerticalNavigationCommands::FUnrealMCPVerticalNavigationCommands()
    : bIsInitialized(false)
    , LastUpdateTime(FDateTime::Now())
{
    bIsInitialized = true;
    UE_LOG(LogTemp, Log, TEXT("FUnrealMCPVerticalNavigationCommands: Sistema de Navegação Vertical inicializado"));
}

FUnrealMCPVerticalNavigationCommands::~FUnrealMCPVerticalNavigationCommands()
{
    // Limpar caches
    NavigationConfigCache.Empty();
    NavigationStates.Empty();
    
    UE_LOG(LogTemp, Log, TEXT("FUnrealMCPVerticalNavigationCommands: Sistema de Navegação Vertical finalizado"));
}

// ========================================================================
// Método Principal de Comando
// ========================================================================

TSharedPtr<FJsonObject> FUnrealMCPVerticalNavigationCommands::HandleCommand(const FString& CommandType, const TSharedPtr<FJsonObject>& Params)
{
    if (CommandType == TEXT("create_primary_portals_system"))
    {
        return HandleCreatePrimaryPortalsSystem(Params);
    }
    else if (CommandType == TEXT("create_mystical_elevators_system"))
    {
        return HandleCreateMysticalElevatorsSystem(Params);
    }
    else if (CommandType == TEXT("create_dimensional_bridges_system"))
    {
        return HandleCreateDimensionalBridgesSystem(Params);
    }
    else if (CommandType == TEXT("setup_navigation_strategies"))
    {
        return HandleSetupNavigationStrategies(Params);
    }
    else
    {
        return CreateErrorResponse(FString::Printf(TEXT("Comando não reconhecido: %s"), *CommandType), TEXT("UNKNOWN_COMMAND"));
    }
}

// ========================================================================
// Implementações dos Comandos
// ========================================================================

TSharedPtr<FJsonObject> FUnrealMCPVerticalNavigationCommands::HandleCreatePrimaryPortalsSystem(const TSharedPtr<FJsonObject>& CommandData)
{
    if (!CommandData.IsValid())
    {
        return CreateErrorResponse(TEXT("Dados do comando inválidos"), TEXT("INVALID_COMMAND_DATA"));
    }
    
    UE_LOG(LogTemp, Log, TEXT("Criando sistema de portais primários"));
    
    // Extrair configurações dos portais
    int32 PortalsPerLayer = 4;
    float CooldownSeconds = 10.0f;
    float TravelTimeSeconds = 2.0f;
    bool bEnableAllLayers = true;
    
    // Ler configurações do JSON se fornecidas
    CommandData->TryGetNumberField(TEXT("portals_per_layer"), PortalsPerLayer);
    CommandData->TryGetNumberField(TEXT("cooldown_seconds"), CooldownSeconds);
    CommandData->TryGetNumberField(TEXT("travel_time_seconds"), TravelTimeSeconds);
    CommandData->TryGetBoolField(TEXT("enable_all_layers"), bEnableAllLayers);
    
    // Criar configuração do sistema de portais
    TSharedPtr<FJsonObject> SystemConfig = MakeShared<FJsonObject>();
    SystemConfig->SetStringField(TEXT("system_name"), TEXT("primary_portals_system"));
    SystemConfig->SetNumberField(TEXT("portals_per_layer"), PortalsPerLayer);
    SystemConfig->SetNumberField(TEXT("cooldown_seconds"), CooldownSeconds);
    SystemConfig->SetNumberField(TEXT("travel_time_seconds"), TravelTimeSeconds);
    SystemConfig->SetBoolField(TEXT("enable_all_layers"), bEnableAllLayers);
    
    // Configurar portais por camada
    TSharedPtr<FJsonObject> LayerPortals = MakeShared<FJsonObject>();
    
    // Portais da Planície Radiante
    TSharedPtr<FJsonObject> PlaniciePortals = MakeShared<FJsonObject>();
    PlaniciePortals->SetStringField(TEXT("layer_name"), LAYER_PLANICIE_RADIANTE);
    PlaniciePortals->SetNumberField(TEXT("portal_count"), PortalsPerLayer);
    PlaniciePortals->SetStringField(TEXT("portal_type"), TEXT("terrestrial"));
    
    TArray<TSharedPtr<FJsonValue>> PlanicieConnections;
    PlanicieConnections.Add(MakeShared<FJsonValueString>(LAYER_FIRMAMENTO_ZEPHYR));
    PlanicieConnections.Add(MakeShared<FJsonValueString>(LAYER_ABISMO_UMBRAL));
    PlaniciePortals->SetArrayField(TEXT("connections"), PlanicieConnections);
    
    TArray<TSharedPtr<FJsonValue>> PlanicieLocations;
    PlanicieLocations.Add(MakeShared<FJsonValueString>(TEXT("Próximo à Top Lane")));
    PlanicieLocations.Add(MakeShared<FJsonValueString>(TEXT("Próximo à Mid Lane")));
    PlanicieLocations.Add(MakeShared<FJsonValueString>(TEXT("Próximo à Bot Lane")));
    PlanicieLocations.Add(MakeShared<FJsonValueString>(TEXT("Centro da Jungle")));
    PlaniciePortals->SetArrayField(TEXT("locations"), PlanicieLocations);
    
    LayerPortals->SetObjectField(LAYER_PLANICIE_RADIANTE, PlaniciePortals);
    
    // Portais do Firmamento Zephyr
    TSharedPtr<FJsonObject> FirmamentoPortals = MakeShared<FJsonObject>();
    FirmamentoPortals->SetStringField(TEXT("layer_name"), LAYER_FIRMAMENTO_ZEPHYR);
    FirmamentoPortals->SetNumberField(TEXT("portal_count"), PortalsPerLayer);
    FirmamentoPortals->SetStringField(TEXT("portal_type"), TEXT("aerial"));
    
    TArray<TSharedPtr<FJsonValue>> FirmamentoConnections;
    FirmamentoConnections.Add(MakeShared<FJsonValueString>(LAYER_PLANICIE_RADIANTE));
    FirmamentoConnections.Add(MakeShared<FJsonValueString>(LAYER_ABISMO_UMBRAL));
    FirmamentoPortals->SetArrayField(TEXT("connections"), FirmamentoConnections);
    
    TArray<TSharedPtr<FJsonValue>> FirmamentoLocations;
    FirmamentoLocations.Add(MakeShared<FJsonValueString>(TEXT("Corrente de Vento Norte")));
    FirmamentoLocations.Add(MakeShared<FJsonValueString>(TEXT("Corrente de Vento Sul")));
    FirmamentoLocations.Add(MakeShared<FJsonValueString>(TEXT("Zona Central")));
    FirmamentoLocations.Add(MakeShared<FJsonValueString>(TEXT("Plataforma Móvel")));
    FirmamentoPortals->SetArrayField(TEXT("locations"), FirmamentoLocations);
    
    LayerPortals->SetObjectField(LAYER_FIRMAMENTO_ZEPHYR, FirmamentoPortals);
    
    // Portais do Abismo Umbral
    TSharedPtr<FJsonObject> AbismoPortals = MakeShared<FJsonObject>();
    AbismoPortals->SetStringField(TEXT("layer_name"), LAYER_ABISMO_UMBRAL);
    AbismoPortals->SetNumberField(TEXT("portal_count"), PortalsPerLayer);
    AbismoPortals->SetStringField(TEXT("portal_type"), TEXT("underground"));
    
    TArray<TSharedPtr<FJsonValue>> AbismoConnections;
    AbismoConnections.Add(MakeShared<FJsonValueString>(LAYER_PLANICIE_RADIANTE));
    AbismoConnections.Add(MakeShared<FJsonValueString>(LAYER_FIRMAMENTO_ZEPHYR));
    AbismoPortals->SetArrayField(TEXT("connections"), AbismoConnections);
    
    TArray<TSharedPtr<FJsonValue>> AbismoLocations;
    AbismoLocations.Add(MakeShared<FJsonValueString>(TEXT("Corredor Principal")));
    AbismoLocations.Add(MakeShared<FJsonValueString>(TEXT("Câmara Secreta")));
    AbismoLocations.Add(MakeShared<FJsonValueString>(TEXT("Zona de Sombras")));
    AbismoLocations.Add(MakeShared<FJsonValueString>(TEXT("Túnel Labiríntico")));
    AbismoPortals->SetArrayField(TEXT("locations"), AbismoLocations);
    
    LayerPortals->SetObjectField(LAYER_ABISMO_UMBRAL, AbismoPortals);
    
    SystemConfig->SetObjectField(TEXT("layer_portals"), LayerPortals);
    
    // Configurar mecânicas dos portais
    TSharedPtr<FJsonObject> PortalMechanics = MakeShared<FJsonObject>();
    PortalMechanics->SetNumberField(TEXT("activation_time"), 1.0f);
    PortalMechanics->SetNumberField(TEXT("travel_time"), TravelTimeSeconds);
    PortalMechanics->SetNumberField(TEXT("cooldown_time"), CooldownSeconds);
    PortalMechanics->SetBoolField(TEXT("interruptible"), true);
    PortalMechanics->SetBoolField(TEXT("visible_to_enemies"), true);
    
    TArray<TSharedPtr<FJsonValue>> StrategicUses;
    StrategicUses.Add(MakeShared<FJsonValueString>(TEXT("Escape Routes")));
    StrategicUses.Add(MakeShared<FJsonValueString>(TEXT("Flanking")));
    StrategicUses.Add(MakeShared<FJsonValueString>(TEXT("Objective Control")));
    StrategicUses.Add(MakeShared<FJsonValueString>(TEXT("Split Push")));
    PortalMechanics->SetArrayField(TEXT("strategic_uses"), StrategicUses);
    
    SystemConfig->SetObjectField(TEXT("portal_mechanics"), PortalMechanics);
    
    // Salvar configuração no cache
    NavigationConfigCache.Add(TEXT("primary_portals_system"), SystemConfig);
    
    // Criar estado do sistema
    TSharedPtr<FJsonObject> SystemState = MakeShared<FJsonObject>();
    SystemState->SetStringField(TEXT("status"), TEXT("created"));
    SystemState->SetStringField(TEXT("creation_time"), FDateTime::Now().ToString());
    SystemState->SetBoolField(TEXT("active"), true);
    SystemState->SetNumberField(TEXT("total_portals"), PortalsPerLayer * 3);
    NavigationStates.Add(TEXT("primary_portals_system"), SystemState);
    
    // Criar resposta de sucesso
    TSharedPtr<FJsonObject> ResponseData = MakeShared<FJsonObject>();
    ResponseData->SetObjectField(TEXT("system_config"), SystemConfig);
    ResponseData->SetObjectField(TEXT("system_state"), SystemState);
    ResponseData->SetStringField(TEXT("system_description"), TEXT("Sistema de portais primários para navegação rápida entre camadas"));
    
    UE_LOG(LogTemp, Log, TEXT("Sistema de portais primários criado com sucesso"));
    return CreateSuccessResponse(ResponseData, TEXT("Sistema de portais primários criado com sucesso"));
}

TSharedPtr<FJsonObject> FUnrealMCPVerticalNavigationCommands::HandleCreateMysticalElevatorsSystem(const TSharedPtr<FJsonObject>& CommandData)
{
    if (!CommandData.IsValid())
    {
        return CreateErrorResponse(TEXT("Dados do comando inválidos"), TEXT("INVALID_COMMAND_DATA"));
    }

    UE_LOG(LogTemp, Log, TEXT("Criando sistema de elevadores místicos"));

    // Extrair configurações dos elevadores
    int32 TotalElevators = 4;
    float TravelTimeSeconds = 5.0f;
    int32 MaxCapacity = 5;
    float ActivationTimeSeconds = 3.0f;
    bool bRequireTeamwork = true;

    // Ler configurações do JSON se fornecidas
    CommandData->TryGetNumberField(TEXT("total_elevators"), TotalElevators);
    CommandData->TryGetNumberField(TEXT("travel_time_seconds"), TravelTimeSeconds);
    CommandData->TryGetNumberField(TEXT("max_capacity"), MaxCapacity);
    CommandData->TryGetNumberField(TEXT("activation_time_seconds"), ActivationTimeSeconds);
    CommandData->TryGetBoolField(TEXT("require_teamwork"), bRequireTeamwork);

    // Criar configuração do sistema de elevadores
    TSharedPtr<FJsonObject> SystemConfig = MakeShared<FJsonObject>();
    SystemConfig->SetStringField(TEXT("system_name"), TEXT("mystical_elevators_system"));
    SystemConfig->SetNumberField(TEXT("total_elevators"), TotalElevators);
    SystemConfig->SetNumberField(TEXT("travel_time_seconds"), TravelTimeSeconds);
    SystemConfig->SetNumberField(TEXT("max_capacity"), MaxCapacity);
    SystemConfig->SetNumberField(TEXT("activation_time_seconds"), ActivationTimeSeconds);
    SystemConfig->SetBoolField(TEXT("require_teamwork"), bRequireTeamwork);

    // Configurar elevadores individuais
    TSharedPtr<FJsonObject> Elevators = MakeShared<FJsonObject>();

    // Elevador 1: Norte
    TSharedPtr<FJsonObject> ElevatorNorth = MakeShared<FJsonObject>();
    ElevatorNorth->SetStringField(TEXT("name"), TEXT("Elevador Místico Norte"));
    ElevatorNorth->SetStringField(TEXT("location"), TEXT("Região Norte do Mapa"));
    ElevatorNorth->SetStringField(TEXT("connection_type"), TEXT("Planície ↔ Firmamento"));
    ElevatorNorth->SetNumberField(TEXT("capacity"), MaxCapacity);
    ElevatorNorth->SetNumberField(TEXT("travel_time"), TravelTimeSeconds);
    ElevatorNorth->SetBoolField(TEXT("vulnerable_during_travel"), true);

    TArray<TSharedPtr<FJsonValue>> NorthStrategicUses;
    NorthStrategicUses.Add(MakeShared<FJsonValueString>(TEXT("Team Rotations")));
    NorthStrategicUses.Add(MakeShared<FJsonValueString>(TEXT("Objective Rushes")));
    ElevatorNorth->SetArrayField(TEXT("strategic_uses"), NorthStrategicUses);

    Elevators->SetObjectField(TEXT("elevator_north"), ElevatorNorth);

    // Elevador 2: Sul
    TSharedPtr<FJsonObject> ElevatorSouth = MakeShared<FJsonObject>();
    ElevatorSouth->SetStringField(TEXT("name"), TEXT("Elevador Místico Sul"));
    ElevatorSouth->SetStringField(TEXT("location"), TEXT("Região Sul do Mapa"));
    ElevatorSouth->SetStringField(TEXT("connection_type"), TEXT("Planície ↔ Abismo"));
    ElevatorSouth->SetNumberField(TEXT("capacity"), MaxCapacity);
    ElevatorSouth->SetNumberField(TEXT("travel_time"), TravelTimeSeconds);
    ElevatorSouth->SetBoolField(TEXT("vulnerable_during_travel"), true);

    TArray<TSharedPtr<FJsonValue>> SouthStrategicUses;
    SouthStrategicUses.Add(MakeShared<FJsonValueString>(TEXT("Defensive Retreats")));
    SouthStrategicUses.Add(MakeShared<FJsonValueString>(TEXT("Surprise Engages")));
    ElevatorSouth->SetArrayField(TEXT("strategic_uses"), SouthStrategicUses);

    Elevators->SetObjectField(TEXT("elevator_south"), ElevatorSouth);

    // Elevador 3: Leste
    TSharedPtr<FJsonObject> ElevatorEast = MakeShared<FJsonObject>();
    ElevatorEast->SetStringField(TEXT("name"), TEXT("Elevador Místico Leste"));
    ElevatorEast->SetStringField(TEXT("location"), TEXT("Região Leste do Mapa"));
    ElevatorEast->SetStringField(TEXT("connection_type"), TEXT("Firmamento ↔ Abismo"));
    ElevatorEast->SetNumberField(TEXT("capacity"), MaxCapacity);
    ElevatorEast->SetNumberField(TEXT("travel_time"), TravelTimeSeconds);
    ElevatorEast->SetBoolField(TEXT("vulnerable_during_travel"), true);

    TArray<TSharedPtr<FJsonValue>> EastStrategicUses;
    EastStrategicUses.Add(MakeShared<FJsonValueString>(TEXT("Cross-Layer Flanks")));
    EastStrategicUses.Add(MakeShared<FJsonValueString>(TEXT("Multi-Layer Pressure")));
    ElevatorEast->SetArrayField(TEXT("strategic_uses"), EastStrategicUses);

    Elevators->SetObjectField(TEXT("elevator_east"), ElevatorEast);

    // Elevador 4: Oeste
    TSharedPtr<FJsonObject> ElevatorWest = MakeShared<FJsonObject>();
    ElevatorWest->SetStringField(TEXT("name"), TEXT("Elevador Místico Oeste"));
    ElevatorWest->SetStringField(TEXT("location"), TEXT("Região Oeste do Mapa"));
    ElevatorWest->SetStringField(TEXT("connection_type"), TEXT("Tri-Layer Connection"));
    ElevatorWest->SetNumberField(TEXT("capacity"), MaxCapacity);
    ElevatorWest->SetNumberField(TEXT("travel_time"), TravelTimeSeconds * 1.5f);
    ElevatorWest->SetBoolField(TEXT("vulnerable_during_travel"), true);
    ElevatorWest->SetBoolField(TEXT("special_elevator"), true);

    TArray<TSharedPtr<FJsonValue>> WestStrategicUses;
    WestStrategicUses.Add(MakeShared<FJsonValueString>(TEXT("Ultimate Team Rotations")));
    WestStrategicUses.Add(MakeShared<FJsonValueString>(TEXT("Emergency Evacuations")));
    ElevatorWest->SetArrayField(TEXT("strategic_uses"), WestStrategicUses);

    Elevators->SetObjectField(TEXT("elevator_west"), ElevatorWest);

    SystemConfig->SetObjectField(TEXT("elevators"), Elevators);

    // Configurar mecânicas dos elevadores
    TSharedPtr<FJsonObject> ElevatorMechanics = MakeShared<FJsonObject>();
    ElevatorMechanics->SetNumberField(TEXT("activation_time"), ActivationTimeSeconds);
    ElevatorMechanics->SetNumberField(TEXT("travel_time"), TravelTimeSeconds);
    ElevatorMechanics->SetNumberField(TEXT("cooldown_after_use"), 60.0f);
    ElevatorMechanics->SetBoolField(TEXT("team_priority_system"), true);
    ElevatorMechanics->SetBoolField(TEXT("progress_bar_visible"), true);
    ElevatorMechanics->SetBoolField(TEXT("interruptible_by_damage"), true);

    SystemConfig->SetObjectField(TEXT("elevator_mechanics"), ElevatorMechanics);

    // Salvar configuração no cache
    NavigationConfigCache.Add(TEXT("mystical_elevators_system"), SystemConfig);

    // Criar estado do sistema
    TSharedPtr<FJsonObject> SystemState = MakeShared<FJsonObject>();
    SystemState->SetStringField(TEXT("status"), TEXT("created"));
    SystemState->SetStringField(TEXT("creation_time"), FDateTime::Now().ToString());
    SystemState->SetBoolField(TEXT("active"), true);
    SystemState->SetNumberField(TEXT("total_elevators"), TotalElevators);
    NavigationStates.Add(TEXT("mystical_elevators_system"), SystemState);

    // Criar resposta de sucesso
    TSharedPtr<FJsonObject> ResponseData = MakeShared<FJsonObject>();
    ResponseData->SetObjectField(TEXT("system_config"), SystemConfig);
    ResponseData->SetObjectField(TEXT("system_state"), SystemState);
    ResponseData->SetStringField(TEXT("system_description"), TEXT("Sistema de elevadores místicos para movimentação coordenada de equipe"));

    UE_LOG(LogTemp, Log, TEXT("Sistema de elevadores místicos criado com sucesso"));
    return CreateSuccessResponse(ResponseData, TEXT("Sistema de elevadores místicos criado com sucesso"));
}

TSharedPtr<FJsonObject> FUnrealMCPVerticalNavigationCommands::HandleCreateDimensionalBridgesSystem(const TSharedPtr<FJsonObject>& CommandData)
{
    if (!CommandData.IsValid())
    {
        return CreateErrorResponse(TEXT("Dados do comando inválidos"), TEXT("INVALID_COMMAND_DATA"));
    }

    UE_LOG(LogTemp, Log, TEXT("Criando sistema de pontes dimensionais"));

    // Extrair configurações das pontes
    bool bEnableObjectiveBridges = true;
    bool bEnableTemporaryBridges = true;
    float BridgeDurationMinutes = 2.0f;
    float TemporaryBridgeDurationSeconds = 90.0f;

    // Ler configurações do JSON se fornecidas
    CommandData->TryGetBoolField(TEXT("enable_objective_bridges"), bEnableObjectiveBridges);
    CommandData->TryGetBoolField(TEXT("enable_temporary_bridges"), bEnableTemporaryBridges);
    CommandData->TryGetNumberField(TEXT("bridge_duration_minutes"), BridgeDurationMinutes);
    CommandData->TryGetNumberField(TEXT("temporary_bridge_duration_seconds"), TemporaryBridgeDurationSeconds);

    // Criar configuração do sistema de pontes
    TSharedPtr<FJsonObject> SystemConfig = MakeShared<FJsonObject>();
    SystemConfig->SetStringField(TEXT("system_name"), TEXT("dimensional_bridges_system"));
    SystemConfig->SetBoolField(TEXT("enable_objective_bridges"), bEnableObjectiveBridges);
    SystemConfig->SetBoolField(TEXT("enable_temporary_bridges"), bEnableTemporaryBridges);
    SystemConfig->SetNumberField(TEXT("bridge_duration_minutes"), BridgeDurationMinutes);
    SystemConfig->SetNumberField(TEXT("temporary_bridge_duration_seconds"), TemporaryBridgeDurationSeconds);

    // Configurar pontes por objetivos
    TSharedPtr<FJsonObject> ObjectiveBridges = MakeShared<FJsonObject>();

    // Ponte do Guardião da Aurora
    TSharedPtr<FJsonObject> GuardianBridge = MakeShared<FJsonObject>();
    GuardianBridge->SetStringField(TEXT("name"), TEXT("Ponte do Guardião da Aurora"));
    GuardianBridge->SetStringField(TEXT("activation_objective"), TEXT("Guardião da Aurora"));
    GuardianBridge->SetStringField(TEXT("connection"), TEXT("Planície ↔ Firmamento"));
    GuardianBridge->SetNumberField(TEXT("duration_minutes"), BridgeDurationMinutes);
    GuardianBridge->SetBoolField(TEXT("bidirectional"), true);
    GuardianBridge->SetStringField(TEXT("bridge_type"), TEXT("light_energy"));

    ObjectiveBridges->SetObjectField(TEXT("guardian_bridge"), GuardianBridge);

    // Ponte do Senhor dos Ventos
    TSharedPtr<FJsonObject> WindLordBridge = MakeShared<FJsonObject>();
    WindLordBridge->SetStringField(TEXT("name"), TEXT("Ponte do Senhor dos Ventos"));
    WindLordBridge->SetStringField(TEXT("activation_objective"), TEXT("Senhor dos Ventos"));
    WindLordBridge->SetStringField(TEXT("connection"), TEXT("Firmamento ↔ Abismo"));
    WindLordBridge->SetNumberField(TEXT("duration_minutes"), BridgeDurationMinutes);
    WindLordBridge->SetBoolField(TEXT("bidirectional"), true);
    WindLordBridge->SetStringField(TEXT("bridge_type"), TEXT("wind_energy"));

    ObjectiveBridges->SetObjectField(TEXT("wind_lord_bridge"), WindLordBridge);

    // Ponte do Arqui-Sombra
    TSharedPtr<FJsonObject> ArchShadowBridge = MakeShared<FJsonObject>();
    ArchShadowBridge->SetStringField(TEXT("name"), TEXT("Ponte do Arqui-Sombra"));
    ArchShadowBridge->SetStringField(TEXT("activation_objective"), TEXT("Arqui-Sombra"));
    ArchShadowBridge->SetStringField(TEXT("connection"), TEXT("Abismo ↔ Planície"));
    ArchShadowBridge->SetNumberField(TEXT("duration_minutes"), BridgeDurationMinutes);
    ArchShadowBridge->SetBoolField(TEXT("bidirectional"), true);
    ArchShadowBridge->SetStringField(TEXT("bridge_type"), TEXT("shadow_energy"));

    ObjectiveBridges->SetObjectField(TEXT("arch_shadow_bridge"), ArchShadowBridge);

    SystemConfig->SetObjectField(TEXT("objective_bridges"), ObjectiveBridges);

    // Configurar pontes temporárias
    if (bEnableTemporaryBridges)
    {
        TSharedPtr<FJsonObject> TemporaryBridges = MakeShared<FJsonObject>();

        // Pontes de Cristais/Esferas/Fragmentos
        TSharedPtr<FJsonObject> ResourceBridges = MakeShared<FJsonObject>();
        ResourceBridges->SetStringField(TEXT("name"), TEXT("Pontes de Recursos"));
        ResourceBridges->SetNumberField(TEXT("duration_seconds"), TemporaryBridgeDurationSeconds);
        ResourceBridges->SetBoolField(TEXT("location_specific"), true);

        TArray<TSharedPtr<FJsonValue>> ActivationResources;
        ActivationResources.Add(MakeShared<FJsonValueString>(TEXT("Cristais de Luz")));
        ActivationResources.Add(MakeShared<FJsonValueString>(TEXT("Esferas de Vento")));
        ActivationResources.Add(MakeShared<FJsonValueString>(TEXT("Fragmentos de Sombra")));
        ResourceBridges->SetArrayField(TEXT("activation_resources"), ActivationResources);

        TemporaryBridges->SetObjectField(TEXT("resource_bridges"), ResourceBridges);
        SystemConfig->SetObjectField(TEXT("temporary_bridges"), TemporaryBridges);
    }

    // Configurar mecânicas das pontes
    TSharedPtr<FJsonObject> BridgeMechanics = MakeShared<FJsonObject>();
    BridgeMechanics->SetNumberField(TEXT("activation_delay"), 5.0f);
    BridgeMechanics->SetBoolField(TEXT("visible_to_all_players"), true);
    BridgeMechanics->SetBoolField(TEXT("destructible"), false);
    BridgeMechanics->SetBoolField(TEXT("provides_vision"), true);
    BridgeMechanics->SetNumberField(TEXT("movement_speed_bonus"), 0.1f);

    TArray<TSharedPtr<FJsonValue>> TacticalAdvantages;
    TacticalAdvantages.Add(MakeShared<FJsonValueString>(TEXT("Direct Layer Access")));
    TacticalAdvantages.Add(MakeShared<FJsonValueString>(TEXT("Bypass Portal Cooldowns")));
    TacticalAdvantages.Add(MakeShared<FJsonValueString>(TEXT("Team Coordination")));
    TacticalAdvantages.Add(MakeShared<FJsonValueString>(TEXT("Strategic Positioning")));
    BridgeMechanics->SetArrayField(TEXT("tactical_advantages"), TacticalAdvantages);

    SystemConfig->SetObjectField(TEXT("bridge_mechanics"), BridgeMechanics);

    // Salvar configuração no cache
    NavigationConfigCache.Add(TEXT("dimensional_bridges_system"), SystemConfig);

    // Criar estado do sistema
    TSharedPtr<FJsonObject> SystemState = MakeShared<FJsonObject>();
    SystemState->SetStringField(TEXT("status"), TEXT("created"));
    SystemState->SetStringField(TEXT("creation_time"), FDateTime::Now().ToString());
    SystemState->SetBoolField(TEXT("active"), true);
    SystemState->SetNumberField(TEXT("objective_bridges_count"), 3);
    NavigationStates.Add(TEXT("dimensional_bridges_system"), SystemState);

    // Criar resposta de sucesso
    TSharedPtr<FJsonObject> ResponseData = MakeShared<FJsonObject>();
    ResponseData->SetObjectField(TEXT("system_config"), SystemConfig);
    ResponseData->SetObjectField(TEXT("system_state"), SystemState);
    ResponseData->SetStringField(TEXT("system_description"), TEXT("Sistema de pontes dimensionais ativadas por objetivos"));

    UE_LOG(LogTemp, Log, TEXT("Sistema de pontes dimensionais criado com sucesso"));
    return CreateSuccessResponse(ResponseData, TEXT("Sistema de pontes dimensionais criado com sucesso"));
}

TSharedPtr<FJsonObject> FUnrealMCPVerticalNavigationCommands::HandleSetupNavigationStrategies(const TSharedPtr<FJsonObject>& CommandData)
{
    if (!CommandData.IsValid())
    {
        return CreateErrorResponse(TEXT("Dados do comando inválidos"), TEXT("INVALID_COMMAND_DATA"));
    }

    UE_LOG(LogTemp, Log, TEXT("Configurando estratégias de navegação"));

    // Configurações padrão
    bool bEnableEarlyGameStrategies = true;
    bool bEnableMidGameStrategies = true;
    bool bEnableLateGameStrategies = true;

    // Ler configurações do JSON se fornecidas
    CommandData->TryGetBoolField(TEXT("enable_early_game_strategies"), bEnableEarlyGameStrategies);
    CommandData->TryGetBoolField(TEXT("enable_mid_game_strategies"), bEnableMidGameStrategies);
    CommandData->TryGetBoolField(TEXT("enable_late_game_strategies"), bEnableLateGameStrategies);

    TSharedPtr<FJsonObject> SystemConfig = MakeShared<FJsonObject>();
    SystemConfig->SetStringField(TEXT("system_name"), TEXT("navigation_strategies"));

    TSharedPtr<FJsonObject> Strategies = MakeShared<FJsonObject>();

    // Early Game Strategies (0-15 min)
    if (bEnableEarlyGameStrategies)
    {
        TSharedPtr<FJsonObject> EarlyGame = MakeShared<FJsonObject>();
        EarlyGame->SetStringField(TEXT("phase_name"), TEXT("Early Game"));
        EarlyGame->SetNumberField(TEXT("start_time"), 0);
        EarlyGame->SetNumberField(TEXT("end_time"), 15);
        EarlyGame->SetStringField(TEXT("focus"), TEXT("Estabelecer controle na Planície Radiante"));

        TArray<TSharedPtr<FJsonValue>> EarlyStrategies;
        EarlyStrategies.Add(MakeShared<FJsonValueString>(TEXT("Transições limitadas, apenas para objetivos específicos")));
        EarlyStrategies.Add(MakeShared<FJsonValueString>(TEXT("Prioridade: Portais próximos às lanes para ganks")));
        EarlyStrategies.Add(MakeShared<FJsonValueString>(TEXT("Evitar elevadores (muito arriscado)")));
        EarlyStrategies.Add(MakeShared<FJsonValueString>(TEXT("Usar portais para escape emergencial")));
        EarlyGame->SetArrayField(TEXT("strategies"), EarlyStrategies);

        TArray<TSharedPtr<FJsonValue>> EarlyPriorities;
        EarlyPriorities.Add(MakeShared<FJsonValueString>(TEXT("Portal próximo à Top Lane")));
        EarlyPriorities.Add(MakeShared<FJsonValueString>(TEXT("Portal próximo à Bot Lane")));
        EarlyPriorities.Add(MakeShared<FJsonValueString>(TEXT("Portal central para Mid Lane")));
        EarlyGame->SetArrayField(TEXT("priority_locations"), EarlyPriorities);

        Strategies->SetObjectField(TEXT("early_game"), EarlyGame);
    }

    // Mid Game Strategies (15-30 min)
    if (bEnableMidGameStrategies)
    {
        TSharedPtr<FJsonObject> MidGame = MakeShared<FJsonObject>();
        MidGame->SetStringField(TEXT("phase_name"), TEXT("Mid Game"));
        MidGame->SetNumberField(TEXT("start_time"), 15);
        MidGame->SetNumberField(TEXT("end_time"), 30);
        MidGame->SetStringField(TEXT("focus"), TEXT("Controle de múltiplas camadas"));

        TArray<TSharedPtr<FJsonValue>> MidStrategies;
        MidStrategies.Add(MakeShared<FJsonValueString>(TEXT("Transições frequentes para controle de objetivos")));
        MidStrategies.Add(MakeShared<FJsonValueString>(TEXT("Prioridade: Elevadores para movimentação de equipe")));
        MidStrategies.Add(MakeShared<FJsonValueString>(TEXT("Usar pontes dimensionais quando disponíveis")));
        MidStrategies.Add(MakeShared<FJsonValueString>(TEXT("Coordenar rotações entre camadas")));
        MidGame->SetArrayField(TEXT("strategies"), MidStrategies);

        TArray<TSharedPtr<FJsonValue>> MidPriorities;
        MidPriorities.Add(MakeShared<FJsonValueString>(TEXT("Elevador Norte para teamfights")));
        MidPriorities.Add(MakeShared<FJsonValueString>(TEXT("Elevador Sul para objetivos épicos")));
        MidPriorities.Add(MakeShared<FJsonValueString>(TEXT("Portais para flanks rápidos")));
        MidGame->SetArrayField(TEXT("priority_locations"), MidPriorities);

        Strategies->SetObjectField(TEXT("mid_game"), MidGame);
    }

    // Late Game Strategies (30+ min)
    if (bEnableLateGameStrategies)
    {
        TSharedPtr<FJsonObject> LateGame = MakeShared<FJsonObject>();
        LateGame->SetStringField(TEXT("phase_name"), TEXT("Late Game"));
        LateGame->SetNumberField(TEXT("start_time"), 30);
        LateGame->SetStringField(TEXT("end_time"), TEXT("game_end"));
        LateGame->SetStringField(TEXT("focus"), TEXT("Controle total do mapa vertical"));

        TArray<TSharedPtr<FJsonValue>> LateStrategies;
        LateStrategies.Add(MakeShared<FJsonValueString>(TEXT("Transições constantes para pressão máxima")));
        LateStrategies.Add(MakeShared<FJsonValueString>(TEXT("Usar todos os sistemas de navegação")));
        LateStrategies.Add(MakeShared<FJsonValueString>(TEXT("Coordenação perfeita entre camadas")));
        LateStrategies.Add(MakeShared<FJsonValueString>(TEXT("Controle de todas as pontes dimensionais")));
        LateGame->SetArrayField(TEXT("strategies"), LateStrategies);

        TArray<TSharedPtr<FJsonValue>> LatePriorities;
        LatePriorities.Add(MakeShared<FJsonValueString>(TEXT("Elevador Oeste para rotações ultimates")));
        LatePriorities.Add(MakeShared<FJsonValueString>(TEXT("Todas as pontes dimensionais ativas")));
        LatePriorities.Add(MakeShared<FJsonValueString>(TEXT("Controle total dos portais")));
        LateGame->SetArrayField(TEXT("priority_locations"), LatePriorities);

        Strategies->SetObjectField(TEXT("late_game"), LateGame);
    }

    SystemConfig->SetObjectField(TEXT("strategies"), Strategies);

    // Configurar rotação estratégica
    TSharedPtr<FJsonObject> StrategicRotation = MakeShared<FJsonObject>();
    StrategicRotation->SetStringField(TEXT("system_name"), TEXT("strategic_rotation"));

    TArray<TSharedPtr<FJsonValue>> RotationPrinciples;
    RotationPrinciples.Add(MakeShared<FJsonValueString>(TEXT("Sempre manter controle de pelo menos uma camada")));
    RotationPrinciples.Add(MakeShared<FJsonValueString>(TEXT("Usar navegação vertical para flanks inesperados")));
    RotationPrinciples.Add(MakeShared<FJsonValueString>(TEXT("Coordenar transições com objetivos neutros")));
    RotationPrinciples.Add(MakeShared<FJsonValueString>(TEXT("Priorizar segurança da equipe durante transições")));
    StrategicRotation->SetArrayField(TEXT("rotation_principles"), RotationPrinciples);

    SystemConfig->SetObjectField(TEXT("strategic_rotation"), StrategicRotation);

    // Criar resposta de sucesso
    TSharedPtr<FJsonObject> ResponseData = MakeShared<FJsonObject>();
    ResponseData->SetObjectField(TEXT("system_config"), SystemConfig);
    ResponseData->SetStringField(TEXT("system_description"), TEXT("Estratégias de navegação vertical por fase da partida"));

    UE_LOG(LogTemp, Log, TEXT("Estratégias de navegação configuradas com sucesso"));
    return CreateSuccessResponse(ResponseData, TEXT("Estratégias de navegação configuradas com sucesso"));
}

// ========================================================================
// Funções Auxiliares
// ========================================================================

TSharedPtr<FJsonObject> FUnrealMCPVerticalNavigationCommands::CreateErrorResponse(const FString& ErrorMessage, const FString& ErrorCode)
{
    TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
    Response->SetStringField(TEXT("status"), RESPONSE_ERROR);
    Response->SetStringField(TEXT("message"), ErrorMessage);
    Response->SetStringField(TEXT("error_code"), ErrorCode);
    Response->SetStringField(TEXT("timestamp"), FDateTime::Now().ToString());

    UE_LOG(LogTemp, Error, TEXT("VerticalNavigation Error [%s]: %s"), *ErrorCode, *ErrorMessage);
    return Response;
}

TSharedPtr<FJsonObject> FUnrealMCPVerticalNavigationCommands::CreateSuccessResponse(const TSharedPtr<FJsonObject>& Data, const FString& Message)
{
    TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
    Response->SetStringField(TEXT("status"), RESPONSE_SUCCESS);
    Response->SetStringField(TEXT("message"), Message);
    Response->SetObjectField(TEXT("data"), Data);
    Response->SetStringField(TEXT("timestamp"), FDateTime::Now().ToString());

    return Response;
}

bool FUnrealMCPVerticalNavigationCommands::ValidateNavigationConfig(const TSharedPtr<FJsonObject>& NavigationConfig, FString& ErrorMessage)
{
    if (!NavigationConfig.IsValid())
    {
        ErrorMessage = TEXT("Configuração de navegação inválida");
        return false;
    }

    // Validações básicas podem ser adicionadas aqui
    return true;
}
