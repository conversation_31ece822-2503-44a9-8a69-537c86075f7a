# Script para abrir o projeto Auracron no Unreal Engine
# Autor: Gerado automaticamente
# Data: $(Get-Date -Format 'yyyy-MM-dd')

param(
    [switch]$Editor,
    [switch]$Build,
    [switch]$Clean
)

# Configurações do projeto
$ProjectPath = "C:\Game\Auracron"
$ProjectFile = "$ProjectPath\Auracron.uproject"
$UnrealEnginePath = "C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor.exe"

# Verificar se o arquivo do projeto existe
if (-not (Test-Path $ProjectFile)) {
    Write-Error "Arquivo do projeto não encontrado: $ProjectFile"
    exit 1
}

# Verificar se o Unreal Engine está instalado
if (-not (Test-Path $UnrealEnginePath)) {
    Write-Error "Unreal Engine não encontrado em: $UnrealEnginePath"
    Write-Host "Procurando por outras versões do Unreal Engine..."
    
    $PossiblePaths = @(
        "C:\Program Files\Epic Games\UE_5.5\Engine\Binaries\Win64\UnrealEditor.exe",
        "C:\Program Files\Epic Games\UE_5.4\Engine\Binaries\Win64\UnrealEditor.exe",
        "C:\Program Files\Epic Games\UE_5.3\Engine\Binaries\Win64\UnrealEditor.exe"
    )
    
    foreach ($Path in $PossiblePaths) {
        if (Test-Path $Path) {
            $UnrealEnginePath = $Path
            Write-Host "Encontrado Unreal Engine em: $UnrealEnginePath" -ForegroundColor Green
            break
        }
    }
    
    if (-not (Test-Path $UnrealEnginePath)) {
        Write-Error "Nenhuma instalação do Unreal Engine foi encontrada."
        exit 1
    }
}

# Função para limpar arquivos temporários
function Clean-Project {
    Write-Host "Limpando arquivos temporários do projeto..." -ForegroundColor Yellow
    
    $FoldersToClean = @(
        "$ProjectPath\Binaries",
        "$ProjectPath\Intermediate",
        "$ProjectPath\Saved\Logs",
        "$ProjectPath\Saved\Config\CrashReportClient"
    )
    
    foreach ($Folder in $FoldersToClean) {
        if (Test-Path $Folder) {
            try {
                Remove-Item $Folder -Recurse -Force -ErrorAction Stop
                Write-Host "Removido: $Folder" -ForegroundColor Green
            }
            catch {
                Write-Warning "Não foi possível remover: $Folder - $($_.Exception.Message)"
            }
        }
    }
    
    Write-Host "Limpeza concluída!" -ForegroundColor Green
}

# Função para compilar o projeto
function Build-Project {
    Write-Host "Compilando o projeto..." -ForegroundColor Yellow
    
    $BuildArgs = @(
        $ProjectFile,
        "-compile"
    )
    
    try {
        & $UnrealEnginePath @BuildArgs
        Write-Host "Compilação concluída!" -ForegroundColor Green
    }
    catch {
        Write-Error "Erro durante a compilação: $($_.Exception.Message)"
        exit 1
    }
}

# Função para abrir o editor
function Open-Editor {
    Write-Host "Abrindo o Unreal Engine Editor..." -ForegroundColor Green
    Write-Host "Projeto: $ProjectFile" -ForegroundColor Cyan
    Write-Host "Engine: $UnrealEnginePath" -ForegroundColor Cyan
    
    try {
        Start-Process -FilePath $UnrealEnginePath -ArgumentList $ProjectFile -WorkingDirectory $ProjectPath
        Write-Host "Editor iniciado com sucesso!" -ForegroundColor Green
    }
    catch {
        Write-Error "Erro ao abrir o editor: $($_.Exception.Message)"
        exit 1
    }
}

# Executar ações baseadas nos parâmetros
Write-Host "=== Script de Abertura do Projeto Auracron ===" -ForegroundColor Magenta
Write-Host "Diretório do projeto: $ProjectPath" -ForegroundColor Cyan

if ($Clean) {
    Clean-Project
}

if ($Build) {
    Build-Project
}

if ($Editor -or (-not $Clean -and -not $Build)) {
    Open-Editor
}

Write-Host "\n=== Script concluído ===" -ForegroundColor Magenta
Write-Host "\nUso do script:"
Write-Host "  .\OpenProject.ps1                 # Abre o editor"
Write-Host "  .\OpenProject.ps1 -Editor         # Abre o editor (explícito)"
Write-Host "  .\OpenProject.ps1 -Build          # Compila o projeto"
Write-Host "  .\OpenProject.ps1 -Clean          # Limpa arquivos temporários"
Write-Host "  .\OpenProject.ps1 -Clean -Build   # Limpa e compila"
Write-Host "  .\OpenProject.ps1 -Clean -Editor  # Limpa e abre o editor"