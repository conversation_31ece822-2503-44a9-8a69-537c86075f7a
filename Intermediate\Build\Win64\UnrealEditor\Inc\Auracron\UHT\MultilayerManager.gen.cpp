// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "MultilayerManager.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeMultilayerManager() {}

// ********** Begin Cross Module References ********************************************************
AURACRON_API UClass* Z_Construct_UClass_AMultilayerManager();
AURACRON_API UClass* Z_Construct_UClass_AMultilayerManager_NoRegister();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_AActor();
ENGINE_API UClass* Z_Construct_UClass_USceneComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UStaticMeshComponent_NoRegister();
UPackage* Z_Construct_UPackage__Script_Auracron();
// ********** End Cross Module References **********************************************************

// ********** Begin Class AMultilayerManager Function GetLayerPosition *****************************
struct Z_Construct_UFunction_AMultilayerManager_GetLayerPosition_Statics
{
	struct MultilayerManager_eventGetLayerPosition_Parms
	{
		FString LayerName;
		FVector ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Auracron|Layers" },
		{ "ModuleRelativePath", "Public/MultilayerManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LayerName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LayerName;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_AMultilayerManager_GetLayerPosition_Statics::NewProp_LayerName = { "LayerName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MultilayerManager_eventGetLayerPosition_Parms, LayerName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LayerName_MetaData), NewProp_LayerName_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AMultilayerManager_GetLayerPosition_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MultilayerManager_eventGetLayerPosition_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AMultilayerManager_GetLayerPosition_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMultilayerManager_GetLayerPosition_Statics::NewProp_LayerName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMultilayerManager_GetLayerPosition_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AMultilayerManager_GetLayerPosition_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMultilayerManager_GetLayerPosition_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AMultilayerManager, nullptr, "GetLayerPosition", Z_Construct_UFunction_AMultilayerManager_GetLayerPosition_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AMultilayerManager_GetLayerPosition_Statics::PropPointers), sizeof(Z_Construct_UFunction_AMultilayerManager_GetLayerPosition_Statics::MultilayerManager_eventGetLayerPosition_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54820401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMultilayerManager_GetLayerPosition_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMultilayerManager_GetLayerPosition_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AMultilayerManager_GetLayerPosition_Statics::MultilayerManager_eventGetLayerPosition_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AMultilayerManager_GetLayerPosition()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMultilayerManager_GetLayerPosition_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMultilayerManager::execGetLayerPosition)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LayerName);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector*)Z_Param__Result=P_THIS->GetLayerPosition(Z_Param_LayerName);
	P_NATIVE_END;
}
// ********** End Class AMultilayerManager Function GetLayerPosition *******************************

// ********** Begin Class AMultilayerManager Function InitializeLayers *****************************
struct Z_Construct_UFunction_AMultilayerManager_InitializeLayers_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Auracron|Layers" },
		{ "ModuleRelativePath", "Public/MultilayerManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMultilayerManager_InitializeLayers_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AMultilayerManager, nullptr, "InitializeLayers", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMultilayerManager_InitializeLayers_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMultilayerManager_InitializeLayers_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AMultilayerManager_InitializeLayers()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMultilayerManager_InitializeLayers_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMultilayerManager::execInitializeLayers)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->InitializeLayers();
	P_NATIVE_END;
}
// ********** End Class AMultilayerManager Function InitializeLayers *******************************

// ********** Begin Class AMultilayerManager Function SetLayerVisibility ***************************
struct Z_Construct_UFunction_AMultilayerManager_SetLayerVisibility_Statics
{
	struct MultilayerManager_eventSetLayerVisibility_Parms
	{
		FString LayerName;
		bool bVisible;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Auracron|Layers" },
		{ "ModuleRelativePath", "Public/MultilayerManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LayerName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LayerName;
	static void NewProp_bVisible_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bVisible;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_AMultilayerManager_SetLayerVisibility_Statics::NewProp_LayerName = { "LayerName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MultilayerManager_eventSetLayerVisibility_Parms, LayerName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LayerName_MetaData), NewProp_LayerName_MetaData) };
void Z_Construct_UFunction_AMultilayerManager_SetLayerVisibility_Statics::NewProp_bVisible_SetBit(void* Obj)
{
	((MultilayerManager_eventSetLayerVisibility_Parms*)Obj)->bVisible = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AMultilayerManager_SetLayerVisibility_Statics::NewProp_bVisible = { "bVisible", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(MultilayerManager_eventSetLayerVisibility_Parms), &Z_Construct_UFunction_AMultilayerManager_SetLayerVisibility_Statics::NewProp_bVisible_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AMultilayerManager_SetLayerVisibility_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMultilayerManager_SetLayerVisibility_Statics::NewProp_LayerName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMultilayerManager_SetLayerVisibility_Statics::NewProp_bVisible,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AMultilayerManager_SetLayerVisibility_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMultilayerManager_SetLayerVisibility_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AMultilayerManager, nullptr, "SetLayerVisibility", Z_Construct_UFunction_AMultilayerManager_SetLayerVisibility_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AMultilayerManager_SetLayerVisibility_Statics::PropPointers), sizeof(Z_Construct_UFunction_AMultilayerManager_SetLayerVisibility_Statics::MultilayerManager_eventSetLayerVisibility_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMultilayerManager_SetLayerVisibility_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMultilayerManager_SetLayerVisibility_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AMultilayerManager_SetLayerVisibility_Statics::MultilayerManager_eventSetLayerVisibility_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AMultilayerManager_SetLayerVisibility()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMultilayerManager_SetLayerVisibility_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMultilayerManager::execSetLayerVisibility)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LayerName);
	P_GET_UBOOL(Z_Param_bVisible);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetLayerVisibility(Z_Param_LayerName,Z_Param_bVisible);
	P_NATIVE_END;
}
// ********** End Class AMultilayerManager Function SetLayerVisibility *****************************

// ********** Begin Class AMultilayerManager *******************************************************
void AMultilayerManager::StaticRegisterNativesAMultilayerManager()
{
	UClass* Class = AMultilayerManager::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "GetLayerPosition", &AMultilayerManager::execGetLayerPosition },
		{ "InitializeLayers", &AMultilayerManager::execInitializeLayers },
		{ "SetLayerVisibility", &AMultilayerManager::execSetLayerVisibility },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_AMultilayerManager;
UClass* AMultilayerManager::GetPrivateStaticClass()
{
	using TClass = AMultilayerManager;
	if (!Z_Registration_Info_UClass_AMultilayerManager.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("MultilayerManager"),
			Z_Registration_Info_UClass_AMultilayerManager.InnerSingleton,
			StaticRegisterNativesAMultilayerManager,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_AMultilayerManager.InnerSingleton;
}
UClass* Z_Construct_UClass_AMultilayerManager_NoRegister()
{
	return AMultilayerManager::GetPrivateStaticClass();
}
struct Z_Construct_UClass_AMultilayerManager_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "IncludePath", "MultilayerManager.h" },
		{ "ModuleRelativePath", "Public/MultilayerManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RootSceneComponent_MetaData[] = {
		{ "Category", "Components" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Componentes das Camadas\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/MultilayerManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componentes das Camadas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlanicieFloorMesh_MetaData[] = {
		{ "Category", "Plan\xc3\xad""cie Radiante" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/MultilayerManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FirmamentoPlatformMesh_MetaData[] = {
		{ "Category", "Firmamento Zephyr" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/MultilayerManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AbismoCeilingMesh_MetaData[] = {
		{ "Category", "Abismo Umbral" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/MultilayerManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlanicieSize_MetaData[] = {
		{ "Category", "Auracron|Layers" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configura\xc3\xa7\xc3\xb5""es das Camadas\n" },
#endif
		{ "ModuleRelativePath", "Public/MultilayerManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xb5""es das Camadas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FirmamentoSize_MetaData[] = {
		{ "Category", "Auracron|Layers" },
		{ "ModuleRelativePath", "Public/MultilayerManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AbismoSize_MetaData[] = {
		{ "Category", "Auracron|Layers" },
		{ "ModuleRelativePath", "Public/MultilayerManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_RootSceneComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PlanicieFloorMesh;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_FirmamentoPlatformMesh;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_AbismoCeilingMesh;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PlanicieSize;
	static const UECodeGen_Private::FStructPropertyParams NewProp_FirmamentoSize;
	static const UECodeGen_Private::FStructPropertyParams NewProp_AbismoSize;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_AMultilayerManager_GetLayerPosition, "GetLayerPosition" }, // 999133062
		{ &Z_Construct_UFunction_AMultilayerManager_InitializeLayers, "InitializeLayers" }, // 3882281448
		{ &Z_Construct_UFunction_AMultilayerManager_SetLayerVisibility, "SetLayerVisibility" }, // 4074013120
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<AMultilayerManager>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AMultilayerManager_Statics::NewProp_RootSceneComponent = { "RootSceneComponent", nullptr, (EPropertyFlags)0x00200800000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AMultilayerManager, RootSceneComponent), Z_Construct_UClass_USceneComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RootSceneComponent_MetaData), NewProp_RootSceneComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AMultilayerManager_Statics::NewProp_PlanicieFloorMesh = { "PlanicieFloorMesh", nullptr, (EPropertyFlags)0x00200800000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AMultilayerManager, PlanicieFloorMesh), Z_Construct_UClass_UStaticMeshComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlanicieFloorMesh_MetaData), NewProp_PlanicieFloorMesh_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AMultilayerManager_Statics::NewProp_FirmamentoPlatformMesh = { "FirmamentoPlatformMesh", nullptr, (EPropertyFlags)0x00200800000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AMultilayerManager, FirmamentoPlatformMesh), Z_Construct_UClass_UStaticMeshComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FirmamentoPlatformMesh_MetaData), NewProp_FirmamentoPlatformMesh_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AMultilayerManager_Statics::NewProp_AbismoCeilingMesh = { "AbismoCeilingMesh", nullptr, (EPropertyFlags)0x00200800000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AMultilayerManager, AbismoCeilingMesh), Z_Construct_UClass_UStaticMeshComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AbismoCeilingMesh_MetaData), NewProp_AbismoCeilingMesh_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AMultilayerManager_Statics::NewProp_PlanicieSize = { "PlanicieSize", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AMultilayerManager, PlanicieSize), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlanicieSize_MetaData), NewProp_PlanicieSize_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AMultilayerManager_Statics::NewProp_FirmamentoSize = { "FirmamentoSize", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AMultilayerManager, FirmamentoSize), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FirmamentoSize_MetaData), NewProp_FirmamentoSize_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AMultilayerManager_Statics::NewProp_AbismoSize = { "AbismoSize", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AMultilayerManager, AbismoSize), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AbismoSize_MetaData), NewProp_AbismoSize_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_AMultilayerManager_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMultilayerManager_Statics::NewProp_RootSceneComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMultilayerManager_Statics::NewProp_PlanicieFloorMesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMultilayerManager_Statics::NewProp_FirmamentoPlatformMesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMultilayerManager_Statics::NewProp_AbismoCeilingMesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMultilayerManager_Statics::NewProp_PlanicieSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMultilayerManager_Statics::NewProp_FirmamentoSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMultilayerManager_Statics::NewProp_AbismoSize,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AMultilayerManager_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_AMultilayerManager_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_AActor,
	(UObject* (*)())Z_Construct_UPackage__Script_Auracron,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AMultilayerManager_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_AMultilayerManager_Statics::ClassParams = {
	&AMultilayerManager::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_AMultilayerManager_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_AMultilayerManager_Statics::PropPointers),
	0,
	0x009001A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_AMultilayerManager_Statics::Class_MetaDataParams), Z_Construct_UClass_AMultilayerManager_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_AMultilayerManager()
{
	if (!Z_Registration_Info_UClass_AMultilayerManager.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_AMultilayerManager.OuterSingleton, Z_Construct_UClass_AMultilayerManager_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_AMultilayerManager.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(AMultilayerManager);
AMultilayerManager::~AMultilayerManager() {}
// ********** End Class AMultilayerManager *********************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Game_Auracron_Source_Auracron_Public_MultilayerManager_h__Script_Auracron_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_AMultilayerManager, AMultilayerManager::StaticClass, TEXT("AMultilayerManager"), &Z_Registration_Info_UClass_AMultilayerManager, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(AMultilayerManager), 771713189U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Game_Auracron_Source_Auracron_Public_MultilayerManager_h__Script_Auracron_1517524935(TEXT("/Script/Auracron"),
	Z_CompiledInDeferFile_FID_Game_Auracron_Source_Auracron_Public_MultilayerManager_h__Script_Auracron_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Game_Auracron_Source_Auracron_Public_MultilayerManager_h__Script_Auracron_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
