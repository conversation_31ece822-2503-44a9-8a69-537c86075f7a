"""Multilayer Map Tools for Unreal MCP.

This module provides tools for creating and managing the three-layer map system
of AURACRON with specific mechanics for each layer.
"""

import logging
from typing import Dict, List, Any, Optional
from mcp.server.fastmcp import FastMCP, Context

# Get logger
logger = logging.getLogger("UnrealMCP")

def register_multilayer_map_tools(mcp: FastMCP):
    """Register Multilayer Map tools with the MCP server."""
    
    @mcp.tool()
    def create_planicie_radiante_layer(
        ctx: Context,
        layer_config: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Create the Planície Radiante (Radiant Plains) layer - Lower layer (Z: 0-2000).
        
        Args:
            ctx: MCP Context
            layer_config: Layer configuration as JSON object containing:
                - enable_light_crystals: Enable light crystals that accelerate minion growth (default: True)
                - solar_tower_enhancement: Enable solar energy tower enhancement (default: True)
                - regeneration_buffs: Enable regeneration and sustain buffs (default: True)
                - jungle_camps_count: Number of neutral jungle camps (default: 24)
                - lane_layout: Lane configuration (default: {"top": True, "mid": True, "bot": True})
        
        Returns:
            Dict[str, Any]: Operation result
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            # Default configuration for Planície Radiante
            default_config = {
                "layer_name": "planicie_radiante",
                "z_range": {"min": 0, "max": 2000},
                "enable_light_crystals": True,
                "solar_tower_enhancement": True,
                "regeneration_buffs": True,
                "jungle_camps_count": 24,
                "lane_layout": {"top": True, "mid": True, "bot": True},
                "special_mechanics": [
                    "accelerated_minion_growth_near_crystals",
                    "solar_enhanced_towers",
                    "regeneration_sustain_buffs"
                ]
            }
            
            # Merge provided config with defaults
            if layer_config:
                default_config.update(layer_config)
            
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            logger.info(f"Creating Planície Radiante layer with config: {default_config}")
            response = unreal.send_command("create_planicie_radiante_layer", default_config)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Planície Radiante layer creation response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error creating Planície Radiante layer: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def create_firmamento_zephyr_layer(
        ctx: Context,
        layer_config: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Create the Firmamento Zephyr (Zephyr Firmament) layer - Middle layer (Z: 2000-4000).
        
        Args:
            ctx: MCP Context
            layer_config: Layer configuration as JSON object containing:
                - wind_currents_count: Number of wind currents (default: 2)
                - central_zone: Enable central zone (default: True)
                - mobile_platforms: Enable mobile platforms that change position every 3 minutes (default: True)
                - wind_speed_boost: Movement speed boost from wind currents (default: 0.3)
                - platform_change_interval: Platform position change interval in seconds (default: 180)
                - enable_vortices: Enable vortices for rapid movement (default: True)
        
        Returns:
            Dict[str, Any]: Operation result
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            # Default configuration for Firmamento Zephyr
            default_config = {
                "layer_name": "firmamento_zephyr",
                "z_range": {"min": 2000, "max": 4000},
                "wind_currents_count": 2,
                "central_zone": True,
                "mobile_platforms": True,
                "wind_speed_boost": 0.3,
                "platform_change_interval": 180,
                "enable_vortices": True,
                "special_mechanics": [
                    "dynamic_layout_wind_currents",
                    "mobile_platforms_3min_cycle",
                    "wind_acceleration_30_percent",
                    "floating_energy_bridges",
                    "rapid_movement_vortices"
                ]
            }
            
            # Merge provided config with defaults
            if layer_config:
                default_config.update(layer_config)
            
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            logger.info(f"Creating Firmamento Zephyr layer with config: {default_config}")
            response = unreal.send_command("create_firmamento_zephyr_layer", default_config)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Firmamento Zephyr layer creation response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error creating Firmamento Zephyr layer: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def create_abismo_umbral_layer(
        ctx: Context,
        layer_config: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Create the Abismo Umbral (Umbral Abyss) layer - Upper layer (Z: 4000-6000).
        
        Args:
            ctx: MCP Context
            layer_config: Layer configuration as JSON object containing:
                - labyrinthine_layout: Enable complex labyrinthine layout (default: True)
                - shadow_zones: Enable shadow zones with reduced visibility (default: True)
                - visibility_reduction: Visibility reduction percentage in shadow zones (default: 0.5)
                - shadow_fog: Enable shadow fog that conceals movements (default: True)
                - proximity_traps: Enable environmental traps activated by proximity (default: True)
                - secret_chambers: Enable secret chambers with special rewards (default: True)
        
        Returns:
            Dict[str, Any]: Operation result
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            # Default configuration for Abismo Umbral
            default_config = {
                "layer_name": "abismo_umbral",
                "z_range": {"min": 4000, "max": 6000},
                "labyrinthine_layout": True,
                "shadow_zones": True,
                "visibility_reduction": 0.5,
                "shadow_fog": True,
                "proximity_traps": True,
                "secret_chambers": True,
                "special_mechanics": [
                    "labyrinthine_corridors_chambers",
                    "shadow_zones_50_percent_visibility",
                    "concealing_shadow_fog",
                    "proximity_environmental_traps",
                    "secret_reward_chambers"
                ]
            }
            
            # Merge provided config with defaults
            if layer_config:
                default_config.update(layer_config)
            
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            logger.info(f"Creating Abismo Umbral layer with config: {default_config}")
            response = unreal.send_command("create_abismo_umbral_layer", default_config)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Abismo Umbral layer creation response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error creating Abismo Umbral layer: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def setup_complete_multilayer_system(
        ctx: Context,
        system_config: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Setup the complete three-layer system with all interconnections.
        
        Args:
            ctx: MCP Context
            system_config: System configuration as JSON object containing:
                - enable_all_layers: Enable all three layers (default: True)
                - auto_connect_layers: Automatically create connections between layers (default: True)
                - planicie_config: Configuration for Planície Radiante layer
                - firmamento_config: Configuration for Firmamento Zephyr layer
                - abismo_config: Configuration for Abismo Umbral layer
        
        Returns:
            Dict[str, Any]: Operation result
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            # Default system configuration
            default_config = {
                "enable_all_layers": True,
                "auto_connect_layers": True,
                "planicie_config": {},
                "firmamento_config": {},
                "abismo_config": {}
            }
            
            # Merge provided config with defaults
            if system_config:
                default_config.update(system_config)
            
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            logger.info(f"Setting up complete multilayer system with config: {default_config}")
            response = unreal.send_command("setup_complete_multilayer_system", default_config)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Complete multilayer system setup response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error setting up complete multilayer system: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
