"""
Realm Transition Tools for Unreal MCP.

This module provides tools for creating and managing realm transition systems in Unreal Engine.
"""

import logging
from typing import Dict, List, Any, Optional
from mcp.server.fastmcp import FastMCP, Context

# Get logger
logger = logging.getLogger("UnrealMCP")



def register_realm_transition_tools(mcp: FastMCP):
    """Register Realm Transition tools with the MCP server."""
    
    @mcp.tool()
    def create_realm_transition_system(
        ctx: Context,
        layer_name: str,
        realm_configs: List[Dict[str, Any]],
        transition_configs: List[Dict[str, Any]],
        global_settings: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        Create a multilayer realm transition system.
        
        Args:
            layer_name: Name of the system layer
            realm_configs: List of realm configurations
            transition_configs: List of transition configurations
            global_settings: Global system settings
        
        Returns:
            Information about the created system
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            # Validate required parameters
            if not layer_name or not isinstance(layer_name, str):
                logger.error("Invalid layer_name: must be a non-empty string")
                return {"success": False, "message": "Invalid layer_name: must be a non-empty string"}
            
            if not isinstance(realm_configs, list) or len(realm_configs) == 0:
                logger.error("Invalid realm_configs: must be a non-empty list")
                return {"success": False, "message": "Invalid realm_configs: must be a non-empty list"}
            
            if not isinstance(transition_configs, list) or len(transition_configs) == 0:
                logger.error("Invalid transition_configs: must be a non-empty list")
                return {"success": False, "message": "Invalid transition_configs: must be a non-empty list"}
            
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            params = {
                "layer_name": layer_name,
                "realm_configs": realm_configs,
                "transition_configs": transition_configs,
                "global_settings": global_settings or {}
            }
            
            logger.info(f"Creating realm transition system with params: {params}")
            response = unreal.send_command("create_realm_transition_system", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Realm transition system creation response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error creating realm transition system: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def configure_asset_streaming(
        ctx: Context,
        layer_name: str,
        streaming_strategy: str,
        asset_priorities: Dict[str, float],
        bandwidth_limits: Dict[str, float]
    ) -> Dict[str, Any]:
        """
        Configure asset streaming system for realm transitions.
        
        Args:
            layer_name: Name of the layer
            streaming_strategy: Streaming strategy (preload_all, lazy_loading, predictive, distance_based, priority_based)
            asset_priorities: Asset priorities mapping (asset_path -> priority_value)
            bandwidth_limits: Bandwidth limits (max_bandwidth_mbps, max_concurrent_loads, timeout_seconds)
        
        Returns:
            Streaming configuration information
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            # Validate required parameters
            if not layer_name or not isinstance(layer_name, str):
                logger.error("Invalid layer_name: must be a non-empty string")
                return {"success": False, "message": "Invalid layer_name: must be a non-empty string"}
            
            if not streaming_strategy or not isinstance(streaming_strategy, str):
                logger.error("Invalid streaming_strategy: must be a non-empty string")
                return {"success": False, "message": "Invalid streaming_strategy: must be a non-empty string"}
            
            # Validate streaming strategy against C++ constants
            valid_strategies = ["preload_all", "lazy_loading", "predictive", "distance_based", "priority_based"]
            if streaming_strategy not in valid_strategies:
                logger.error(f"Invalid streaming_strategy: must be one of {valid_strategies}")
                return {"success": False, "message": f"Invalid streaming_strategy: must be one of {valid_strategies}"}
            
            if not isinstance(asset_priorities, dict):
                logger.error("Invalid asset_priorities: must be a dictionary")
                return {"success": False, "message": "Invalid asset_priorities: must be a dictionary"}
            
            if not isinstance(bandwidth_limits, dict):
                logger.error("Invalid bandwidth_limits: must be a dictionary")
                return {"success": False, "message": "Invalid bandwidth_limits: must be a dictionary"}
            
            # Validate required bandwidth_limits fields
            required_bandwidth_fields = ["max_bandwidth_mbps", "max_concurrent_loads", "timeout_seconds"]
            for field in required_bandwidth_fields:
                if field not in bandwidth_limits:
                    logger.error(f"Missing required bandwidth_limits field: {field}")
                    return {"success": False, "message": f"Missing required bandwidth_limits field: {field}"}
            
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            params = {
                "layer_name": layer_name,
                "strategy": streaming_strategy,
                "asset_priorities": asset_priorities,
                "bandwidth_limits": bandwidth_limits
            }
            
            logger.info(f"Configuring asset streaming with params: {params}")
            response = unreal.send_command("configure_asset_streaming", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Asset streaming configuration response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error configuring asset streaming: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def setup_world_partitioning(
        ctx: Context,
        layer_name: str,
        method: str,
        partition_size: List[float],
        overlap_distance: float = 1000.0
    ) -> Dict[str, Any]:
        """
        Set up world partitioning system for realms.
        
        Args:
            layer_name: Name of the layer
            method: Partitioning method (grid_based, hierarchical, adaptive, content_aware, performance_based)
            partition_size: Partition size as [X, Y, Z]
            overlap_distance: Overlap distance (default: 1000.0)
        
        Returns:
            World partitioning configuration information
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            # Validate required parameters
            if not layer_name or not isinstance(layer_name, str):
                logger.error("Invalid layer_name: must be a non-empty string")
                return {"success": False, "message": "Invalid layer_name: must be a non-empty string"}
            
            if not method or not isinstance(method, str):
                logger.error("Invalid method: must be a non-empty string")
                return {"success": False, "message": "Invalid method: must be a non-empty string"}
            
            # Validate method against C++ constants
            valid_methods = ["grid_based", "hierarchical", "adaptive", "content_aware", "performance_based"]
            if method not in valid_methods:
                logger.error(f"Invalid method: must be one of {valid_methods}")
                return {"success": False, "message": f"Invalid method: must be one of {valid_methods}"}
            
            # Validate partition_size format (similar to blueprint_tools.py validation)
            if not isinstance(partition_size, list) or len(partition_size) != 3:
                logger.error(f"Invalid partition_size format: {partition_size}. Must be a list of 3 float values.")
                return {"success": False, "message": "Invalid partition_size format. Must be a list of 3 float values."}
            
            # Ensure all values are float
            try:
                partition_size = [float(val) for val in partition_size]
            except (ValueError, TypeError):
                logger.error(f"Invalid partition_size values: {partition_size}. All values must be numeric.")
                return {"success": False, "message": "Invalid partition_size values. All values must be numeric."}
            
            # Validate that partition sizes are positive
            if any(size <= 0 for size in partition_size):
                logger.error(f"Invalid partition_size values: {partition_size}. All values must be positive.")
                return {"success": False, "message": "Invalid partition_size values. All values must be positive."}
            
            # Validate overlap_distance
            try:
                overlap_distance = float(overlap_distance)
            except (ValueError, TypeError):
                logger.error(f"Invalid overlap_distance: {overlap_distance}. Must be a numeric value.")
                return {"success": False, "message": "Invalid overlap_distance. Must be a numeric value."}
            
            if overlap_distance < 0:
                logger.error(f"Invalid overlap_distance: {overlap_distance}. Must be non-negative.")
                return {"success": False, "message": "Invalid overlap_distance. Must be non-negative."}
            
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            params = {
                "layer_name": layer_name,
                "method": method,
                "partition_size": partition_size,
                "overlap_distance": overlap_distance
            }
            
            logger.info(f"Setting up world partitioning with params: {params}")
            response = unreal.send_command("setup_world_partitioning", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"World partitioning setup response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error setting up world partitioning: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def create_transition_triggers(
        ctx: Context,
        layer_name: str,
        trigger_configs: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        Create triggers for transitions between realms.
        
        Args:
            layer_name: Name of the layer
            trigger_configs: Trigger configurations
        
        Returns:
            Information about created triggers
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            # Validate required parameters
            if not layer_name or not isinstance(layer_name, str):
                logger.error("Invalid layer_name: must be a non-empty string")
                return {"success": False, "message": "Invalid layer_name: must be a non-empty string"}
            
            if not isinstance(trigger_configs, list) or len(trigger_configs) == 0:
                logger.error("Invalid trigger_configs: must be a non-empty list")
                return {"success": False, "message": "Invalid trigger_configs: must be a non-empty list"}
            
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            params = {
                "layer_name": layer_name,
                "trigger_configs": trigger_configs
            }
            
            logger.info(f"Creating transition triggers with params: {params}")
            response = unreal.send_command("create_transition_triggers", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Transition triggers creation response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error creating transition triggers: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def configure_realm_persistence(
        ctx: Context,
        layer_name: str,
        persistence_settings: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Configure realm state persistence.
        
        Args:
            layer_name: Name of the layer
            persistence_settings: Persistence settings (object with persistence configuration)
        
        Returns:
            Persistence configuration information
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            # Validate required parameters
            if not layer_name or not isinstance(layer_name, str):
                logger.error("Invalid layer_name: must be a non-empty string")
                return {"success": False, "message": "Invalid layer_name: must be a non-empty string"}
            
            if not isinstance(persistence_settings, dict):
                logger.error("Invalid persistence_settings: must be a dictionary")
                return {"success": False, "message": "Invalid persistence_settings: must be a dictionary"}
            
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            params = {
                "layer_name": layer_name,
                "persistence_settings": persistence_settings
            }
            
            logger.info(f"Configuring realm persistence with params: {params}")
            response = unreal.send_command("configure_realm_persistence", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Realm persistence configuration response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error configuring realm persistence: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def setup_cross_realm_communication(
        ctx: Context,
        layer_name: str,
        channels: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        Set up communication between realms.
        
        Args:
            layer_name: Name of the layer
            channels: Communication channels
        
        Returns:
            Communication configuration information
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            # Validate required parameters
            if not layer_name or not isinstance(layer_name, str):
                logger.error("Invalid layer_name: must be a non-empty string")
                return {"success": False, "message": "Invalid layer_name: must be a non-empty string"}
            
            if not isinstance(channels, list) or len(channels) == 0:
                logger.error("Invalid channels: must be a non-empty list")
                return {"success": False, "message": "Invalid channels: must be a non-empty list"}
            
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            params = {
                "layer_name": layer_name,
                "channels": channels
            }
            
            logger.info(f"Setting up cross-realm communication with params: {params}")
            response = unreal.send_command("setup_cross_realm_communication", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Cross-realm communication setup response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error setting up cross-realm communication: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def optimize_transition_performance(
        ctx: Context,
        layer_name: str,
        optimization_settings: Dict[str, Any],
        performance_targets: Dict[str, float]
    ) -> Dict[str, Any]:
        """
        Optimize performance of transitions between realms.
        
        Args:
            layer_name: Name of the layer
            optimization_settings: Optimization settings
            performance_targets: Performance targets
        
        Returns:
            Optimization results information
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            # Validate required parameters
            if not layer_name or not isinstance(layer_name, str):
                logger.error("Invalid layer_name: must be a non-empty string")
                return {"success": False, "message": "Invalid layer_name: must be a non-empty string"}
            
            if not isinstance(optimization_settings, dict):
                logger.error("Invalid optimization_settings: must be a dictionary")
                return {"success": False, "message": "Invalid optimization_settings: must be a dictionary"}
            
            if not isinstance(performance_targets, dict):
                logger.error("Invalid performance_targets: must be a dictionary")
                return {"success": False, "message": "Invalid performance_targets: must be a dictionary"}
            
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            params = {
                "layer_name": layer_name,
                "optimization_settings": optimization_settings,
                "performance_targets": performance_targets
            }
            
            logger.info(f"Optimizing transition performance with params: {params}")
            response = unreal.send_command("optimize_transition_performance", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Performance optimization response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error optimizing transition performance: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def debug_realm_transitions(
        ctx: Context,
        layer_name: str,
        debug_level: str = "info",
        capture_metrics: bool = True
    ) -> Dict[str, Any]:
        """
        Enable debugging for realm transitions.
        
        Args:
            layer_name: Name of the layer
            debug_level: Debug level (info, warning, error)
            capture_metrics: Whether to capture metrics
        
        Returns:
            Debug information
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            # Validate required parameters
            if not layer_name or not isinstance(layer_name, str):
                logger.error("Invalid layer_name: must be a non-empty string")
                return {"success": False, "message": "Invalid layer_name: must be a non-empty string"}
            
            if debug_level not in ["info", "warning", "error"]:
                logger.error(f"Invalid debug_level: {debug_level}. Must be 'info', 'warning', or 'error'")
                return {"success": False, "message": "Invalid debug_level. Must be 'info', 'warning', or 'error'"}
            
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            params = {
                "layer_name": layer_name,
                "debug_level": debug_level,
                "capture_metrics": capture_metrics
            }
            
            logger.info(f"Starting realm transition debugging with params: {params}")
            response = unreal.send_command("debug_realm_transitions", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Debug realm transitions response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error debugging realm transitions: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def validate_realm_setup(
        ctx: Context,
        layer_name: str,
        validation_rules: List[str],
        strict_mode: bool = False
    ) -> Dict[str, Any]:
        """
        Validate realm configuration.
        
        Args:
            layer_name: Name of the layer
            validation_rules: Validation rules
            strict_mode: Strict validation mode
        
        Returns:
            Validation results information
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            # Validate required parameters
            if not layer_name or not isinstance(layer_name, str):
                logger.error("Invalid layer_name: must be a non-empty string")
                return {"success": False, "message": "Invalid layer_name: must be a non-empty string"}
            
            if not isinstance(validation_rules, list) or len(validation_rules) == 0:
                logger.error("Invalid validation_rules: must be a non-empty list")
                return {"success": False, "message": "Invalid validation_rules: must be a non-empty list"}
            
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            params = {
                "layer_name": layer_name,
                "validation_rules": validation_rules,
                "strict_mode": strict_mode
            }
            
            logger.info(f"Validating realm setup with params: {params}")
            response = unreal.send_command("validate_realm_setup", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Realm validation response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error validating realm setup: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def start_analytics_collection(
        ctx: Context,
        layer_name: str,
        metrics_config: Dict[str, Any],
        collection_interval: float = 1.0
    ) -> Dict[str, Any]:
        """
        Start analytics collection for realm transitions.
        
        Args:
            layer_name: Name of the layer
            metrics_config: Metrics configuration
            collection_interval: Collection interval in seconds
        
        Returns:
            Collection status information
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            # Validate required parameters
            if not layer_name or not isinstance(layer_name, str):
                logger.error("Invalid layer_name: must be a non-empty string")
                return {"success": False, "message": "Invalid layer_name: must be a non-empty string"}
            
            if not isinstance(metrics_config, dict):
                logger.error("Invalid metrics_config: must be a dictionary")
                return {"success": False, "message": "Invalid metrics_config: must be a dictionary"}
            
            try:
                collection_interval = float(collection_interval)
                if collection_interval <= 0:
                    logger.error(f"Invalid collection_interval: {collection_interval}. Must be a positive number")
                    return {"success": False, "message": "Invalid collection_interval. Must be a positive number"}
            except (ValueError, TypeError):
                logger.error(f"Invalid collection_interval: {collection_interval}. Must be a numeric value")
                return {"success": False, "message": "Invalid collection_interval. Must be a numeric value"}
            
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            params = {
                "layer_name": layer_name,
                "metrics_config": metrics_config,
                "collection_interval": collection_interval
            }
            
            logger.info(f"Starting analytics collection with params: {params}")
            response = unreal.send_command("start_analytics_collection", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Analytics collection start response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error starting analytics collection: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def stop_analytics_collection(
        ctx: Context,
        layer_name: str
    ) -> Dict[str, Any]:
        """
        Stop analytics collection for realm transitions.
        
        Args:
            layer_name: Name of the layer
        
        Returns:
            Collection stop status information
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            # Validate required parameters
            if not layer_name or not isinstance(layer_name, str):
                logger.error("Invalid layer_name: must be a non-empty string")
                return {"success": False, "message": "Invalid layer_name: must be a non-empty string"}
            
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            params = {
                "layer_name": layer_name
            }
            
            logger.info(f"Stopping analytics collection with params: {params}")
            response = unreal.send_command("stop_analytics_collection", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Analytics collection stop response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error stopping analytics collection: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def process_analytics_data(
        ctx: Context,
        layer_name: str,
        data_filters: Dict[str, Any],
        processing_options: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Process collected analytics data.
        
        Args:
            layer_name: Name of the layer
            data_filters: Data filters
            processing_options: Processing options
        
        Returns:
            Processed data information
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            # Validate required parameters
            if not layer_name or not isinstance(layer_name, str):
                logger.error("Invalid layer_name: must be a non-empty string")
                return {"success": False, "message": "Invalid layer_name: must be a non-empty string"}
            
            if not isinstance(data_filters, dict):
                logger.error("Invalid data_filters: must be a dictionary")
                return {"success": False, "message": "Invalid data_filters: must be a dictionary"}
            
            if not isinstance(processing_options, dict):
                logger.error("Invalid processing_options: must be a dictionary")
                return {"success": False, "message": "Invalid processing_options: must be a dictionary"}
            
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            params = {
                "layer_name": layer_name,
                "data_filters": data_filters,
                "processing_options": processing_options
            }
            
            logger.info(f"Processing analytics data with params: {params}")
            response = unreal.send_command("process_analytics_data", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Analytics data processing response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error processing analytics data: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def generate_analytics_visualization(
        ctx: Context,
        layer_name: str,
        visualization_type: str,
        data_range: Dict[str, Any],
        export_format: str = "json"
    ) -> Dict[str, Any]:
        """
        Generate analytics data visualizations.
        
        Args:
            layer_name: Name of the layer
            visualization_type: Visualization type
            data_range: Data range
            export_format: Export format
        
        Returns:
            Visualization data information
        """
        from unreal_mcp_server import get_unreal_connection
        
        # Validate required parameters
        if not layer_name or not isinstance(layer_name, str):
            raise ValueError("layer_name must be a non-empty string")
        if not visualization_type or not isinstance(visualization_type, str):
            raise ValueError("visualization_type must be a non-empty string")
        if not isinstance(data_range, dict):
            raise ValueError("data_range must be a dictionary")
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            params = {
                "layer_name": layer_name,
                "visualization_type": visualization_type,
                "data_range": data_range,
                "export_format": export_format
            }
            
            logger.info(f"Generating analytics visualization with params: {params}")
            response = unreal.send_command("generate_analytics_visualization", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Analytics visualization response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error generating analytics visualization: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def setup_performance_alerts(
        ctx: Context,
        layer_name: str,
        alert_thresholds: Dict[str, float],
        notification_settings: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Setup performance alerts for realm transitions.
        
        Args:
            layer_name: Name of the layer
            alert_thresholds: Alert thresholds
            notification_settings: Notification settings
        
        Returns:
            Alert configuration information
        """
        from unreal_mcp_server import get_unreal_connection
        
        # Validate required parameters
        if not layer_name or not isinstance(layer_name, str):
            raise ValueError("layer_name must be a non-empty string")
        if not isinstance(alert_thresholds, dict):
            raise ValueError("alert_thresholds must be a dictionary")
        if not isinstance(notification_settings, dict):
            raise ValueError("notification_settings must be a dictionary")
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            params = {
                "layer_name": layer_name,
                "alert_thresholds": alert_thresholds,
                "notification_settings": notification_settings
            }
            
            logger.info(f"Setting up performance alerts with params: {params}")
            response = unreal.send_command("setup_performance_alerts", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Performance alerts setup response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error setting up performance alerts: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    # export_analytics_report moved to analytics_telemetry_tools.py to avoid duplication
    
    @mcp.tool()
    def get_realm_system_status(
        ctx: Context,
        layer_name: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Get the current status of the realm system.
        
        Args:
            layer_name: Optional layer name to get specific layer status
        
        Returns:
            System status information
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            params = {}
            if layer_name:
                params["layer_name"] = layer_name
            
            logger.info(f"Getting realm system status with params: {params}")
            response = unreal.send_command("get_realm_system_status", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Realm system status response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error getting realm system status: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    logger.info("Realm transition tools registered successfully")