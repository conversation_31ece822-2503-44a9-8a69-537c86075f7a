// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "Dom/JsonObject.h"

/**
 * Classe responsável por gerenciar comandos de Fases da Partida.
 * 
 * Esta classe fornece funcionalidades para:
 * - <PERSON><PERSON><PERSON> Early Game (0-15min) com foco em laning
 * - Criar Mid Game (15-30min) com teamfights e objetivos
 * - Criar Late Game (30+min) com decisões finais
 * - Configurar marcos temporais e transições dinâmicas
 */
class UNREALMCP_API FUnrealMCPGamePhasesCommands
{
public:
    FUnrealMCPGamePhasesCommands();
    ~FUnrealMCPGamePhasesCommands();

    // Método principal para processar comandos
    TSharedPtr<FJsonObject> HandleCommand(const FString& CommandType, const TSharedPtr<FJsonObject>& Params);
    
    // ========================================================================
    // Métodos Principais de Fases da Partida
    // ========================================================================

    /**
     * Cria a fase Early Game (0-15 minutos).
     */
    TSharedPtr<FJsonObject> HandleCreateEarlyGamePhase(const TSharedPtr<FJsonObject>& CommandData);

    /**
     * Cria a fase Mid Game (15-30 minutos).
     */
    TSharedPtr<FJsonObject> HandleCreateMidGamePhase(const TSharedPtr<FJsonObject>& CommandData);

    /**
     * Cria a fase Late Game (30+ minutos).
     */
    TSharedPtr<FJsonObject> HandleCreateLateGamePhase(const TSharedPtr<FJsonObject>& CommandData);

    /**
     * Configura o sistema completo de fases da partida.
     */
    TSharedPtr<FJsonObject> HandleSetupCompleteGamePhasesSystem(const TSharedPtr<FJsonObject>& CommandData);

    // ========================================================================
    // Constantes Públicas
    // ========================================================================

    // Tipos de resposta
    static const FString RESPONSE_SUCCESS;
    static const FString RESPONSE_ERROR;
    static const FString RESPONSE_WARNING;
    static const FString RESPONSE_INFO;

private:
    // ========================================================================
    // Funções Auxiliares
    // ========================================================================

    /**
     * Cria resposta de erro.
     */
    TSharedPtr<FJsonObject> CreateErrorResponse(const FString& ErrorMessage, const FString& ErrorCode = TEXT("GAME_PHASES_ERROR"));

    /**
     * Cria resposta de sucesso.
     */
    TSharedPtr<FJsonObject> CreateSuccessResponse(const TSharedPtr<FJsonObject>& Data, const FString& Message = TEXT("Operation completed successfully"));

    // ========================================================================
    // Variáveis Privadas
    // ========================================================================

    /** Cache de configurações de fases */
    TMap<FString, TSharedPtr<FJsonObject>> PhaseConfigCache;

    /** Estado atual das fases */
    TMap<FString, TSharedPtr<FJsonObject>> PhaseStates;

    /** Flag para indicar se o sistema está inicializado */
    bool bIsInitialized;

    /** Timestamp da última atualização */
    FDateTime LastUpdateTime;
};
