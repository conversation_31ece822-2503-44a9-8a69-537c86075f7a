#pragma once

// Core Unreal Engine includes
#include "CoreMinimal.h"
#include "Engine/Engine.h"
#include "Engine/World.h"
#include "UObject/UObjectGlobals.h"
#include "UObject/Package.h"
#include "UObject/ConstructorHelpers.h"

// Editor includes for UE 5.6
#include "EditorAssetLibrary.h"
#include "AssetRegistry/AssetRegistryModule.h"
#include "AssetToolsModule.h"
#include "IAssetTools.h"
#include "ContentBrowserModule.h"
#include "IContentBrowserSingleton.h"

// Blueprint includes
#include "Engine/Blueprint.h"
#include "Engine/BlueprintGeneratedClass.h"
#include "Factories/BlueprintFactory.h"
#include "Kismet2/KismetEditorUtilities.h"
#include "Kismet2/BlueprintEditorUtils.h"
#include "BlueprintGraph/Classes/K2Node_Event.h"
#include "BlueprintGraph/Classes/K2Node_CallFunction.h"
#include "BlueprintGraph/Classes/K2Node_VariableGet.h"
#include "BlueprintGraph/Classes/K2Node_VariableSet.h"
#include "EdGraphSchema_K2.h"
#include "Engine/SimpleConstructionScript.h"
#include "Engine/SCS_Node.h"

// Material includes
#include "Materials/Material.h"
#include "Materials/MaterialInstance.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "Materials/MaterialInstanceConstant.h"
#include "Factories/MaterialFactory.h"
#include "Factories/MaterialInstanceConstantFactory.h"
#include "MaterialEditor/Public/MaterialEditorModule.h"

// Static Mesh includes
#include "Engine/StaticMesh.h"
#include "Engine/StaticMeshActor.h"
#include "Components/StaticMeshComponent.h"
#include "Factories/StaticMeshFactory.h"
#include "StaticMeshEditorModule.h"

// Niagara includes
#include "NiagaraSystem.h"
#include "NiagaraEmitter.h"
#include "NiagaraComponent.h"
#include "Factories/NiagaraSystemFactory.h"
#include "Factories/NiagaraEmitterFactory.h"
#include "NiagaraEditorModule.h"

// Behavior Tree includes
#include "BehaviorTree/BehaviorTree.h"
#include "BehaviorTree/BlackboardData.h"
#include "BehaviorTree/BlackboardComponent.h"
#include "Factories/BehaviorTreeFactory.h"
#include "Factories/BlackboardDataFactory.h"
#include "BehaviorTreeEditorModule.h"

// Navigation includes
#include "NavigationSystem.h"
#include "NavMesh/RecastNavMesh.h"
#include "NavMesh/NavMeshBoundsVolume.h"
#include "AI/NavigationSystemBase.h"
#include "NavigationData.h"

// Audio includes
#include "Sound/SoundCue.h"
#include "Sound/SoundWave.h"
#include "MetasoundSource.h"
#include "Factories/SoundCueFactory.h"
#include "Factories/SoundFactory.h"
#include "AudioEditorModule.h"

// UMG includes
#include "Blueprint/UserWidget.h"
#include "Components/Widget.h"
#include "Components/TextBlock.h"
#include "Components/Button.h"
#include "Components/CanvasPanel.h"
#include "Components/CanvasPanelSlot.h"
#include "WidgetBlueprint.h"
#include "Factories/WidgetBlueprintFactory.h"
#include "UMGEditorModule.h"

// Chaos Physics includes
#include "GeometryCollection/GeometryCollectionActor.h"
#include "GeometryCollection/GeometryCollectionComponent.h"
#include "GeometryCollection/GeometryCollection.h"
#include "Factories/GeometryCollectionFactory.h"
#include "ChaosEditorModule.h"

// World Partition includes
#include "WorldPartition/WorldPartition.h"
#include "WorldPartition/WorldPartitionSubsystem.h"
#include "WorldPartition/DataLayer/DataLayerSubsystem.h"
#include "WorldPartition/DataLayer/DataLayerAsset.h"

// Rendering includes
#include "Engine/PostProcessVolume.h"
#include "Components/PostProcessComponent.h"
#include "Engine/DirectionalLight.h"
#include "Components/DirectionalLightComponent.h"
#include "Engine/PointLight.h"
#include "Components/PointLightComponent.h"
#include "Engine/SpotLight.h"
#include "Components/SpotLightComponent.h"

// Component includes
#include "Components/BoxComponent.h"
#include "Components/SphereComponent.h"
#include "Components/CapsuleComponent.h"
#include "Components/MeshComponent.h"
#include "Components/PrimitiveComponent.h"
#include "Components/SceneComponent.h"

// Actor includes
#include "GameFramework/Actor.h"
#include "GameFramework/Pawn.h"
#include "GameFramework/Character.h"
#include "GameFramework/PlayerController.h"
#include "GameFramework/GameModeBase.h"

// Collision includes
#include "Engine/CollisionProfile.h"
#include "CollisionQueryParams.h"
#include "PhysicsEngine/BodySetup.h"

// Logging
#include "Logging/LogMacros.h"

// JSON includes
#include "Dom/JsonObject.h"
#include "Dom/JsonValue.h"
#include "Serialization/JsonSerializer.h"
#include "Serialization/JsonWriter.h"

// Utility includes
#include "Misc/DateTime.h"
#include "Misc/Guid.h"
#include "HAL/PlatformFilemanager.h"
#include "GenericPlatform/GenericPlatformFile.h"

// Editor Utility includes
#include "EditorUtilityLibrary.h"
#include "EditorLevelLibrary.h"
#include "EditorActorSubsystem.h"
#include "Subsystems/EditorAssetSubsystem.h"

// Texture includes
#include "Engine/Texture2D.h"
#include "Engine/TextureRenderTarget2D.h"
#include "Factories/TextureFactory.h"

// Animation includes
#include "Animation/AnimBlueprint.h"
#include "Animation/AnimSequence.h"
#include "Factories/AnimBlueprintFactory.h"
#include "Factories/AnimSequenceFactory.h"

// Level Sequence includes
#include "LevelSequence.h"
#include "Factories/LevelSequenceFactory.h"

// Data Asset includes
#include "Engine/DataAsset.h"
#include "Factories/DataAssetFactory.h"

// Curve includes
#include "Curves/CurveFloat.h"
#include "Curves/CurveVector.h"
#include "Factories/CurveFactory.h"

// Particle System includes (Legacy)
#include "Particles/ParticleSystem.h"
#include "Factories/ParticleSystemFactory.h"

// Input includes
#include "GameFramework/InputSettings.h"
#include "InputCoreTypes.h"

// Platform includes
#include "HAL/Platform.h"
#include "Misc/EngineVersionComparison.h"

// Memory includes
#include "HAL/UnrealMemory.h"
#include "Templates/SharedPointer.h"

// Threading includes
#include "HAL/Runnable.h"
#include "HAL/RunnableThread.h"
#include "Async/Async.h"

// File System includes
#include "HAL/FileManager.h"
#include "Misc/FileHelper.h"
#include "Misc/Paths.h"

// String includes
#include "Containers/UnrealString.h"
#include "Internationalization/Text.h"

// Math includes
#include "Math/UnrealMathUtility.h"
#include "Math/Vector.h"
#include "Math/Rotator.h"
#include "Math/Transform.h"
#include "Math/Color.h"

// Container includes
#include "Containers/Array.h"
#include "Containers/Map.h"
#include "Containers/Set.h"

// Delegate includes
#include "Delegates/Delegate.h"
#include "Delegates/MulticastDelegate.h"

// Timer includes
#include "Engine/TimerManager.h"

// Gameplay includes
#include "GameFramework/GameplayStatics.h"
#include "Kismet/KismetSystemLibrary.h"
#include "Kismet/KismetMathLibrary.h"

// Network includes
#include "Engine/NetConnection.h"
#include "Engine/NetDriver.h"

// AI includes
#include "AIController.h"
#include "BrainComponent.h"
#include "Perception/AIPerceptionComponent.h"

// Physics includes
#include "PhysicsEngine/PhysicsSettings.h"
#include "PhysicsEngine/BodyInstance.h"

// Landscape includes
#include "Landscape.h"
#include "LandscapeComponent.h"
#include "Factories/LandscapeFactory.h"

// Foliage includes
#include "FoliageType.h"
#include "InstancedFoliageActor.h"

// Spline includes
#include "Components/SplineComponent.h"
#include "Components/SplineMeshComponent.h"

// Widget Component includes
#include "Components/WidgetComponent.h"

// Camera includes
#include "Camera/CameraComponent.h"
#include "Camera/CameraActor.h"

// Light includes
#include "Engine/Light.h"
#include "Components/LightComponent.h"

// Volume includes
#include "Engine/TriggerVolume.h"
#include "Engine/BlockingVolume.h"

// Decal includes
#include "Engine/DecalActor.h"
#include "Components/DecalComponent.h"

// Reflection includes
#include "Engine/ReflectionCapture.h"
#include "Components/ReflectionCaptureComponent.h"

// Sky includes
#include "Engine/SkyLight.h"
#include "Components/SkyLightComponent.h"
#include "Engine/AtmosphericFog.h"

// Water includes (if available in UE 5.6)
#if ENGINE_MAJOR_VERSION >= 5
#include "WaterBodyActor.h"
#include "WaterSplineComponent.h"
#endif

// PCG includes (if available in UE 5.6)
#if ENGINE_MAJOR_VERSION >= 5
#include "PCGComponent.h"
#include "PCGGraph.h"
#endif

// Lumen includes (UE5 specific)
#if ENGINE_MAJOR_VERSION >= 5
#include "Engine/RendererSettings.h"
#endif

// Nanite includes (UE5 specific)
#if ENGINE_MAJOR_VERSION >= 5
#include "Engine/StaticMeshSourceData.h"
#endif

// World Partition includes (UE5 specific)
#if ENGINE_MAJOR_VERSION >= 5
#include "WorldPartition/WorldPartitionEditorModule.h"
#endif

// Chaos includes (UE5 specific)
#if ENGINE_MAJOR_VERSION >= 5
#include "Chaos/ChaosEngineInterface.h"
#endif
