#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "Components/StaticMeshComponent.h"
#include "Components/SceneComponent.h"
#include "MultilayerManager.generated.h"

UCLASS()
class AURACRON_API AMultilayerManager : public AActor
{
    GENERATED_BODY()
    
public:    
    AMultilayerManager();

protected:
    virtual void BeginPlay() override;

    // Componentes das Camadas
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    USceneComponent* RootSceneComponent;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Planície Radiante")
    UStaticMeshComponent* PlanicieFloorMesh;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Firmamento Zephyr")
    UStaticMeshComponent* FirmamentoPlatformMesh;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Abismo Umbral")
    UStaticMeshComponent* AbismoCeilingMesh;

    // Configurações das Camadas
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Auracron|Layers")
    FVector PlanicieSize = FVector(5000, 5000, 100);

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Auracron|Layers")
    FVector FirmamentoSize = FVector(4000, 4000, 100);

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Auracron|Layers")
    FVector AbismoSize = FVector(3000, 3000, 100);

public:    
    virtual void Tick(float DeltaTime) override;

    UFUNCTION(BlueprintCallable, Category = "Auracron|Layers")
    void InitializeLayers();

    UFUNCTION(BlueprintCallable, Category = "Auracron|Layers")
    void SetLayerVisibility(const FString& LayerName, bool bVisible);

    UFUNCTION(BlueprintPure, Category = "Auracron|Layers")
    FVector GetLayerPosition(const FString& LayerName) const;
};
