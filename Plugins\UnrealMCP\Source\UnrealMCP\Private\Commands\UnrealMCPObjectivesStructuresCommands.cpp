// Copyright Epic Games, Inc. All Rights Reserved.

#include "Commands/UnrealMCPObjectivesStructuresCommands.h"

// ========================================================================
// Constantes
// ========================================================================

const FString FUnrealMCPObjectivesStructuresCommands::RESPONSE_SUCCESS = TEXT("success");
const FString FUnrealMCPObjectivesStructuresCommands::RESPONSE_ERROR = TEXT("error");
const FString FUnrealMCPObjectivesStructuresCommands::RESPONSE_WARNING = TEXT("warning");
const FString FUnrealMCPObjectivesStructuresCommands::RESPONSE_INFO = TEXT("info");

// ========================================================================
// Construtor e Destrutor
// ========================================================================

FUnrealMCPObjectivesStructuresCommands::FUnrealMCPObjectivesStructuresCommands()
    : bIsInitialized(false)
    , LastUpdateTime(FDateTime::Now())
{
    bIsInitialized = true;
    UE_LOG(LogTemp, Log, TEXT("FUnrealMCPObjectivesStructuresCommands: Sistema de Objetivos e Estruturas inicializado"));
}

FUnrealMCPObjectivesStructuresCommands::~FUnrealMCPObjectivesStructuresCommands()
{
    ObjectiveConfigCache.Empty();
    ObjectiveStates.Empty();
    UE_LOG(LogTemp, Log, TEXT("FUnrealMCPObjectivesStructuresCommands: Sistema de Objetivos e Estruturas finalizado"));
}

// ========================================================================
// Método Principal de Comando
// ========================================================================

TSharedPtr<FJsonObject> FUnrealMCPObjectivesStructuresCommands::HandleCommand(const FString& CommandType, const TSharedPtr<FJsonObject>& Params)
{
    if (CommandType == TEXT("create_primordial_dragons_system"))
    {
        return HandleCreatePrimordialDragonsSystem(Params);
    }
    else if (CommandType == TEXT("create_baron_auracron_system"))
    {
        return HandleCreateBaronAuracronSystem(Params);
    }
    else if (CommandType == TEXT("create_tower_system"))
    {
        return HandleCreateTowerSystem(Params);
    }
    else if (CommandType == TEXT("create_inhibitor_system"))
    {
        return HandleCreateInhibitorSystem(Params);
    }
    else
    {
        return CreateErrorResponse(FString::Printf(TEXT("Comando não reconhecido: %s"), *CommandType), TEXT("UNKNOWN_COMMAND"));
    }
}

// ========================================================================
// Implementações dos Comandos
// ========================================================================

TSharedPtr<FJsonObject> FUnrealMCPObjectivesStructuresCommands::HandleCreatePrimordialDragonsSystem(const TSharedPtr<FJsonObject>& CommandData)
{
    if (!CommandData.IsValid())
    {
        return CreateErrorResponse(TEXT("Dados do comando inválidos"), TEXT("INVALID_COMMAND_DATA"));
    }
    
    UE_LOG(LogTemp, Log, TEXT("Criando sistema de Dragões Primordiais"));
    
    // Configurações padrão
    bool bEnableInfernalDragon = true;
    bool bEnableOceanDragon = true;
    bool bEnableMountainDragon = true;
    bool bEnableCloudDragon = true;
    bool bEnableElderDragon = true;
    
    // Ler configurações do JSON se fornecidas
    CommandData->TryGetBoolField(TEXT("enable_infernal_dragon"), bEnableInfernalDragon);
    CommandData->TryGetBoolField(TEXT("enable_ocean_dragon"), bEnableOceanDragon);
    CommandData->TryGetBoolField(TEXT("enable_mountain_dragon"), bEnableMountainDragon);
    CommandData->TryGetBoolField(TEXT("enable_cloud_dragon"), bEnableCloudDragon);
    CommandData->TryGetBoolField(TEXT("enable_elder_dragon"), bEnableElderDragon);
    
    // Criar configuração do sistema
    TSharedPtr<FJsonObject> SystemConfig = MakeShared<FJsonObject>();
    SystemConfig->SetStringField(TEXT("system_name"), TEXT("primordial_dragons_system"));
    
    TSharedPtr<FJsonObject> Dragons = MakeShared<FJsonObject>();
    
    // Dragão Infernal
    if (bEnableInfernalDragon)
    {
        TSharedPtr<FJsonObject> InfernalDragon = MakeShared<FJsonObject>();
        InfernalDragon->SetStringField(TEXT("name"), TEXT("Dragão Infernal"));
        InfernalDragon->SetBoolField(TEXT("enabled"), true);
        InfernalDragon->SetNumberField(TEXT("health"), 3500);
        InfernalDragon->SetNumberField(TEXT("health_per_minute"), 240);
        InfernalDragon->SetNumberField(TEXT("spawn_time"), 5);
        InfernalDragon->SetNumberField(TEXT("respawn_time"), 300);
        InfernalDragon->SetStringField(TEXT("buff_effect"), TEXT("+8% AD e +12 AP"));
        InfernalDragon->SetStringField(TEXT("soul_effect"), TEXT("Ataques e habilidades causam explosões"));
        Dragons->SetObjectField(TEXT("infernal_dragon"), InfernalDragon);
    }
    
    // Dragão Oceânico
    if (bEnableOceanDragon)
    {
        TSharedPtr<FJsonObject> OceanDragon = MakeShared<FJsonObject>();
        OceanDragon->SetStringField(TEXT("name"), TEXT("Dragão Oceânico"));
        OceanDragon->SetBoolField(TEXT("enabled"), true);
        OceanDragon->SetNumberField(TEXT("health"), 3500);
        OceanDragon->SetNumberField(TEXT("health_per_minute"), 240);
        OceanDragon->SetNumberField(TEXT("spawn_time"), 5);
        OceanDragon->SetNumberField(TEXT("respawn_time"), 300);
        OceanDragon->SetStringField(TEXT("buff_effect"), TEXT("Regeneração de HP e MP"));
        OceanDragon->SetStringField(TEXT("soul_effect"), TEXT("Habilidades curam aliados próximos"));
        Dragons->SetObjectField(TEXT("ocean_dragon"), OceanDragon);
    }
    
    // Dragão da Montanha
    if (bEnableMountainDragon)
    {
        TSharedPtr<FJsonObject> MountainDragon = MakeShared<FJsonObject>();
        MountainDragon->SetStringField(TEXT("name"), TEXT("Dragão da Montanha"));
        MountainDragon->SetBoolField(TEXT("enabled"), true);
        MountainDragon->SetNumberField(TEXT("health"), 3500);
        MountainDragon->SetNumberField(TEXT("health_per_minute"), 240);
        MountainDragon->SetNumberField(TEXT("spawn_time"), 5);
        MountainDragon->SetNumberField(TEXT("respawn_time"), 300);
        MountainDragon->SetStringField(TEXT("buff_effect"), TEXT("+6% Armor e +6% MR"));
        MountainDragon->SetStringField(TEXT("soul_effect"), TEXT("Escudo após usar habilidade"));
        Dragons->SetObjectField(TEXT("mountain_dragon"), MountainDragon);
    }
    
    // Dragão das Nuvens
    if (bEnableCloudDragon)
    {
        TSharedPtr<FJsonObject> CloudDragon = MakeShared<FJsonObject>();
        CloudDragon->SetStringField(TEXT("name"), TEXT("Dragão das Nuvens"));
        CloudDragon->SetBoolField(TEXT("enabled"), true);
        CloudDragon->SetNumberField(TEXT("health"), 3500);
        CloudDragon->SetNumberField(TEXT("health_per_minute"), 240);
        CloudDragon->SetNumberField(TEXT("spawn_time"), 5);
        CloudDragon->SetNumberField(TEXT("respawn_time"), 300);
        CloudDragon->SetStringField(TEXT("buff_effect"), TEXT("+3% Velocidade de Movimento"));
        CloudDragon->SetStringField(TEXT("soul_effect"), TEXT("Velocidade extra após usar ultimate"));
        Dragons->SetObjectField(TEXT("cloud_dragon"), CloudDragon);
    }
    
    // Dragão Ancião
    if (bEnableElderDragon)
    {
        TSharedPtr<FJsonObject> ElderDragon = MakeShared<FJsonObject>();
        ElderDragon->SetStringField(TEXT("name"), TEXT("Dragão Ancião"));
        ElderDragon->SetBoolField(TEXT("enabled"), true);
        ElderDragon->SetNumberField(TEXT("health"), 6400);
        ElderDragon->SetNumberField(TEXT("health_per_minute"), 180);
        ElderDragon->SetNumberField(TEXT("spawn_time"), 35);
        ElderDragon->SetNumberField(TEXT("respawn_time"), 360);
        ElderDragon->SetStringField(TEXT("buff_effect"), TEXT("Queimadura que executa inimigos com HP baixo"));
        ElderDragon->SetNumberField(TEXT("execute_threshold"), 20);
        Dragons->SetObjectField(TEXT("elder_dragon"), ElderDragon);
    }
    
    SystemConfig->SetObjectField(TEXT("dragons"), Dragons);
    
    // Salvar configuração no cache
    ObjectiveConfigCache.Add(TEXT("primordial_dragons_system"), SystemConfig);
    
    // Criar resposta de sucesso
    TSharedPtr<FJsonObject> ResponseData = MakeShared<FJsonObject>();
    ResponseData->SetObjectField(TEXT("system_config"), SystemConfig);
    ResponseData->SetStringField(TEXT("system_description"), TEXT("Sistema de Dragões Primordiais configurado"));
    
    UE_LOG(LogTemp, Log, TEXT("Sistema de Dragões Primordiais criado com sucesso"));
    return CreateSuccessResponse(ResponseData, TEXT("Sistema de Dragões Primordiais criado com sucesso"));
}

TSharedPtr<FJsonObject> FUnrealMCPObjectivesStructuresCommands::HandleCreateBaronAuracronSystem(const TSharedPtr<FJsonObject>& CommandData)
{
    if (!CommandData.IsValid())
    {
        return CreateErrorResponse(TEXT("Dados do comando inválidos"), TEXT("INVALID_COMMAND_DATA"));
    }
    
    UE_LOG(LogTemp, Log, TEXT("Criando sistema de Baron Auracron"));
    
    // Configurações padrão
    int32 SpawnTime = 20;
    int32 RespawnTime = 420;
    int32 BuffDuration = 180;
    
    // Ler configurações do JSON se fornecidas
    CommandData->TryGetNumberField(TEXT("spawn_time"), SpawnTime);
    CommandData->TryGetNumberField(TEXT("respawn_time"), RespawnTime);
    CommandData->TryGetNumberField(TEXT("buff_duration"), BuffDuration);
    
    // Criar configuração do sistema
    TSharedPtr<FJsonObject> SystemConfig = MakeShared<FJsonObject>();
    SystemConfig->SetStringField(TEXT("system_name"), TEXT("baron_auracron_system"));
    
    TSharedPtr<FJsonObject> BaronAuracron = MakeShared<FJsonObject>();
    BaronAuracron->SetStringField(TEXT("name"), TEXT("Baron Auracron"));
    BaronAuracron->SetBoolField(TEXT("enabled"), true);
    BaronAuracron->SetNumberField(TEXT("health"), 9000);
    BaronAuracron->SetNumberField(TEXT("health_per_minute"), 180);
    BaronAuracron->SetNumberField(TEXT("spawn_time"), SpawnTime);
    BaronAuracron->SetNumberField(TEXT("respawn_time"), RespawnTime);
    BaronAuracron->SetNumberField(TEXT("buff_duration"), BuffDuration);
    
    // Buff Hand of Baron
    TSharedPtr<FJsonObject> HandOfBaron = MakeShared<FJsonObject>();
    HandOfBaron->SetStringField(TEXT("name"), TEXT("Hand of Baron"));
    HandOfBaron->SetNumberField(TEXT("duration"), BuffDuration);
    
    TArray<TSharedPtr<FJsonValue>> BuffEffects;
    BuffEffects.Add(MakeShared<FJsonValueString>(TEXT("+40 AD")));
    BuffEffects.Add(MakeShared<FJsonValueString>(TEXT("+40 AP")));
    BuffEffects.Add(MakeShared<FJsonValueString>(TEXT("Recall aprimorado (4s)")));
    BuffEffects.Add(MakeShared<FJsonValueString>(TEXT("Minions empoderados")));
    BuffEffects.Add(MakeShared<FJsonValueString>(TEXT("Dano extra a estruturas")));
    HandOfBaron->SetArrayField(TEXT("buff_effects"), BuffEffects);
    
    BaronAuracron->SetObjectField(TEXT("hand_of_baron"), HandOfBaron);
    SystemConfig->SetObjectField(TEXT("baron_auracron"), BaronAuracron);
    
    // Salvar configuração no cache
    ObjectiveConfigCache.Add(TEXT("baron_auracron_system"), SystemConfig);
    
    // Criar resposta de sucesso
    TSharedPtr<FJsonObject> ResponseData = MakeShared<FJsonObject>();
    ResponseData->SetObjectField(TEXT("system_config"), SystemConfig);
    ResponseData->SetStringField(TEXT("system_description"), TEXT("Sistema de Baron Auracron configurado"));
    
    UE_LOG(LogTemp, Log, TEXT("Sistema de Baron Auracron criado com sucesso"));
    return CreateSuccessResponse(ResponseData, TEXT("Sistema de Baron Auracron criado com sucesso"));
}

TSharedPtr<FJsonObject> FUnrealMCPObjectivesStructuresCommands::HandleCreateTowerSystem(const TSharedPtr<FJsonObject>& CommandData)
{
    if (!CommandData.IsValid())
    {
        return CreateErrorResponse(TEXT("Dados do comando inválidos"), TEXT("INVALID_COMMAND_DATA"));
    }
    
    UE_LOG(LogTemp, Log, TEXT("Criando sistema de Torres"));
    
    // Criar configuração do sistema
    TSharedPtr<FJsonObject> SystemConfig = MakeShared<FJsonObject>();
    SystemConfig->SetStringField(TEXT("system_name"), TEXT("tower_system"));
    
    TSharedPtr<FJsonObject> TowerTypes = MakeShared<FJsonObject>();
    
    // Torre Externa
    TSharedPtr<FJsonObject> OuterTower = MakeShared<FJsonObject>();
    OuterTower->SetStringField(TEXT("name"), TEXT("Torre Externa"));
    OuterTower->SetNumberField(TEXT("health"), 5000);
    OuterTower->SetNumberField(TEXT("damage"), 152);
    OuterTower->SetNumberField(TEXT("gold_reward"), 650);
    OuterTower->SetBoolField(TEXT("fortification"), true);
    TowerTypes->SetObjectField(TEXT("outer_tower"), OuterTower);
    
    // Torre Interna
    TSharedPtr<FJsonObject> InnerTower = MakeShared<FJsonObject>();
    InnerTower->SetStringField(TEXT("name"), TEXT("Torre Interna"));
    InnerTower->SetNumberField(TEXT("health"), 5500);
    InnerTower->SetNumberField(TEXT("damage"), 170);
    InnerTower->SetNumberField(TEXT("gold_reward"), 700);
    InnerTower->SetBoolField(TEXT("fortification"), false);
    TowerTypes->SetObjectField(TEXT("inner_tower"), InnerTower);
    
    // Torre do Inibidor
    TSharedPtr<FJsonObject> InhibitorTower = MakeShared<FJsonObject>();
    InhibitorTower->SetStringField(TEXT("name"), TEXT("Torre do Inibidor"));
    InhibitorTower->SetNumberField(TEXT("health"), 4000);
    InhibitorTower->SetNumberField(TEXT("damage"), 190);
    InhibitorTower->SetNumberField(TEXT("gold_reward"), 750);
    InhibitorTower->SetBoolField(TEXT("fortification"), false);
    TowerTypes->SetObjectField(TEXT("inhibitor_tower"), InhibitorTower);
    
    // Torres do Nexus
    TSharedPtr<FJsonObject> NexusTowers = MakeShared<FJsonObject>();
    NexusTowers->SetStringField(TEXT("name"), TEXT("Torres do Nexus"));
    NexusTowers->SetNumberField(TEXT("health"), 4000);
    NexusTowers->SetNumberField(TEXT("damage"), 230);
    NexusTowers->SetNumberField(TEXT("gold_reward"), 50);
    NexusTowers->SetNumberField(TEXT("count"), 2);
    TowerTypes->SetObjectField(TEXT("nexus_towers"), NexusTowers);
    
    SystemConfig->SetObjectField(TEXT("tower_types"), TowerTypes);
    
    // Salvar configuração no cache
    ObjectiveConfigCache.Add(TEXT("tower_system"), SystemConfig);
    
    // Criar resposta de sucesso
    TSharedPtr<FJsonObject> ResponseData = MakeShared<FJsonObject>();
    ResponseData->SetObjectField(TEXT("system_config"), SystemConfig);
    ResponseData->SetStringField(TEXT("system_description"), TEXT("Sistema de Torres configurado"));
    
    UE_LOG(LogTemp, Log, TEXT("Sistema de Torres criado com sucesso"));
    return CreateSuccessResponse(ResponseData, TEXT("Sistema de Torres criado com sucesso"));
}

TSharedPtr<FJsonObject> FUnrealMCPObjectivesStructuresCommands::HandleCreateInhibitorSystem(const TSharedPtr<FJsonObject>& CommandData)
{
    if (!CommandData.IsValid())
    {
        return CreateErrorResponse(TEXT("Dados do comando inválidos"), TEXT("INVALID_COMMAND_DATA"));
    }
    
    UE_LOG(LogTemp, Log, TEXT("Criando sistema de Inibidores"));
    
    // Configurações padrão
    int32 RespawnTime = 300;
    bool bEnableSuperMinions = true;
    
    // Ler configurações do JSON se fornecidas
    CommandData->TryGetNumberField(TEXT("respawn_time"), RespawnTime);
    CommandData->TryGetBoolField(TEXT("enable_super_minions"), bEnableSuperMinions);
    
    // Criar configuração do sistema
    TSharedPtr<FJsonObject> SystemConfig = MakeShared<FJsonObject>();
    SystemConfig->SetStringField(TEXT("system_name"), TEXT("inhibitor_system"));
    
    TSharedPtr<FJsonObject> Inhibitor = MakeShared<FJsonObject>();
    Inhibitor->SetStringField(TEXT("name"), TEXT("Inibidor"));
    Inhibitor->SetNumberField(TEXT("health"), 4000);
    Inhibitor->SetNumberField(TEXT("respawn_time"), RespawnTime);
    Inhibitor->SetNumberField(TEXT("gold_reward"), 50);
    Inhibitor->SetBoolField(TEXT("enable_super_minions"), bEnableSuperMinions);
    
    if (bEnableSuperMinions)
    {
        TSharedPtr<FJsonObject> SuperMinions = MakeShared<FJsonObject>();
        SuperMinions->SetStringField(TEXT("name"), TEXT("Super Minions"));
        SuperMinions->SetNumberField(TEXT("health"), 1500);
        SuperMinions->SetNumberField(TEXT("damage"), 190);
        SuperMinions->SetNumberField(TEXT("armor"), 30);
        SuperMinions->SetNumberField(TEXT("magic_resist"), 30);
        SuperMinions->SetBoolField(TEXT("siege_damage"), true);
        Inhibitor->SetObjectField(TEXT("super_minions"), SuperMinions);
    }
    
    SystemConfig->SetObjectField(TEXT("inhibitor"), Inhibitor);
    
    // Salvar configuração no cache
    ObjectiveConfigCache.Add(TEXT("inhibitor_system"), SystemConfig);
    
    // Criar resposta de sucesso
    TSharedPtr<FJsonObject> ResponseData = MakeShared<FJsonObject>();
    ResponseData->SetObjectField(TEXT("system_config"), SystemConfig);
    ResponseData->SetStringField(TEXT("system_description"), TEXT("Sistema de Inibidores configurado"));
    
    UE_LOG(LogTemp, Log, TEXT("Sistema de Inibidores criado com sucesso"));
    return CreateSuccessResponse(ResponseData, TEXT("Sistema de Inibidores criado com sucesso"));
}

// ========================================================================
// Funções Auxiliares
// ========================================================================

TSharedPtr<FJsonObject> FUnrealMCPObjectivesStructuresCommands::CreateErrorResponse(const FString& ErrorMessage, const FString& ErrorCode)
{
    TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
    Response->SetStringField(TEXT("status"), RESPONSE_ERROR);
    Response->SetStringField(TEXT("message"), ErrorMessage);
    Response->SetStringField(TEXT("error_code"), ErrorCode);
    Response->SetStringField(TEXT("timestamp"), FDateTime::Now().ToString());
    
    UE_LOG(LogTemp, Error, TEXT("ObjectivesStructures Error [%s]: %s"), *ErrorCode, *ErrorMessage);
    return Response;
}

TSharedPtr<FJsonObject> FUnrealMCPObjectivesStructuresCommands::CreateSuccessResponse(const TSharedPtr<FJsonObject>& Data, const FString& Message)
{
    TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
    Response->SetStringField(TEXT("status"), RESPONSE_SUCCESS);
    Response->SetStringField(TEXT("message"), Message);
    Response->SetObjectField(TEXT("data"), Data);
    Response->SetStringField(TEXT("timestamp"), FDateTime::Now().ToString());
    
    return Response;
}
