# -*- coding: utf-8 -*-
"""
Performance Tools for Unreal MCP.

This module provides tools for performance optimization, LOD management, 
culling systems and memory management in Unreal Engine.
"""

import json
import logging
import random
import time
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
from mcp.server.fastmcp import FastMCP, Context

# Get logger
logger = logging.getLogger("UnrealMCP")

class LODLevel(Enum):
    """Níveis de LOD disponíveis"""
    ULTRA_HIGH = "ultra_high"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    ULTRA_LOW = "ultra_low"

class CullingType(Enum):
    """Tipos de culling disponíveis"""
    FRUSTUM = "frustum"
    OCCLUSION = "occlusion"
    DISTANCE = "distance"
    LAYER = "layer"
    DYNAMIC = "dynamic"

class MemoryPool(Enum):
    """Tipos de pools de memória"""
    STATIC_MESH = "static_mesh"
    DYNAMIC_MESH = "dynamic_mesh"
    TEXTURE = "texture"
    AUDIO = "audio"
    ANIMATION = "animation"
    PARTICLE = "particle"
    UI = "ui"
    SCRIPT = "script"

@dataclass
class PerformanceMetrics:
    """Métricas de performance do sistema"""
    fps: float
    frame_time_ms: float
    cpu_usage: float
    gpu_usage: float
    memory_usage_mb: float
    draw_calls: int
    triangles: int
    texture_memory_mb: float

def register_performance_tools(mcp: FastMCP):
    """Register Performance tools with the MCP server."""
    
    @mcp.tool()
    def create_performance_optimization_system(
        ctx: Context,
        system_id: str, 
        config: str
    ) -> Dict[str, Any]:
        """
        Create a multi-layer performance optimization system.
        
        Args:
            system_id: Unique system identifier
            config: System configuration
            
        Returns:
            Response with creation status
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            try:
                import json
                config_dict = json.loads(config) if config else {}
            except (json.JSONDecodeError, TypeError):
                config_dict = {}
            
            params = {
                "system_id": system_id,
                "config": config_dict
            }
            
            logger.info(f"Creating performance optimization system with params: {params}")
            response = unreal.send_command("create_performance_optimization_system", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Performance system creation response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error creating performance optimization system: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}

    @mcp.tool()
    def configure_dynamic_lod(
        ctx: Context,
        system_id: str, 
        auto_calculate: bool = True,
        target_objects: Optional[str] = None,
        distance_thresholds: Optional[str] = None,
        lod_levels: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Configure dynamic LOD system.
        
        Args:
            system_id: Performance system ID
            lod_config: LOD configuration
            
        Returns:
            Response with applied configuration
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            # Parse string parameters to appropriate types
            try:
                import json
                target_objects_list = json.loads(target_objects) if target_objects else []
                distance_thresholds_list = json.loads(distance_thresholds) if distance_thresholds else []
                lod_levels_list = json.loads(lod_levels) if lod_levels else []
            except (json.JSONDecodeError, TypeError):
                # Fallback to comma-separated parsing
                target_objects_list = [obj.strip() for obj in target_objects.split(",")] if target_objects else []
                distance_thresholds_list = [float(t.strip()) for t in distance_thresholds.split(",")] if distance_thresholds else []
                lod_levels_list = [level.strip() for level in lod_levels.split(",")] if lod_levels else []
            
            params = {
                "system_id": system_id,
                "target_objects": target_objects_list,
                "distance_thresholds": distance_thresholds_list,
                "lod_levels": lod_levels_list,
                "auto_calculate": auto_calculate
            }
            
            logger.info(f"Configuring dynamic LOD with params: {params}")
            response = unreal.send_command("configure_dynamic_lod", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Dynamic LOD configuration response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error configuring dynamic LOD: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}

    @mcp.tool()
    def setup_culling_systems(
        ctx: Context,
        system_id: str, 
        frustum_enabled: bool = True,
        occlusion_enabled: bool = True,
        distance_enabled: bool = True,
        max_distance: float = 10000.0,
        culling_types: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Configure multi-layer culling systems.
        
        Args:
            system_id: Performance system ID
            culling_config: Culling systems configuration
            
        Returns:
            Response with configured systems
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            # Parse culling_types parameter
            try:
                import json
                culling_types_list = json.loads(culling_types) if culling_types else []
            except (json.JSONDecodeError, TypeError):
                # Fallback to comma-separated parsing
                culling_types_list = [t.strip() for t in culling_types.split(",")] if culling_types else []
            
            params = {
                "system_id": system_id,
                "culling_types": culling_types_list,
                "frustum_enabled": frustum_enabled,
                "occlusion_enabled": occlusion_enabled,
                "distance_enabled": distance_enabled,
                "max_distance": max_distance
            }
            
            logger.info(f"Setting up culling systems with params: {params}")
            response = unreal.send_command("setup_culling_systems", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Culling systems setup response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error setting up culling systems: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}

    @mcp.tool()
    def configure_memory_management(
        ctx: Context,
        system_id: str, 
        auto_cleanup: bool = True,
        gc_frequency: float = 30.0,
        memory_budget_mb: float = 2048.0,
        pool_sizes: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Configure multi-layer memory management.
        
        Args:
            system_id: Performance system ID
            memory_config: Memory management configuration
            
        Returns:
            Response with applied configuration
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            # Parse pool_sizes parameter
            try:
                import json
                pool_sizes_dict = json.loads(pool_sizes) if pool_sizes else {}
            except (json.JSONDecodeError, TypeError):
                # Fallback to empty dict
                pool_sizes_dict = {}
            
            params = {
                "system_id": system_id,
                "pool_sizes": pool_sizes_dict,
                "auto_cleanup": auto_cleanup,
                "gc_frequency": gc_frequency,
                "memory_budget_mb": memory_budget_mb
            }
            
            logger.info(f"Configuring memory management with params: {params}")
            response = unreal.send_command("configure_memory_management", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Memory management configuration response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error configuring memory management: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}

    @mcp.tool()
    def optimize_rendering_pipeline(
        ctx: Context,
        system_id: str, 
        enable_instancing: bool = True,
        enable_batching: bool = True,
        shadow_quality: str = "medium",
        texture_streaming: bool = True,
        max_draw_calls: int = 2000
    ) -> Dict[str, Any]:
        """
        Optimize rendering pipeline.
        
        Args:
            system_id: Performance system ID
            pipeline_config: Pipeline configuration
            
        Returns:
            Response with applied optimizations
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            params = {
                "system_id": system_id,
                "enable_instancing": enable_instancing,
                "enable_batching": enable_batching,
                "shadow_quality": shadow_quality,
                "texture_streaming": texture_streaming,
                "max_draw_calls": max_draw_calls
            }
            
            logger.info(f"Optimizing rendering pipeline with params: {params}")
            response = unreal.send_command("optimize_rendering_pipeline", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Rendering pipeline optimization response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error optimizing rendering pipeline: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}

    @mcp.tool()
    def monitor_performance_metrics(
        ctx: Context,
        system_id: str, 
        sample_interval: float = 1.0,
        include_gpu_stats: bool = True,
        include_memory_stats: bool = True,
        include_render_stats: bool = True
    ) -> Dict[str, Any]:
        """
        Monitor performance metrics.
        
        Args:
            system_id: Performance system ID
            monitoring_config: Monitoring configuration
            
        Returns:
            Response with collected metrics
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            params = {
                "system_id": system_id,
                "sample_interval": sample_interval,
                "include_gpu_stats": include_gpu_stats,
                "include_memory_stats": include_memory_stats,
                "include_render_stats": include_render_stats
            }
            
            logger.info(f"Monitoring performance metrics with params: {params}")
            response = unreal.send_command("monitor_performance_metrics", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Performance monitoring response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error monitoring performance metrics: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}

    @mcp.tool()
    def debug_performance_issues(
        ctx: Context,
        system_id: str, 
        enable_profiling: bool = True,
        capture_callstack: bool = False,
        analysis_depth: str = "medium"
    ) -> Dict[str, Any]:
        """
        Debug performance issues.
        
        Args:
            system_id: Performance system ID
            debug_config: Debug configuration
            
        Returns:
            Response with debug information
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            params = {
                "system_id": system_id,
                "enable_profiling": enable_profiling,
                "capture_callstack": capture_callstack,
                "analysis_depth": analysis_depth
            }
            
            logger.info(f"Debugging performance issues with params: {params}")
            response = unreal.send_command("debug_performance_issues", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Performance debugging response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error debugging performance issues: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}

    @mcp.tool()
    def validate_performance_setup(
        ctx: Context,
        system_id: str, 
        check_compliance: bool = True,
        performance_requirements: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Validate performance setup.
        
        Args:
            system_id: Performance system ID
            validation_config: Validation configuration
            
        Returns:
            Response with validation results
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            # Parse performance_requirements parameter
            try:
                import json
                performance_requirements_dict = json.loads(performance_requirements) if performance_requirements else {}
            except (json.JSONDecodeError, TypeError):
                # Fallback to empty dict
                performance_requirements_dict = {}
            
            params = {
                "system_id": system_id,
                "check_compliance": check_compliance,
                "performance_requirements": performance_requirements_dict
            }
            
            logger.info(f"Validating performance setup with params: {params}")
            response = unreal.send_command("validate_performance_setup", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Performance validation response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error validating performance setup: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}

    @mcp.tool()
    def get_performance_system_status(
        ctx: Context,
        system_id: str
    ) -> Dict[str, Any]:
        """
        Get performance system status.
        
        Args:
            system_id: Performance system ID
            
        Returns:
            Current system status
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            params = {
                "system_id": system_id
            }
            
            logger.info(f"Getting performance system status with params: {params}")
            response = unreal.send_command("get_performance_system_status", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Performance system status response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error getting performance system status: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}