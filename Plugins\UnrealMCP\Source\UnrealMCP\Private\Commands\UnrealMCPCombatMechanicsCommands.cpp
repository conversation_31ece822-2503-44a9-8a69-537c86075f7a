// Copyright Epic Games, Inc. All Rights Reserved.

#include "Commands/UnrealMCPCombatMechanicsCommands.h"

// ========================================================================
// Constantes
// ========================================================================

const FString FUnrealMCPCombatMechanicsCommands::RESPONSE_SUCCESS = TEXT("success");
const FString FUnrealMCPCombatMechanicsCommands::RESPONSE_ERROR = TEXT("error");
const FString FUnrealMCPCombatMechanicsCommands::RESPONSE_WARNING = TEXT("warning");
const FString FUnrealMCPCombatMechanicsCommands::RESPONSE_INFO = TEXT("info");

// ========================================================================
// Construtor e Destrutor
// ========================================================================

FUnrealMCPCombatMechanicsCommands::FUnrealMCPCombatMechanicsCommands()
    : bIsInitialized(false)
    , LastUpdateTime(FDateTime::Now())
{
    bIsInitialized = true;
    UE_LOG(LogTemp, Log, TEXT("FUnrealMCPCombatMechanicsCommands: Sistema de Mecânicas de Combate inicializado"));
}

FUnrealMCPCombatMechanicsCommands::~FUnrealMCPCombatMechanicsCommands()
{
    CombatConfigCache.Empty();
    CombatStates.Empty();
    UE_LOG(LogTemp, Log, TEXT("FUnrealMCPCombatMechanicsCommands: Sistema de Mecânicas de Combate finalizado"));
}

// ========================================================================
// Método Principal de Comando
// ========================================================================

TSharedPtr<FJsonObject> FUnrealMCPCombatMechanicsCommands::HandleCommand(const FString& CommandType, const TSharedPtr<FJsonObject>& Params)
{
    if (CommandType == TEXT("create_damage_system"))
    {
        return HandleCreateDamageSystem(Params);
    }
    else if (CommandType == TEXT("create_crowd_control_system"))
    {
        return HandleCreateCrowdControlSystem(Params);
    }
    else if (CommandType == TEXT("create_healing_system"))
    {
        return HandleCreateHealingSystem(Params);
    }
    else if (CommandType == TEXT("create_tenacity_system"))
    {
        return HandleCreateTenacitySystem(Params);
    }
    else
    {
        return CreateErrorResponse(FString::Printf(TEXT("Comando não reconhecido: %s"), *CommandType), TEXT("UNKNOWN_COMMAND"));
    }
}

// ========================================================================
// Implementações dos Comandos
// ========================================================================

TSharedPtr<FJsonObject> FUnrealMCPCombatMechanicsCommands::HandleCreateDamageSystem(const TSharedPtr<FJsonObject>& CommandData)
{
    if (!CommandData.IsValid())
    {
        return CreateErrorResponse(TEXT("Dados do comando inválidos"), TEXT("INVALID_COMMAND_DATA"));
    }
    
    UE_LOG(LogTemp, Log, TEXT("Criando sistema de Dano"));
    
    // Configurações padrão
    bool bEnablePhysicalDamage = true;
    bool bEnableMagicDamage = true;
    bool bEnableTrueDamage = true;
    bool bEnableMixedDamage = true;
    
    // Ler configurações do JSON se fornecidas
    CommandData->TryGetBoolField(TEXT("enable_physical_damage"), bEnablePhysicalDamage);
    CommandData->TryGetBoolField(TEXT("enable_magic_damage"), bEnableMagicDamage);
    CommandData->TryGetBoolField(TEXT("enable_true_damage"), bEnableTrueDamage);
    CommandData->TryGetBoolField(TEXT("enable_mixed_damage"), bEnableMixedDamage);
    
    // Criar configuração do sistema
    TSharedPtr<FJsonObject> SystemConfig = MakeShared<FJsonObject>();
    SystemConfig->SetStringField(TEXT("system_name"), TEXT("damage_system"));
    
    TSharedPtr<FJsonObject> DamageTypes = MakeShared<FJsonObject>();
    
    // Dano Físico
    if (bEnablePhysicalDamage)
    {
        TSharedPtr<FJsonObject> PhysicalDamage = MakeShared<FJsonObject>();
        PhysicalDamage->SetStringField(TEXT("name"), TEXT("Dano Físico"));
        PhysicalDamage->SetBoolField(TEXT("enabled"), true);
        PhysicalDamage->SetStringField(TEXT("resistance_stat"), TEXT("Armor"));
        PhysicalDamage->SetStringField(TEXT("penetration_stat"), TEXT("Armor Penetration / Lethality"));
        PhysicalDamage->SetStringField(TEXT("scaling_stat"), TEXT("Attack Damage"));
        
        TArray<TSharedPtr<FJsonValue>> PhysicalSources;
        PhysicalSources.Add(MakeShared<FJsonValueString>(TEXT("Auto-ataques")));
        PhysicalSources.Add(MakeShared<FJsonValueString>(TEXT("Habilidades AD")));
        PhysicalSources.Add(MakeShared<FJsonValueString>(TEXT("Itens físicos")));
        PhysicalDamage->SetArrayField(TEXT("damage_sources"), PhysicalSources);
        
        DamageTypes->SetObjectField(TEXT("physical_damage"), PhysicalDamage);
    }
    
    // Dano Mágico
    if (bEnableMagicDamage)
    {
        TSharedPtr<FJsonObject> MagicDamage = MakeShared<FJsonObject>();
        MagicDamage->SetStringField(TEXT("name"), TEXT("Dano Mágico"));
        MagicDamage->SetBoolField(TEXT("enabled"), true);
        MagicDamage->SetStringField(TEXT("resistance_stat"), TEXT("Magic Resist"));
        MagicDamage->SetStringField(TEXT("penetration_stat"), TEXT("Magic Penetration"));
        MagicDamage->SetStringField(TEXT("scaling_stat"), TEXT("Ability Power"));
        
        TArray<TSharedPtr<FJsonValue>> MagicSources;
        MagicSources.Add(MakeShared<FJsonValueString>(TEXT("Habilidades AP")));
        MagicSources.Add(MakeShared<FJsonValueString>(TEXT("Itens mágicos")));
        MagicSources.Add(MakeShared<FJsonValueString>(TEXT("Efeitos passivos")));
        MagicDamage->SetArrayField(TEXT("damage_sources"), MagicSources);
        
        DamageTypes->SetObjectField(TEXT("magic_damage"), MagicDamage);
    }
    
    // Dano Verdadeiro
    if (bEnableTrueDamage)
    {
        TSharedPtr<FJsonObject> TrueDamage = MakeShared<FJsonObject>();
        TrueDamage->SetStringField(TEXT("name"), TEXT("Dano Verdadeiro"));
        TrueDamage->SetBoolField(TEXT("enabled"), true);
        TrueDamage->SetStringField(TEXT("resistance_stat"), TEXT("Nenhuma"));
        TrueDamage->SetStringField(TEXT("penetration_stat"), TEXT("Não aplicável"));
        TrueDamage->SetStringField(TEXT("special_property"), TEXT("Ignora todas as resistências"));
        
        TArray<TSharedPtr<FJsonValue>> TrueSources;
        TrueSources.Add(MakeShared<FJsonValueString>(TEXT("Habilidades especiais")));
        TrueSources.Add(MakeShared<FJsonValueString>(TEXT("Itens únicos")));
        TrueSources.Add(MakeShared<FJsonValueString>(TEXT("Execuções")));
        TrueDamage->SetArrayField(TEXT("damage_sources"), TrueSources);
        
        DamageTypes->SetObjectField(TEXT("true_damage"), TrueDamage);
    }
    
    // Dano Misto
    if (bEnableMixedDamage)
    {
        TSharedPtr<FJsonObject> MixedDamage = MakeShared<FJsonObject>();
        MixedDamage->SetStringField(TEXT("name"), TEXT("Dano Misto"));
        MixedDamage->SetBoolField(TEXT("enabled"), true);
        MixedDamage->SetStringField(TEXT("description"), TEXT("Combinação de tipos de dano"));
        
        TArray<TSharedPtr<FJsonValue>> MixedSources;
        MixedSources.Add(MakeShared<FJsonValueString>(TEXT("Champions híbridos")));
        MixedSources.Add(MakeShared<FJsonValueString>(TEXT("Itens híbridos")));
        MixedSources.Add(MakeShared<FJsonValueString>(TEXT("Conversões de dano")));
        MixedDamage->SetArrayField(TEXT("damage_sources"), MixedSources);
        
        DamageTypes->SetObjectField(TEXT("mixed_damage"), MixedDamage);
    }
    
    SystemConfig->SetObjectField(TEXT("damage_types"), DamageTypes);
    
    // Salvar configuração no cache
    CombatConfigCache.Add(TEXT("damage_system"), SystemConfig);
    
    // Criar resposta de sucesso
    TSharedPtr<FJsonObject> ResponseData = MakeShared<FJsonObject>();
    ResponseData->SetObjectField(TEXT("system_config"), SystemConfig);
    ResponseData->SetStringField(TEXT("system_description"), TEXT("Sistema de Dano configurado"));
    
    UE_LOG(LogTemp, Log, TEXT("Sistema de Dano criado com sucesso"));
    return CreateSuccessResponse(ResponseData, TEXT("Sistema de Dano criado com sucesso"));
}

TSharedPtr<FJsonObject> FUnrealMCPCombatMechanicsCommands::HandleCreateCrowdControlSystem(const TSharedPtr<FJsonObject>& CommandData)
{
    if (!CommandData.IsValid())
    {
        return CreateErrorResponse(TEXT("Dados do comando inválidos"), TEXT("INVALID_COMMAND_DATA"));
    }
    
    UE_LOG(LogTemp, Log, TEXT("Criando sistema de Crowd Control"));
    
    // Configurações padrão
    bool bEnableHardCC = true;
    bool bEnableSoftCC = true;
    
    // Ler configurações do JSON se fornecidas
    CommandData->TryGetBoolField(TEXT("enable_hard_cc"), bEnableHardCC);
    CommandData->TryGetBoolField(TEXT("enable_soft_cc"), bEnableSoftCC);
    
    // Criar configuração do sistema
    TSharedPtr<FJsonObject> SystemConfig = MakeShared<FJsonObject>();
    SystemConfig->SetStringField(TEXT("system_name"), TEXT("crowd_control_system"));
    
    TSharedPtr<FJsonObject> CCTypes = MakeShared<FJsonObject>();
    
    // Hard CC
    if (bEnableHardCC)
    {
        TSharedPtr<FJsonObject> HardCC = MakeShared<FJsonObject>();
        HardCC->SetStringField(TEXT("name"), TEXT("Hard Crowd Control"));
        HardCC->SetBoolField(TEXT("enabled"), true);
        HardCC->SetStringField(TEXT("description"), TEXT("Impede completamente ações do alvo"));
        
        TArray<TSharedPtr<FJsonValue>> HardCCTypes;
        HardCCTypes.Add(MakeShared<FJsonValueString>(TEXT("Stun - Impede movimento e ações")));
        HardCCTypes.Add(MakeShared<FJsonValueString>(TEXT("Root - Impede movimento, permite ações")));
        HardCCTypes.Add(MakeShared<FJsonValueString>(TEXT("Suppress - Impede tudo, incluindo Summoner Spells")));
        HardCCTypes.Add(MakeShared<FJsonValueString>(TEXT("Knockup - Deslocamento forçado no ar")));
        HardCCTypes.Add(MakeShared<FJsonValueString>(TEXT("Fear - Movimento aleatório incontrolável")));
        HardCCTypes.Add(MakeShared<FJsonValueString>(TEXT("Charm - Força movimento em direção ao caster")));
        HardCCTypes.Add(MakeShared<FJsonValueString>(TEXT("Taunt - Força ataques contra o caster")));
        HardCC->SetArrayField(TEXT("cc_types"), HardCCTypes);
        
        CCTypes->SetObjectField(TEXT("hard_cc"), HardCC);
    }
    
    // Soft CC
    if (bEnableSoftCC)
    {
        TSharedPtr<FJsonObject> SoftCC = MakeShared<FJsonObject>();
        SoftCC->SetStringField(TEXT("name"), TEXT("Soft Crowd Control"));
        SoftCC->SetBoolField(TEXT("enabled"), true);
        SoftCC->SetStringField(TEXT("description"), TEXT("Reduz efetividade sem impedir completamente"));
        
        TArray<TSharedPtr<FJsonValue>> SoftCCTypes;
        SoftCCTypes.Add(MakeShared<FJsonValueString>(TEXT("Slow - Reduz velocidade de movimento")));
        SoftCCTypes.Add(MakeShared<FJsonValueString>(TEXT("Blind - Impede acerto de auto-ataques")));
        SoftCCTypes.Add(MakeShared<FJsonValueString>(TEXT("Silence - Impede uso de habilidades")));
        SoftCCTypes.Add(MakeShared<FJsonValueString>(TEXT("Disarm - Impede auto-ataques")));
        SoftCCTypes.Add(MakeShared<FJsonValueString>(TEXT("Cripple - Reduz velocidade de ataque")));
        SoftCC->SetArrayField(TEXT("cc_types"), SoftCCTypes);
        
        CCTypes->SetObjectField(TEXT("soft_cc"), SoftCC);
    }
    
    SystemConfig->SetObjectField(TEXT("cc_types"), CCTypes);
    
    // Salvar configuração no cache
    CombatConfigCache.Add(TEXT("crowd_control_system"), SystemConfig);
    
    // Criar resposta de sucesso
    TSharedPtr<FJsonObject> ResponseData = MakeShared<FJsonObject>();
    ResponseData->SetObjectField(TEXT("system_config"), SystemConfig);
    ResponseData->SetStringField(TEXT("system_description"), TEXT("Sistema de Crowd Control configurado"));
    
    UE_LOG(LogTemp, Log, TEXT("Sistema de Crowd Control criado com sucesso"));
    return CreateSuccessResponse(ResponseData, TEXT("Sistema de Crowd Control criado com sucesso"));
}

TSharedPtr<FJsonObject> FUnrealMCPCombatMechanicsCommands::HandleCreateHealingSystem(const TSharedPtr<FJsonObject>& CommandData)
{
    if (!CommandData.IsValid())
    {
        return CreateErrorResponse(TEXT("Dados do comando inválidos"), TEXT("INVALID_COMMAND_DATA"));
    }
    
    UE_LOG(LogTemp, Log, TEXT("Criando sistema de Cura"));
    
    // Criar configuração do sistema
    TSharedPtr<FJsonObject> SystemConfig = MakeShared<FJsonObject>();
    SystemConfig->SetStringField(TEXT("system_name"), TEXT("healing_system"));
    
    TSharedPtr<FJsonObject> HealingTypes = MakeShared<FJsonObject>();
    
    // Regeneração
    TSharedPtr<FJsonObject> Regeneration = MakeShared<FJsonObject>();
    Regeneration->SetStringField(TEXT("name"), TEXT("Regeneração"));
    Regeneration->SetStringField(TEXT("description"), TEXT("Cura passiva ao longo do tempo"));
    HealingTypes->SetObjectField(TEXT("regeneration"), Regeneration);
    
    // Lifesteal
    TSharedPtr<FJsonObject> Lifesteal = MakeShared<FJsonObject>();
    Lifesteal->SetStringField(TEXT("name"), TEXT("Lifesteal"));
    Lifesteal->SetStringField(TEXT("description"), TEXT("Cura baseada em dano físico causado"));
    HealingTypes->SetObjectField(TEXT("lifesteal"), Lifesteal);
    
    // Spell Vamp
    TSharedPtr<FJsonObject> SpellVamp = MakeShared<FJsonObject>();
    SpellVamp->SetStringField(TEXT("name"), TEXT("Spell Vamp"));
    SpellVamp->SetStringField(TEXT("description"), TEXT("Cura baseada em dano mágico causado"));
    HealingTypes->SetObjectField(TEXT("spell_vamp"), SpellVamp);
    
    // Omnivamp
    TSharedPtr<FJsonObject> Omnivamp = MakeShared<FJsonObject>();
    Omnivamp->SetStringField(TEXT("name"), TEXT("Omnivamp"));
    Omnivamp->SetStringField(TEXT("description"), TEXT("Cura baseada em qualquer dano causado"));
    HealingTypes->SetObjectField(TEXT("omnivamp"), Omnivamp);
    
    SystemConfig->SetObjectField(TEXT("healing_types"), HealingTypes);
    
    // Salvar configuração no cache
    CombatConfigCache.Add(TEXT("healing_system"), SystemConfig);
    
    // Criar resposta de sucesso
    TSharedPtr<FJsonObject> ResponseData = MakeShared<FJsonObject>();
    ResponseData->SetObjectField(TEXT("system_config"), SystemConfig);
    ResponseData->SetStringField(TEXT("system_description"), TEXT("Sistema de Cura configurado"));
    
    UE_LOG(LogTemp, Log, TEXT("Sistema de Cura criado com sucesso"));
    return CreateSuccessResponse(ResponseData, TEXT("Sistema de Cura criado com sucesso"));
}

TSharedPtr<FJsonObject> FUnrealMCPCombatMechanicsCommands::HandleCreateTenacitySystem(const TSharedPtr<FJsonObject>& CommandData)
{
    if (!CommandData.IsValid())
    {
        return CreateErrorResponse(TEXT("Dados do comando inválidos"), TEXT("INVALID_COMMAND_DATA"));
    }
    
    UE_LOG(LogTemp, Log, TEXT("Criando sistema de Tenacidade"));
    
    // Criar configuração do sistema
    TSharedPtr<FJsonObject> SystemConfig = MakeShared<FJsonObject>();
    SystemConfig->SetStringField(TEXT("system_name"), TEXT("tenacity_system"));
    
    TSharedPtr<FJsonObject> Tenacity = MakeShared<FJsonObject>();
    Tenacity->SetStringField(TEXT("name"), TEXT("Tenacidade"));
    Tenacity->SetStringField(TEXT("description"), TEXT("Reduz duração de efeitos de crowd control"));
    Tenacity->SetNumberField(TEXT("max_reduction_percent"), 51);
    Tenacity->SetBoolField(TEXT("affects_knockups"), false);
    Tenacity->SetBoolField(TEXT("affects_suppression"), false);
    Tenacity->SetBoolField(TEXT("stacking_mechanics"), true);
    
    TArray<TSharedPtr<FJsonValue>> AffectedCC;
    AffectedCC.Add(MakeShared<FJsonValueString>(TEXT("Stun")));
    AffectedCC.Add(MakeShared<FJsonValueString>(TEXT("Root")));
    AffectedCC.Add(MakeShared<FJsonValueString>(TEXT("Fear")));
    AffectedCC.Add(MakeShared<FJsonValueString>(TEXT("Charm")));
    AffectedCC.Add(MakeShared<FJsonValueString>(TEXT("Taunt")));
    AffectedCC.Add(MakeShared<FJsonValueString>(TEXT("Slow")));
    AffectedCC.Add(MakeShared<FJsonValueString>(TEXT("Blind")));
    AffectedCC.Add(MakeShared<FJsonValueString>(TEXT("Silence")));
    Tenacity->SetArrayField(TEXT("affected_cc"), AffectedCC);
    
    SystemConfig->SetObjectField(TEXT("tenacity"), Tenacity);
    
    // Salvar configuração no cache
    CombatConfigCache.Add(TEXT("tenacity_system"), SystemConfig);
    
    // Criar resposta de sucesso
    TSharedPtr<FJsonObject> ResponseData = MakeShared<FJsonObject>();
    ResponseData->SetObjectField(TEXT("system_config"), SystemConfig);
    ResponseData->SetStringField(TEXT("system_description"), TEXT("Sistema de Tenacidade configurado"));
    
    UE_LOG(LogTemp, Log, TEXT("Sistema de Tenacidade criado com sucesso"));
    return CreateSuccessResponse(ResponseData, TEXT("Sistema de Tenacidade criado com sucesso"));
}

// ========================================================================
// Funções Auxiliares
// ========================================================================

TSharedPtr<FJsonObject> FUnrealMCPCombatMechanicsCommands::CreateErrorResponse(const FString& ErrorMessage, const FString& ErrorCode)
{
    TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
    Response->SetStringField(TEXT("status"), RESPONSE_ERROR);
    Response->SetStringField(TEXT("message"), ErrorMessage);
    Response->SetStringField(TEXT("error_code"), ErrorCode);
    Response->SetStringField(TEXT("timestamp"), FDateTime::Now().ToString());
    
    UE_LOG(LogTemp, Error, TEXT("CombatMechanics Error [%s]: %s"), *ErrorCode, *ErrorMessage);
    return Response;
}

TSharedPtr<FJsonObject> FUnrealMCPCombatMechanicsCommands::CreateSuccessResponse(const TSharedPtr<FJsonObject>& Data, const FString& Message)
{
    TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
    Response->SetStringField(TEXT("status"), RESPONSE_SUCCESS);
    Response->SetStringField(TEXT("message"), Message);
    Response->SetObjectField(TEXT("data"), Data);
    Response->SetStringField(TEXT("timestamp"), FDateTime::Now().ToString());
    
    return Response;
}
