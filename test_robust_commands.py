import sys
sys.path.append('Python')
from unreal_mcp_server import get_unreal_connection
import json

print('🚀 TESTANDO TODOS OS COMANDOS IMPLEMENTADOS - UNREAL ENGINE 5.6')
print('=' * 70)

conn = get_unreal_connection()
if not conn:
    print('❌ ERRO: Unreal Engine não está rodando ou não está escutando na porta 55557')
    print('   Verifique se o plugin UnrealMCP está ativo no projeto')
    exit(1)

print('✅ Conexão TCP estabelecida com Unreal Engine!')
print()

# Lista completa de comandos para testar
test_commands = [
    # Comandos básicos
    ('ping', {}, 'Teste de conectividade básica'),
    ('get_engine_version', {}, 'Versão do Unreal Engine'),
    ('get_project_name', {}, 'Nome do projeto'),
    ('list_available_commands', {}, 'Lista de comandos disponíveis'),
    ('get_world_info', {}, 'Informações do mundo atual'),
    ('get_level_info', {}, 'Informações do nível atual'),
    
    # Comandos de atores
    ('get_actors_in_level', {'limit': 5}, 'Lista de atores (limitado a 5)'),
    ('get_selected_actors', {}, 'Atores selecionados no editor'),
    
    # Comandos de criação
    ('spawn_actor', {
        'type': 'StaticMeshActor', 
        'name': 'TestActor_Robust_001', 
        'location': [200, 0, 100]
    }, 'Criar StaticMeshActor'),
    
    ('spawn_actor', {
        'type': 'PointLight', 
        'name': 'TestLight_Robust_001', 
        'location': [300, 0, 150]
    }, 'Criar PointLight'),
    
    # Comandos de Blueprint
    ('create_blueprint_class', {
        'name': 'BP_TestClass_Robust',
        'parent_class': 'Actor',
        'package_path': '/Game/Blueprints/'
    }, 'Criar Blueprint Class'),
    
    ('create_actor_blueprint', {
        'name': 'BP_TestActor_Robust',
        'package_path': '/Game/Blueprints/Actors/'
    }, 'Criar Actor Blueprint'),
]

successful_commands = []
failed_commands = []
results_summary = {}

print('🧪 INICIANDO TESTES ROBUSTOS...')
print()

for i, (cmd_name, cmd_params, description) in enumerate(test_commands, 1):
    print(f'[{i:2d}/{len(test_commands)}] 🎯 {cmd_name}: {description}')
    
    try:
        response = conn.send_command(cmd_name, cmd_params)
        
        if not response:
            failed_commands.append(cmd_name)
            results_summary[cmd_name] = 'Nenhuma resposta do Unreal'
            print(f'      ❌ FALHA: Nenhuma resposta')
            continue
            
        status = response.get('status', 'unknown')
        
        if status == 'success':
            successful_commands.append(cmd_name)
            result = response.get('result', {})
            
            # Análise detalhada por comando
            if cmd_name == 'ping':
                message = result.get('message', '')
                results_summary[cmd_name] = f'Pong recebido: {message}'
                print(f'      ✅ SUCESSO: {message}')
                
            elif cmd_name == 'get_engine_version':
                version = result.get('version', 'Unknown')
                major = result.get('major', 0)
                minor = result.get('minor', 0)
                results_summary[cmd_name] = f'UE {major}.{minor} - {version}'
                print(f'      ✅ SUCESSO: Unreal Engine {major}.{minor}')
                print(f'          Versão completa: {version}')
                
            elif cmd_name == 'get_project_name':
                project = result.get('project_name', 'Unknown')
                results_summary[cmd_name] = f'Projeto: {project}'
                print(f'      ✅ SUCESSO: Projeto "{project}"')
                
            elif cmd_name == 'list_available_commands':
                commands = result.get('commands', [])
                results_summary[cmd_name] = f'{len(commands)} comandos disponíveis'
                print(f'      ✅ SUCESSO: {len(commands)} comandos listados')
                print(f'          Primeiros 5: {commands[:5]}')
                
            elif cmd_name == 'get_world_info':
                world_name = result.get('world_name', 'Unknown')
                world_type = result.get('world_type', 'Unknown')
                is_editor = result.get('is_editor_world', False)
                results_summary[cmd_name] = f'{world_name} ({world_type})'
                print(f'      ✅ SUCESSO: Mundo "{world_name}" - Tipo: {world_type}')
                print(f'          Editor World: {is_editor}')
                
            elif cmd_name == 'get_level_info':
                level_name = result.get('level_name', 'Unknown')
                actor_count = result.get('actor_count', 0)
                results_summary[cmd_name] = f'{level_name} ({actor_count} atores)'
                print(f'      ✅ SUCESSO: Nível "{level_name}" com {actor_count} atores')
                
            elif cmd_name == 'get_actors_in_level':
                returned = result.get('returned_actors', 0)
                total = result.get('total_actors', 0)
                limited = result.get('limited', False)
                results_summary[cmd_name] = f'{returned}/{total} atores' + (' (limitado)' if limited else '')
                print(f'      ✅ SUCESSO: {returned}/{total} atores retornados')
                if limited:
                    print(f'          ⚠️  Lista limitada para performance')
                    
            elif cmd_name == 'get_selected_actors':
                count = result.get('count', 0)
                results_summary[cmd_name] = f'{count} atores selecionados'
                print(f'      ✅ SUCESSO: {count} atores selecionados no editor')
                
            elif cmd_name == 'spawn_actor':
                actor_name = result.get('name', 'Unknown')
                actor_class = result.get('class', 'Unknown')
                location = result.get('location', [0,0,0])
                results_summary[cmd_name] = f'{actor_name} ({actor_class}) criado'
                print(f'      ✅ SUCESSO: Actor "{actor_name}" ({actor_class}) criado')
                print(f'          Localização: [{location[0]:.1f}, {location[1]:.1f}, {location[2]:.1f}]')
                
            elif cmd_name in ['create_blueprint_class', 'create_actor_blueprint']:
                bp_name = result.get('name', 'Unknown')
                bp_path = result.get('path', 'Unknown')
                saved = result.get('saved', False)
                results_summary[cmd_name] = f'{bp_name} criado' + (' e salvo' if saved else '')
                print(f'      ✅ SUCESSO: Blueprint "{bp_name}" criado')
                print(f'          Caminho: {bp_path}')
                print(f'          Salvo: {"Sim" if saved else "Não"}')
            else:
                results_summary[cmd_name] = 'Sucesso'
                print(f'      ✅ SUCESSO: Comando executado')
                
        elif status == 'error':
            failed_commands.append(cmd_name)
            error_msg = response.get('error', 'Erro desconhecido')
            results_summary[cmd_name] = f'ERRO: {error_msg}'
            print(f'      ❌ ERRO: {error_msg}')
            
        else:
            failed_commands.append(cmd_name)
            results_summary[cmd_name] = f'Status desconhecido: {status}'
            print(f'      ❌ FALHA: Status desconhecido - {status}')
            
    except Exception as e:
        failed_commands.append(cmd_name)
        results_summary[cmd_name] = f'Exception: {str(e)}'
        print(f'      ❌ EXCEPTION: {str(e)}')
    
    print()

# Relatório final detalhado
print('=' * 70)
print('📊 RELATÓRIO FINAL DE TESTES')
print('=' * 70)
print()

print(f'✅ COMANDOS FUNCIONANDO: {len(successful_commands)}/{len(test_commands)}')
for cmd in successful_commands:
    print(f'   ✓ {cmd}: {results_summary[cmd]}')

print()
print(f'❌ COMANDOS COM PROBLEMAS: {len(failed_commands)}/{len(test_commands)}')
for cmd in failed_commands:
    print(f'   ✗ {cmd}: {results_summary[cmd]}')

print()
success_rate = (len(successful_commands) / len(test_commands)) * 100
print(f'📈 TAXA DE SUCESSO: {success_rate:.1f}%')

if success_rate >= 90:
    print('🎉 EXCELENTE! Plugin funcionando de forma robusta!')
elif success_rate >= 70:
    print('👍 BOM! Maioria dos comandos funcionando, pequenos ajustes necessários.')
elif success_rate >= 50:
    print('⚠️  MÉDIO! Vários comandos precisam de correções.')
else:
    print('🚨 CRÍTICO! Plugin precisa de correções significativas.')

print()
print('=' * 70)
