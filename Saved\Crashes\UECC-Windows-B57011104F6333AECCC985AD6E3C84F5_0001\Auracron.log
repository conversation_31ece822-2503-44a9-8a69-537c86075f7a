﻿Log file open, 08/25/25 23:43:12
LogWindows: Failed to load 'aqProf.dll' (GetLastError=126)
LogWindows: File 'aqProf.dll' does not exist
LogProfilingDebugging: Loading WinPixEventRuntime.dll for PIX profiling (from ../../../Engine/Binaries/ThirdParty/Windows/WinPixEventRuntime/x64).
LogWindows: Failed to load 'VtuneApi.dll' (GetLastError=126)
LogWindows: File 'VtuneApi.dll' does not exist
LogWindows: Failed to load 'VtuneApi32e.dll' (GetLastError=126)
LogWindows: File 'VtuneApi32e.dll' does not exist
LogWindows: Started CrashReportClient (pid=19396)
LogWindows: Enabling Tpause support
LogWindows: Custom abort handler registered for crash reporting.
LogInit: Display: Running engine for game: Auracron
LogCore: UTS: Unreal Trace Server launched successfully
LogTrace: Initializing trace...
LogCore: Display: Requested channels: 'cpu,gpu,frame,log,bookmark,screenshot,region'
LogTrace: Display: Control listening on port 1985
LogTrace: Finished trace initialization.
LogCsvProfiler: Display: Metadata set : platform="Windows"
LogCsvProfiler: Display: Metadata set : config="Development"
LogCsvProfiler: Display: Metadata set : buildversion="++UE5+Release-5.6-***********"
LogCsvProfiler: Display: Metadata set : engineversion="5.6.1-44394996+++UE5+Release-5.6"
LogCsvProfiler: Display: Metadata set : os="Windows 11 (24H2) [10.0.26100.4946] "
LogCsvProfiler: Display: Metadata set : cpu="GenuineIntel|13th Gen Intel(R) Core(TM) i5-1345U"
LogCsvProfiler: Display: Metadata set : pgoenabled="0"
LogCsvProfiler: Display: Metadata set : pgoprofilingenabled="0"
LogCsvProfiler: Display: Metadata set : ltoenabled="0"
LogCsvProfiler: Display: Metadata set : asan="0"
LogCsvProfiler: Display: Metadata set : commandline="" C:\Game\Auracron\Auracron.uproject""
LogCsvProfiler: Display: Metadata set : loginid="8bb1964343e8298f803f869f44351803"
LogCsvProfiler: Display: Metadata set : llm="0"
LogStats: Stats thread started at 0.347994
LogCsvProfiler: Display: Metadata set : systemresolution.resx="1280"
LogCsvProfiler: Display: Metadata set : systemresolution.resy="720"
LogICUInternationalization: ICU TimeZone Detection - Raw Offset: -3:00, Platform Override: ''
LogInit: Session CrashGUID >====================================================
         Session CrashGUID >   UECC-Windows-B57011104F6333AECCC985AD6E3C84F5
         Session CrashGUID >====================================================
LogConfig: No local boot hotfix file found at: [../../../../../../Game/Auracron/Saved/PersistentDownloadDir/HotfixForNextBoot.txt]
LogAudio: Display: Pre-Initializing Audio Device Manager...
LogAudio: Display: AudioInfo: 'OPUS' Registered
LogAudioDebug: Display: Lib vorbis DLL was dynamically loaded.
LogAudio: Display: AudioInfo: 'OGG' Registered
LogAudio: Display: AudioInfo: 'ADPCM' Registered
LogAudio: Display: AudioInfo: 'PCM' Registered
LogAudio: Display: AudioInfo: 'BINKA' Registered
LogAudio: Display: AudioInfo: 'RADA' Registered
LogAudio: Display: Audio Device Manager Pre-Initialized
LogPluginManager: Looking for build plugins target receipt
LogPluginManager: Found matching target receipt: ../../../../../../Game/Auracron/Binaries/Win64/AuracronEditor.target
LogPluginManager: Looking for enabled plugins target receipt
LogConfig: Display: Loading Mac ini files took 0.06 seconds
LogConfig: Display: Loading Android ini files took 0.06 seconds
LogPluginManager: Found matching target receipt: ../../../../../../Game/Auracron/Binaries/Win64/AuracronEditor.target
LogConfig: Display: Loading IOS ini files took 0.06 seconds
LogConfig: Display: Loading VulkanPC ini files took 0.06 seconds
LogConfig: Display: Loading Windows ini files took 0.07 seconds
LogConfig: Display: Loading Unix ini files took 0.07 seconds
LogConfig: Display: Loading TVOS ini files took 0.07 seconds
LogAssetRegistry: Display: Asset registry cache read as 73.0 MiB from ../../../../../../Game/Auracron/Intermediate/CachedAssetRegistry_0.bin.
LogConfig: Display: Loading Linux ini files took 0.08 seconds
LogPluginManager: Mounting Engine plugin ChaosCloth
LogPluginManager: Mounting Engine plugin ChaosInsights
LogPluginManager: Mounting Engine plugin ChaosVD
LogPluginManager: Mounting Engine plugin CmdLinkServer
LogPluginManager: Mounting Engine plugin EnhancedInput
LogPluginManager: Mounting Engine plugin FastBuildController
LogPluginManager: Mounting Engine plugin IoStoreInsights
LogPluginManager: Mounting Engine plugin MassInsights
LogPluginManager: Mounting Engine plugin MeshPainting
LogPluginManager: Mounting Engine plugin PCG
LogPluginManager: Mounting Engine plugin RenderGraphInsights
LogPluginManager: Mounting Engine plugin TraceUtilities
LogPluginManager: Mounting Engine plugin UbaController
LogPluginManager: Mounting Engine plugin WorldMetrics
LogPluginManager: Mounting Engine plugin XGEController
LogPluginManager: Mounting Engine plugin ACLPlugin
LogPluginManager: Mounting Engine plugin AnimationData
LogPluginManager: Mounting Engine plugin AnimationModifierLibrary
LogPluginManager: Mounting Engine plugin BlendSpaceMotionAnalysis
LogPluginManager: Mounting Engine plugin ControlRig
LogPluginManager: Mounting Engine plugin ControlRigModules
LogPluginManager: Mounting Engine plugin ControlRigSpline
LogPluginManager: Mounting Engine plugin DeformerGraph
LogPluginManager: Mounting Engine plugin GameplayInsights
LogPluginManager: Mounting Engine plugin IKRig
LogPluginManager: Mounting Engine plugin RigLogic
LogPluginManager: Mounting Engine plugin SkeletalMeshModelingTools
LogPluginManager: Mounting Engine plugin TweeningUtils
LogPluginManager: Mounting Engine plugin CameraShakePreviewer
LogPluginManager: Mounting Engine plugin EngineCameras
LogPluginManager: Mounting Engine plugin GameplayCameras
LogPluginManager: Mounting Engine plugin OodleNetwork
LogPluginManager: Mounting Engine plugin AssetManagerEditor
LogPluginManager: Mounting Engine plugin BlueprintHeaderView
LogPluginManager: Mounting Engine plugin ChangelistReview
LogPluginManager: Mounting Engine plugin ColorGrading
LogConfig: Display: Loading VisionOS ini files took 0.04 seconds
LogPluginManager: Mounting Engine plugin CryptoKeys
LogPluginManager: Mounting Engine plugin CurveEditorTools
LogPluginManager: Mounting Engine plugin DataValidation
LogPluginManager: Mounting Engine plugin EditorDebugTools
LogPluginManager: Mounting Engine plugin EditorScriptingUtilities
LogPluginManager: Mounting Engine plugin EngineAssetDefinitions
LogPluginManager: Mounting Engine plugin FacialAnimation
LogPluginManager: Mounting Engine plugin GameplayTagsEditor
LogPluginManager: Mounting Engine plugin GeometryMode
LogPluginManager: Mounting Engine plugin MacGraphicsSwitching
LogPluginManager: Mounting Engine plugin MaterialAnalyzer
LogPluginManager: Mounting Engine plugin MeshLODToolset
LogPluginManager: Mounting Engine plugin MobileLauncherProfileWizard
LogPluginManager: Mounting Engine plugin ModelingToolsEditorMode
LogPluginManager: Mounting Engine plugin ProxyLODPlugin
LogPluginManager: Mounting Engine plugin SequencerAnimTools
LogPluginManager: Mounting Engine plugin SpeedTreeImporter
LogPluginManager: Mounting Engine plugin PluginBrowser
LogPluginManager: Mounting Engine plugin StylusInput
LogPluginManager: Mounting Engine plugin UMGWidgetPreview
LogPluginManager: Mounting Engine plugin WorldPartitionHLODUtilities
LogPluginManager: Mounting Engine plugin UVEditor
LogPluginManager: Mounting Engine plugin GLTFExporter
LogPluginManager: Mounting Engine plugin DatasmithContent
LogPluginManager: Mounting Engine plugin VariantManagerContent
LogPluginManager: Mounting Engine plugin Cascade
LogPluginManager: Mounting Engine plugin VariantManager
LogPluginManager: Mounting Engine plugin NiagaraSimCaching
LogPluginManager: Mounting Engine plugin Niagara
LogPluginManager: Mounting Engine plugin AlembicImporter
LogPluginManager: Mounting Engine plugin InterchangeAssets
LogPluginManager: Mounting Engine plugin InterchangeEditor
LogPluginManager: Mounting Engine plugin Interchange
LogPluginManager: Mounting Engine plugin TcpMessaging
LogPluginManager: Mounting Engine plugin UdpMessaging
LogPluginManager: Mounting Engine plugin ActorSequence
LogPluginManager: Mounting Engine plugin TemplateSequence
LogPluginManager: Mounting Engine plugin SequencerScripting
LogPluginManager: Mounting Engine plugin LevelSequenceEditor
LogPluginManager: Mounting Engine plugin EOSShared
LogPluginManager: Mounting Engine plugin OnlineBase
LogPluginManager: Mounting Engine plugin OnlineServices
LogPluginManager: Mounting Engine plugin OnlineSubsystem
LogPluginManager: Mounting Engine plugin OnlineSubsystemNull
LogPluginManager: Mounting Engine plugin OnlineSubsystemUtils
LogPluginManager: Mounting Engine plugin ActorLayerUtilities
LogPluginManager: Mounting Engine plugin AndroidDeviceProfileSelector
LogPluginManager: Mounting Engine plugin AndroidFileServer
LogPluginManager: Mounting Engine plugin AndroidPermission
LogPluginManager: Mounting Engine plugin AndroidMoviePlayer
LogPluginManager: Mounting Engine plugin AppleImageUtils
LogPluginManager: Mounting Engine plugin AppleMoviePlayer
LogPluginManager: Mounting Engine plugin ArchVisCharacter
LogPluginManager: Mounting Engine plugin AssetTags
LogPluginManager: Mounting Engine plugin AudioSynesthesia
LogPluginManager: Mounting Engine plugin AudioWidgets
LogPluginManager: Mounting Engine plugin AudioCapture
LogPluginManager: Mounting Engine plugin ChunkDownloader
LogPluginManager: Mounting Engine plugin CustomMeshComponent
LogPluginManager: Mounting Engine plugin DataRegistry
LogPluginManager: Mounting Engine plugin CableComponent
LogPluginManager: Mounting Engine plugin ComputeFramework
LogPluginManager: Mounting Engine plugin GameplayAbilities
LogPluginManager: Mounting Engine plugin ExampleDeviceProfileSelector
LogPluginManager: Mounting Engine plugin GeometryCache
LogPluginManager: Mounting Engine plugin GooglePAD
LogPluginManager: Mounting Engine plugin HairStrands
LogPluginManager: Mounting Engine plugin GeometryProcessing
LogPluginManager: Mounting Engine plugin InputDebugging
LogPluginManager: Mounting Engine plugin IOSDeviceProfileSelector
LogPluginManager: Mounting Engine plugin GoogleCloudMessaging
LogPluginManager: Mounting Engine plugin LinuxDeviceProfileSelector
LogPluginManager: Mounting Engine plugin LocationServicesBPLibrary
LogPluginManager: Mounting Engine plugin MassGameplay
LogPluginManager: Mounting Engine plugin MeshModelingToolset
LogPluginManager: Mounting Engine plugin Metasound
LogPluginManager: Mounting Engine plugin MobilePatchingUtils
LogPluginManager: Mounting Engine plugin MsQuic
LogPluginManager: Mounting Engine plugin PropertyAccessEditor
LogPluginManager: Mounting Engine plugin PropertyBindingUtils
LogPluginManager: Mounting Engine plugin ResonanceAudio
LogPluginManager: Mounting Engine plugin RigVM
LogPluginManager: Mounting Engine plugin ProceduralMeshComponent
LogPluginManager: Mounting Engine plugin SignificanceManager
LogPluginManager: Mounting Engine plugin ReplicationGraph
LogPluginManager: Mounting Engine plugin SoundFields
LogPluginManager: Mounting Engine plugin SmartObjects
LogPluginManager: Mounting Engine plugin StateTree
LogPluginManager: Mounting Engine plugin WaveTable
LogPluginManager: Mounting Engine plugin WebMMoviePlayer
LogPluginManager: Mounting Engine plugin WorldConditions
LogPluginManager: Mounting Engine plugin WindowsMoviePlayer
LogPluginManager: Mounting Engine plugin WindowsDeviceProfileSelector
LogPluginManager: Mounting Engine plugin ZoneGraphAnnotations
LogPluginManager: Mounting Engine plugin ZoneGraph
LogPluginManager: Mounting Engine plugin Synthesis
LogPluginManager: Mounting Engine plugin CameraCalibrationCore
LogPluginManager: Mounting Engine plugin Takes
LogPluginManager: Mounting Engine plugin Paper2D
LogPluginManager: Mounting Engine plugin AISupport
LogPluginManager: Mounting Engine plugin EnvironmentQueryEditor
LogPluginManager: Mounting Engine plugin CodeLiteSourceCodeAccess
LogPluginManager: Mounting Engine plugin CLionSourceCodeAccess
LogPluginManager: Mounting Engine plugin GitSourceControl
LogPluginManager: Mounting Engine plugin KDevelopSourceCodeAccess
LogPluginManager: Mounting Engine plugin DumpGPUServices
LogPluginManager: Mounting Engine plugin PerforceSourceControl
LogPluginManager: Mounting Engine plugin NullSourceCodeAccess
LogPluginManager: Mounting Engine plugin AnimationSharing
LogPluginManager: Mounting Engine plugin N10XSourceCodeAccess
LogPluginManager: Mounting Engine plugin NamingTokens
LogPluginManager: Mounting Engine plugin ProjectLauncher
LogPluginManager: Mounting Engine plugin PluginUtils
LogPluginManager: Mounting Engine plugin PropertyAccessNode
LogPluginManager: Mounting Engine plugin PlasticSourceControl
LogPluginManager: Mounting Engine plugin PixWinPlugin
LogPluginManager: Mounting Engine plugin RiderSourceCodeAccess
LogPluginManager: Mounting Engine plugin UObjectPlugin
LogPluginManager: Mounting Engine plugin RenderDocPlugin
LogPluginManager: Mounting Engine plugin SubversionSourceControl
LogPluginManager: Mounting Engine plugin TextureFormatOodle
LogPluginManager: Mounting Engine plugin VisualStudioCodeSourceCodeAccess
LogPluginManager: Mounting Engine plugin VisualStudioSourceCodeAccess
LogPluginManager: Mounting Engine plugin XCodeSourceCodeAccess
LogPluginManager: Mounting Engine plugin AdvancedRenamer
LogPluginManager: Mounting Engine plugin AutomationUtils
LogPluginManager: Mounting Engine plugin BackChannel
LogPluginManager: Mounting Engine plugin ChaosEditor
LogPluginManager: Mounting Engine plugin ChaosCaching
LogPluginManager: Mounting Engine plugin ChaosNiagara
LogPluginManager: Mounting Engine plugin ChaosUserDataPT
LogPluginManager: Mounting Engine plugin ChaosSolverPlugin
LogPluginManager: Mounting Engine plugin CharacterAI
LogPluginManager: Mounting Engine plugin ChaosVehiclesPlugin
LogPluginManager: Mounting Engine plugin Dataflow
LogPluginManager: Mounting Engine plugin EditorDataStorage
LogPluginManager: Mounting Engine plugin EditorDataStorageFeatures
LogPluginManager: Mounting Engine plugin EditorPerformance
LogPluginManager: Mounting Engine plugin FieldSystemPlugin
LogPluginManager: Mounting Engine plugin EditorTelemetry
LogPluginManager: Mounting Engine plugin Fracture
LogPluginManager: Mounting Engine plugin FullBodyIK
LogPluginManager: Mounting Engine plugin GeometryFlow
LogPluginManager: Mounting Engine plugin GeometryCollectionPlugin
LogPluginManager: Mounting Engine plugin GeometryDataflow
LogPluginManager: Mounting Engine plugin TargetingSystem
LogPluginManager: Mounting Engine plugin LevelSequenceNavigatorBridge
LogPluginManager: Mounting Engine plugin MeshModelingToolsetExp
LogPluginManager: Mounting Engine plugin LowLevelNetTrace
LogPluginManager: Mounting Engine plugin LocalizableMessage
LogPluginManager: Mounting Engine plugin NFORDenoise
LogPluginManager: Mounting Engine plugin PlatformCrypto
LogPluginManager: Mounting Engine plugin PlanarCut
LogPluginManager: Mounting Engine plugin PythonScriptPlugin
LogPluginManager: Mounting Engine plugin RuntimeTelemetry
LogPluginManager: Mounting Engine plugin SequenceNavigator
LogPluginManager: Mounting Engine plugin SkeletalReduction
LogPluginManager: Mounting Engine plugin ToolPresets
LogPluginManager: Mounting Engine plugin AvfMedia
LogPluginManager: Mounting Engine plugin AndroidMedia
LogPluginManager: Mounting Engine plugin ImgMedia
LogPluginManager: Mounting Engine plugin MediaCompositing
LogPluginManager: Mounting Engine plugin MediaPlayerEditor
LogPluginManager: Mounting Engine plugin WmfMedia
LogPluginManager: Mounting Engine plugin NNEDenoiser
LogPluginManager: Mounting Engine plugin WebMMedia
LogPluginManager: Mounting Engine plugin MetaHumanSDK
LogPluginManager: Mounting Engine plugin LauncherChunkInstaller
LogPluginManager: Mounting Engine plugin NNERuntimeORT
LogPluginManager: Mounting Engine plugin InterchangeTests
LogPluginManager: Mounting Engine plugin MediaPlate
LogPluginManager: Mounting Engine plugin ContentBrowserFileDataSource
LogPluginManager: Mounting Engine plugin PortableObjectFileDataSource
LogPluginManager: Mounting Engine plugin ContentBrowserClassDataSource
LogPluginManager: Mounting Engine plugin LightMixer
LogPluginManager: Mounting Engine plugin ContentBrowserAssetDataSource
LogPluginManager: Mounting Engine plugin ObjectMixer
LogPluginManager: Mounting Engine plugin XInputDevice
LogPluginManager: Mounting Engine plugin OnlineSubsystemIOS
LogPluginManager: Mounting Engine plugin CompositeCore
LogPluginManager: Mounting Engine plugin BaseCharacterFXEditor
LogPluginManager: Mounting Engine plugin OnlineSubsystemGooglePlay
LogPluginManager: Mounting Project plugin UnrealMCP
SourceControl: Revision control is disabled
SourceControl: Revision control is disabled
SourceControl: Revision control is disabled
LogEOSShared: Loaded "C:/Program Files/Epic Games/UE_5.6/Engine/Binaries/Win64/EOSSDK-Win64-Shipping.dll"
LogEOSShared: FEOSSDKManager::Initialize Initializing EOSSDK Version:1.17.0-41373641
LogInit: Using libcurl 8.12.1
LogInit:  - built for Windows
LogInit:  - supports SSL with OpenSSL/1.1.1t
LogInit:  - supports HTTP deflate (compression) using libz 1.3
LogInit:  - other features:
LogInit:      CURL_VERSION_SSL
LogInit:      CURL_VERSION_LIBZ
LogInit:      CURL_VERSION_IPV6
LogInit:      CURL_VERSION_ASYNCHDNS
LogInit:      CURL_VERSION_LARGEFILE
LogInit:      CURL_VERSION_TLSAUTH_SRP
LogInit:      CURL_VERSION_HTTP2
LogInit:  CurlRequestOptions (configurable via config and command line):
LogInit:  - bVerifyPeer = true  - Libcurl will verify peer certificate
LogInit:  - bUseHttpProxy = false  - Libcurl will NOT use HTTP proxy
LogInit:  - bDontReuseConnections = false  - Libcurl will reuse connections
LogInit:  - MaxHostConnections = 16  - Libcurl will limit the number of connections to a host
LogInit:  - LocalHostAddr = Default
LogInit:  - BufferSize = 65536
LogInit: CreateHttpThread using FCurlMultiPollEventLoopHttpThread
LogInit: Creating http thread with maximum 256 concurrent requests
LogInit: WinSock: version 1.1 (2.2), MaxSocks=32767, MaxUdp=65467
LogOnline: OSS: Created online subsystem instance for: NULL
LogOnline: OSS: TryLoadSubsystemAndSetDefault: Loaded subsystem for type [NULL]
LogWindows: Failed to load 'WinPixGpuCapturer.dll' (GetLastError=126)
LogWindows: File 'WinPixGpuCapturer.dll' does not exist
PixWinPlugin: PIX capture plugin failed to initialize! Check that the process is launched from PIX.
LogConfig: Applying CVar settings from Section [/Script/RenderDocPlugin.RenderDocPluginSettings] File [Engine]
RenderDocPlugin: Display: RenderDoc plugin will not be loaded. Use '-AttachRenderDoc' on the cmd line or enable 'renderdoc.AutoAttach' in the plugin settings.
LogStudioTelemetry: Started StudioTelemetry Session
LogNFORDenoise: NFORDenoise function starting up
LogConfig: Applying CVar settings from Section [/Script/CompositeCore.CompositeCorePluginSettings] File [Engine]
LogInit: ExecutableName: UnrealEditor.exe
LogInit: Build: ++UE5+Release-5.6-***********
LogInit: Platform=WindowsEditor
LogInit: MachineId=8bb1964343e8298f803f869f44351803
LogInit: DeviceId=
LogInit: Engine Version: 5.6.1-44394996+++UE5+Release-5.6
LogInit: Compatible Engine Version: 5.6.0-43139311+++UE5+Release-5.6
LogInit: Net CL: 43139311
LogInit: OS: Windows 11 (24H2) [10.0.26100.4946] (), CPU: 13th Gen Intel(R) Core(TM) i5-1345U, GPU: Intel(R) Iris(R) Xe Graphics
LogInit: Compiled (64-bit): Jul 28 2025 20:53:34
LogInit: Architecture: x64
LogInit: Compiled with Visual C++: 19.38.33130.00
LogInit: Build Configuration: Development
LogInit: Branch Name: ++UE5+Release-5.6
LogInit: Command Line: 
LogInit: Base Directory: C:/Program Files/Epic Games/UE_5.6/Engine/Binaries/Win64/
LogInit: Allocator: Mimalloc
LogInit: Installed Engine Build: 1
LogInit: This binary is optimized with LTO: no, PGO: no, instrumented for PGO data collection: no
LogDevObjectVersion: Number of dev versions registered: 37
LogDevObjectVersion:   Dev-Blueprints (B0D832E4-1F89-4F0D-ACCF-7EB736FD4AA2): 10
LogDevObjectVersion:   Dev-Build (E1C64328-A22C-4D53-A36C-8E866417BD8C): 0
LogDevObjectVersion:   Dev-Core (375EC13C-06E4-48FB-B500-84F0262A717E): 4
LogDevObjectVersion:   Dev-Editor (E4B068ED-F494-42E9-A231-DA0B2E46BB41): 40
LogDevObjectVersion:   Dev-Framework (CFFC743F-43B0-4480-9391-14DF171D2073): 37
LogDevObjectVersion:   Dev-Mobile (B02B49B5-BB20-44E9-A304-32B752E40360): 3
LogDevObjectVersion:   Dev-Networking (A4E4105C-59A1-49B5-A7C5-40C4547EDFEE): 0
LogDevObjectVersion:   Dev-Online (39C831C9-5AE6-47DC-9A44-9C173E1C8E7C): 0
LogDevObjectVersion:   Dev-Physics (78F01B33-EBEA-4F98-B9B4-84EACCB95AA2): 20
LogDevObjectVersion:   Dev-Platform (6631380F-2D4D-43E0-8009-CF276956A95A): 0
LogDevObjectVersion:   Dev-Rendering (12F88B9F-8875-4AFC-A67C-D90C383ABD29): 49
LogDevObjectVersion:   Dev-Sequencer (7B5AE74C-D270-4C10-A958-57980B212A5A): 13
LogDevObjectVersion:   Dev-VR (D7296918-1DD6-4BDD-9DE2-64A83CC13884): 3
LogDevObjectVersion:   Dev-LoadTimes (C2A15278-BFE7-4AFE-6C17-90FF531DF755): 1
LogDevObjectVersion:   Private-Geometry (6EACA3D4-40EC-4CC1-B786-8BED09428FC5): 3
LogDevObjectVersion:   Dev-AnimPhys (29E575DD-E0A3-4627-9D10-D276232CDCEA): 17
LogDevObjectVersion:   Dev-Anim (AF43A65D-7FD3-4947-9873-3E8ED9C1BB05): 15
LogDevObjectVersion:   Dev-ReflectionCapture (6B266CEC-1EC7-4B8F-A30B-E4D90942FC07): 1
LogDevObjectVersion:   Dev-Automation (0DF73D61-A23F-47EA-B727-89E90C41499A): 1
LogDevObjectVersion:   FortniteMain (601D1886-AC64-4F84-AA16-D3DE0DEAC7D6): 207
LogDevObjectVersion:   FortniteValkyrie (8DBC2C5B-54A7-43E0-A768-FCBB7DA29060): 8
LogDevObjectVersion:   FortniteSeason (5B4C06B7-2463-4AF8-805B-BF70CDF5D0DD): 13
LogDevObjectVersion:   FortniteRelease (E7086368-6B23-4C58-8439-1B7016265E91): 17
LogDevObjectVersion:   Dev-Enterprise (9DFFBCD6-494F-0158-E221-12823C92A888): 11
LogDevObjectVersion:   Dev-Niagara (F2AED0AC-9AFE-416F-8664-AA7FFA26D6FC): 1
LogDevObjectVersion:   Dev-Destruction (174F1F0B-B4C6-45A5-B13F-2EE8D0FB917D): 10
LogDevObjectVersion:   Dev-Physics-Ext (35F94A83-E258-406C-A318-09F59610247C): 41
LogDevObjectVersion:   Dev-PhysicsMaterial-Chaos (B68FC16E-8B1B-42E2-B453-215C058844FE): 1
LogDevObjectVersion:   Dev-CineCamera (B2E18506-4273-CFC2-A54E-F4BB758BBA07): 1
LogDevObjectVersion:   Dev-VirtualProduction (64F58936-FD1B-42BA-BA96-7289D5D0FA4E): 1
LogDevObjectVersion:   UE5-Main (697DD581-E64F-41AB-AA4A-51ECBEB7B628): 121
LogDevObjectVersion:   UE5-Release (D89B5E42-24BD-4D46-8412-ACA8DF641779): 56
LogDevObjectVersion:   UE5-SpecialProject (59DA5D52-1232-4948-B878-597870B8E98B): 9
LogDevObjectVersion:   Dev-MediaFramework (6F0ED827-A609-4895-9C91-998D90180EA4): 2
LogDevObjectVersion:   Dev-NaniteResearch (30D58BE3-95EA-4282-A6E3-B159D8EBB06A): 1
LogDevObjectVersion:   Dev-ComputeFramework (6304A3E7-0059-4F59-8CFC-21BD7721FD4E): 0
LogDevObjectVersion:   LensFileVersion (8652A554-966A-466C-9FD7-1C6DD61B1ADB): 1
LogInit: Presizing for max 25165824 objects, including 0 objects not considered by GC.
LogInit: Object subsystem initialized
LogConfig: Set CVar [[con.DebugEarlyDefault:1]]
LogConfig: CVar [[con.DebugLateDefault:1]] deferred - dummy variable created
LogConfig: CVar [[con.DebugLateCheat:1]] deferred - dummy variable created
LogConfig: CVar [[LogNamedEventFilters:Frame *]] deferred - dummy variable created
LogConfig: Set CVar [[r.setres:1280x720]]
LogConfig: CVar [[framepro.ScopeMinTimeMicroseconds:10]] deferred - dummy variable created
LogConfig: Set CVar [[fx.NiagaraAllowRuntimeScalabilityChanges:1]]
LogConfig: CVar [[QualityLevelMapping:high]] deferred - dummy variable created
LogConfig: CVar [[r.Occlusion.SingleRHIThreadStall:1]] deferred - dummy variable created
LogConfig: Set CVar [[r.Nanite.Streaming.ReservedResources:1]]
LogConfig: CVar [[D3D12.Bindless.ResourceDescriptorHeapSize:32768]] deferred - dummy variable created
LogConfig: CVar [[D3D12.Bindless.SamplerDescriptorHeapSize:2048]] deferred - dummy variable created
LogConfig: Set CVar [[r.PSOPrecache.GlobalShaders:1]]
LogConfig: Set CVar [[r.VRS.EnableSoftware:1]]
LogConfig: Set CVar [[r.VRS.ContrastAdaptiveShading:1]]
[2025.08.26-02.43.12:552][  0]LogConfig: Set CVar [[r.VSync:0]]
[2025.08.26-02.43.12:552][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.RendererSettings] File [Engine]
[2025.08.26-02.43.12:552][  0]LogConfig: CVar [[VisualizeCalibrationColorMaterialPath:/Engine/EngineMaterials/PPM_DefaultCalibrationColor.PPM_DefaultCalibrationColor]] deferred - dummy variable created
[2025.08.26-02.43.12:552][  0]LogConfig: CVar [[VisualizeCalibrationGrayscaleMaterialPath:/Engine/EngineMaterials/PPM_DefaultCalibrationGrayscale.PPM_DefaultCalibrationGrayscale]] deferred - dummy variable created
[2025.08.26-02.43.12:552][  0]LogConfig: Set CVar [[r.GPUCrashDebugging:0]]
[2025.08.26-02.43.12:552][  0]LogConfig: CVar [[MaxSkinBones:(Default=65536,PerPlatform=(("Mobile", 256)))]] deferred - dummy variable created
[2025.08.26-02.43.12:552][  0]LogConfig: Set CVar [[r.Shaders.RemoveUnusedInterpolators:1]]
[2025.08.26-02.43.12:552][  0]LogConfig: Set CVar [[r.Shadow.DetectVertexShaderLayerAtRuntime:1]]
[2025.08.26-02.43.12:552][  0]LogConfig: Set CVar [[r.AllowStaticLighting:0]]
[2025.08.26-02.43.12:552][  0]LogConfig: Set CVar [[r.GenerateMeshDistanceFields:1]]
[2025.08.26-02.43.12:552][  0]LogConfig: Set CVar [[r.DynamicGlobalIlluminationMethod:1]]
[2025.08.26-02.43.12:552][  0]LogConfig: Set CVar [[r.ReflectionMethod:1]]
[2025.08.26-02.43.12:552][  0]LogConfig: Set CVar [[r.SkinCache.CompileShaders:1]]
[2025.08.26-02.43.12:552][  0]LogConfig: Set CVar [[r.RayTracing:1]]
[2025.08.26-02.43.12:552][  0]LogConfig: Set CVar [[r.RayTracing.RayTracingProxies.ProjectEnabled:1]]
[2025.08.26-02.43.12:552][  0]LogConfig: Set CVar [[r.Shadow.Virtual.Enable:1]]
[2025.08.26-02.43.12:552][  0]LogConfig: Set CVar [[r.DefaultFeature.AutoExposure.ExtendDefaultLuminanceRange:1]]
[2025.08.26-02.43.12:552][  0]LogConfig: Set CVar [[r.DefaultFeature.LocalExposure.HighlightContrastScale:0.8]]
[2025.08.26-02.43.12:552][  0]LogConfig: Set CVar [[r.DefaultFeature.LocalExposure.ShadowContrastScale:0.8]]
[2025.08.26-02.43.12:552][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.RendererOverrideSettings] File [Engine]
[2025.08.26-02.43.12:552][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.StreamingSettings] File [Engine]
[2025.08.26-02.43.12:552][  0]LogConfig: Set CVar [[s.MinBulkDataSizeForAsyncLoading:131072]]
[2025.08.26-02.43.12:552][  0]LogConfig: Set CVar [[s.AsyncLoadingThreadEnabled:1]]
[2025.08.26-02.43.12:552][  0]LogConfig: Set CVar [[s.EventDrivenLoaderEnabled:1]]
[2025.08.26-02.43.12:552][  0]LogConfig: Set CVar [[s.WarnIfTimeLimitExceeded:0]]
[2025.08.26-02.43.12:552][  0]LogConfig: Set CVar [[s.TimeLimitExceededMultiplier:1.5]]
[2025.08.26-02.43.12:552][  0]LogConfig: Set CVar [[s.TimeLimitExceededMinTime:0.005]]
[2025.08.26-02.43.12:552][  0]LogConfig: Set CVar [[s.UseBackgroundLevelStreaming:1]]
[2025.08.26-02.43.12:552][  0]LogConfig: Set CVar [[s.PriorityAsyncLoadingExtraTime:15.0]]
[2025.08.26-02.43.12:552][  0]LogConfig: Set CVar [[s.LevelStreamingActorsUpdateTimeLimit:5.0]]
[2025.08.26-02.43.12:552][  0]LogConfig: Set CVar [[s.PriorityLevelStreamingActorsUpdateExtraTime:5.0]]
[2025.08.26-02.43.12:552][  0]LogConfig: Set CVar [[s.LevelStreamingComponentsRegistrationGranularity:10]]
[2025.08.26-02.43.12:552][  0]LogConfig: Set CVar [[s.UnregisterComponentsTimeLimit:1.0]]
[2025.08.26-02.43.12:552][  0]LogConfig: Set CVar [[s.LevelStreamingComponentsUnregistrationGranularity:5]]
[2025.08.26-02.43.12:552][  0]LogConfig: CVar [[s.MaxPackageSummarySize:16384]] deferred - dummy variable created
[2025.08.26-02.43.12:552][  0]LogConfig: Set CVar [[s.FlushStreamingOnExit:1]]
[2025.08.26-02.43.12:552][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__SoundBase]] deferred - dummy variable created
[2025.08.26-02.43.12:552][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__MaterialInterface]] deferred - dummy variable created
[2025.08.26-02.43.12:552][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__DeviceProfileManager]] deferred - dummy variable created
[2025.08.26-02.43.12:552][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.GarbageCollectionSettings] File [Engine]
[2025.08.26-02.43.12:552][  0]LogConfig: Set CVar [[gc.MaxObjectsNotConsideredByGC:1]]
[2025.08.26-02.43.12:552][  0]LogConfig: Set CVar [[gc.FlushStreamingOnGC:0]]
[2025.08.26-02.43.12:552][  0]LogConfig: Set CVar [[gc.NumRetriesBeforeForcingGC:10]]
[2025.08.26-02.43.12:552][  0]LogConfig: Set CVar [[gc.AllowParallelGC:1]]
[2025.08.26-02.43.12:552][  0]LogConfig: Set CVar [[gc.TimeBetweenPurgingPendingKillObjects:61.1]]
[2025.08.26-02.43.12:552][  0]LogConfig: Set CVar [[gc.MaxObjectsInEditor:25165824]]
[2025.08.26-02.43.12:552][  0]LogConfig: Set CVar [[gc.IncrementalBeginDestroyEnabled:1]]
[2025.08.26-02.43.12:552][  0]LogConfig: Set CVar [[gc.CreateGCClusters:1]]
[2025.08.26-02.43.12:552][  0]LogConfig: Set CVar [[gc.MinGCClusterSize:5]]
[2025.08.26-02.43.12:552][  0]LogConfig: Set CVar [[gc.AssetClustreringEnabled:0]]
[2025.08.26-02.43.12:552][  0]LogConfig: Set CVar [[gc.ActorClusteringEnabled:0]]
[2025.08.26-02.43.12:552][  0]LogConfig: Set CVar [[gc.VerifyUObjectsAreNotFGCObjects:0]]
[2025.08.26-02.43.12:552][  0]LogConfig: Set CVar [[gc.GarbageEliminationEnabled:1]]
[2025.08.26-02.43.12:552][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.NetworkSettings] File [Engine]
[2025.08.26-02.43.12:552][  0]LogConfig: CVar [[NetworkEmulationProfiles:(ProfileName="Average",ToolTip="Simulates average internet conditions")]] deferred - dummy variable created
[2025.08.26-02.43.12:552][  0]LogConfig: CVar [[NetworkEmulationProfiles:(ProfileName="Bad",ToolTip="Simulates laggy internet conditions")]] deferred - dummy variable created
[2025.08.26-02.43.12:552][  0]LogConfig: Applying CVar settings from Section [/Script/UnrealEd.CookerSettings] File [Engine]
[2025.08.26-02.43.12:552][  0]LogConfig: CVar [[DefaultASTCQualityBySpeed:2]] deferred - dummy variable created
[2025.08.26-02.43.12:552][  0]LogConfig: CVar [[DefaultASTCQualityBySize:3]] deferred - dummy variable created
[2025.08.26-02.43.12:552][  0]LogConfig: CVar [[DefaultASTCQualityBySizeHQ:4]] deferred - dummy variable created
[2025.08.26-02.43.12:552][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:WidgetBlueprint]] deferred - dummy variable created
[2025.08.26-02.43.12:552][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:GroupActor]] deferred - dummy variable created
[2025.08.26-02.43.12:552][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:MetaData]] deferred - dummy variable created
[2025.08.26-02.43.12:552][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ObjectRedirector]] deferred - dummy variable created
[2025.08.26-02.43.12:552][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NavMeshRenderingComponent]] deferred - dummy variable created
[2025.08.26-02.43.12:552][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ReflectionCaptureComponent]] deferred - dummy variable created
[2025.08.26-02.43.12:552][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:TextRenderComponent]] deferred - dummy variable created
[2025.08.26-02.43.12:552][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:Font]] deferred - dummy variable created
[2025.08.26-02.43.12:552][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:InterpCurveEdSetup]] deferred - dummy variable created
[2025.08.26-02.43.12:552][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:MaterialExpression]] deferred - dummy variable created
[2025.08.26-02.43.12:552][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NiagaraEmitter]] deferred - dummy variable created
[2025.08.26-02.43.12:552][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NiagaraScript]] deferred - dummy variable created
[2025.08.26-02.43.12:552][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleEmitter]] deferred - dummy variable created
[2025.08.26-02.43.12:552][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleLODLevel]] deferred - dummy variable created
[2025.08.26-02.43.12:552][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleModule]] deferred - dummy variable created
[2025.08.26-02.43.12:552][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:SubUVAnimation]] deferred - dummy variable created
[2025.08.26-02.43.12:552][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:SoundNode]] deferred - dummy variable created
[2025.08.26-02.43.12:552][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:GameplayEffectUIData]] deferred - dummy variable created
[2025.08.26-02.43.12:552][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:AmbientSound]] deferred - dummy variable created
[2025.08.26-02.43.12:552][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:WidgetBlueprint]] deferred - dummy variable created
[2025.08.26-02.43.12:552][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:GroupActor]] deferred - dummy variable created
[2025.08.26-02.43.12:552][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:MetaData]] deferred - dummy variable created
[2025.08.26-02.43.12:552][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:ObjectRedirector]] deferred - dummy variable created
[2025.08.26-02.43.12:552][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:InterpCurveEdSetup]] deferred - dummy variable created
[2025.08.26-02.43.12:552][  0]LogConfig: CVar [[VersionedIntRValues:r.AllowStaticLighting]] deferred - dummy variable created
[2025.08.26-02.43.12:552][  0]LogConfig: CVar [[VersionedIntRValues:r.MaterialEditor.LWCTruncateMode]] deferred - dummy variable created
[2025.08.26-02.43.12:552][  0]LogConfig: CVar [[VersionedIntRValues:r.GBuffer]] deferred - dummy variable created
[2025.08.26-02.43.12:552][  0]LogConfig: CVar [[VersionedIntRValues:r.VelocityOutputPass]] deferred - dummy variable created
[2025.08.26-02.43.12:552][  0]LogConfig: CVar [[VersionedIntRValues:r.SelectiveBasePassOutputs]] deferred - dummy variable created
[2025.08.26-02.43.12:552][  0]LogConfig: CVar [[VersionedIntRValues:r.DBuffer]] deferred - dummy variable created
[2025.08.26-02.43.12:552][  0]LogConfig: CVar [[VersionedIntRValues:r.Mobile.DBuffer]] deferred - dummy variable created
[2025.08.26-02.43.12:552][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.Symbols]] deferred - dummy variable created
[2025.08.26-02.43.12:552][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.GenerateSymbols]] deferred - dummy variable created
[2025.08.26-02.43.12:552][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.WriteSymbols]] deferred - dummy variable created
[2025.08.26-02.43.12:552][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.AllowUniqueSymbols]] deferred - dummy variable created
[2025.08.26-02.43.12:552][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.ExtraData]] deferred - dummy variable created
[2025.08.26-02.43.12:552][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.Optimize]] deferred - dummy variable created
[2025.08.26-02.43.12:552][  0]LogConfig: CVar [[VersionedIntRValues:r.CompileShadersForDevelopment]] deferred - dummy variable created
[2025.08.26-02.43.12:552][  0]LogConfig: CVar [[VersionedIntRValues:r.MobileHDR]] deferred - dummy variable created
[2025.08.26-02.43.12:552][  0]LogConfig: CVar [[VersionedIntRValues:r.UsePreExposure]] deferred - dummy variable created
[2025.08.26-02.43.12:554][  0]LogCsvProfiler: Display: Metadata set : systemresolution.resx="1536"
[2025.08.26-02.43.12:554][  0]LogCsvProfiler: Display: Metadata set : systemresolution.resy="864"
[2025.08.26-02.43.12:554][  0]LogConfig: Applying CVar settings from Section [ViewDistanceQuality@3] File [Scalability]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.SkeletalMeshLODBias:0]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.ViewDistanceScale:1.0]]
[2025.08.26-02.43.12:554][  0]LogConfig: Applying CVar settings from Section [AntiAliasingQuality@3] File [Scalability]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.FXAA.Quality:4]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.TemporalAA.Quality:2]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.TSR.History.R11G11B10:1]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.TSR.History.ScreenPercentage:200]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.TSR.History.UpdateQuality:3]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.TSR.ShadingRejection.Flickering:1]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.TSR.RejectionAntiAliasingQuality:2]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.TSR.ReprojectionField:1]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.TSR.Resurrection:1]]
[2025.08.26-02.43.12:554][  0]LogConfig: Applying CVar settings from Section [ShadowQuality@3] File [Scalability]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.LightFunctionQuality:1]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.ShadowQuality:5]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.Shadow.CSM.MaxCascades:10]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.Shadow.MaxResolution:2048]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.Shadow.MaxCSMResolution:2048]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.Shadow.RadiusThreshold:0.01]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.Shadow.DistanceScale:1.0]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.Shadow.CSM.TransitionScale:1.0]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.Shadow.PreShadowResolutionFactor:1.0]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.DistanceFieldShadowing:1]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.VolumetricFog:1]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.VolumetricFog.GridPixelSize:8]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.VolumetricFog.GridSizeZ:128]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.VolumetricFog.HistoryMissSupersampleCount:4]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.LightMaxDrawDistanceScale:1]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.CapsuleShadows:1]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.Shadow.Virtual.MaxPhysicalPages:4096]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasDirectional:-1.5]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasDirectionalMoving:-1.5]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasLocal:0.0]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasLocalMoving:1.0]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.RayCountDirectional:8]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.SamplesPerRayDirectional:4]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.RayCountLocal:8]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.SamplesPerRayLocal:4]]
[2025.08.26-02.43.12:554][  0]LogConfig: Applying CVar settings from Section [GlobalIlluminationQuality@3] File [Scalability]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.DistanceFieldAO:1]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.SkylightIntensityMultiplier:1.0]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.AOQuality:2]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.Lumen.DiffuseIndirect.Allow:1]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.LumenScene.DirectLighting.MaxLightsPerTile:8]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.LumenScene.DirectLighting.UpdateFactor:32]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.LumenScene.Radiosity.UpdateFactor:64]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.LumenScene.Radiosity.ProbeSpacing:4]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.LumenScene.Radiosity.HemisphereProbeResolution:4]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.Lumen.TraceMeshSDFs.Allow:1]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.RadianceCache.ProbeResolution:32]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.RadianceCache.NumProbesToTraceBudget:100]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.DownsampleFactor:16]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.NumAdaptiveProbes:8]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.TracingOctahedronResolution:8]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.IrradianceFormat:0]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.StochasticInterpolation:0]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.FullResolutionJitterWidth:1]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.TwoSidedFoliageBackfaceDiffuse:1]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.ScreenTraces.HZBTraversal.FullResDepth:1]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.ShortRangeAO.HardwareRayTracing:0]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.ShortRangeAO.BentNormal:1]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.GridPixelSize:32]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.TraceFromVolume:1]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.TracingOctahedronResolution:3]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.RadianceCache.ProbeResolution:8]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.RadianceCache.NumProbesToTraceBudget:70]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.RayTracing.Scene.BuildMode:1]]
[2025.08.26-02.43.12:554][  0]LogConfig: Applying CVar settings from Section [ReflectionQuality@3] File [Scalability]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.SSR.Quality:3]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.SSR.HalfResSceneColor:0]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.Lumen.Reflections.Allow:1]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.Lumen.Reflections.DownsampleFactor:1]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.Lumen.Reflections.MaxRoughnessToTraceForFoliage:0.4]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.MaxRoughnessToEvaluateRoughSpecularForFoliage:0.8]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.Lumen.Reflections.ScreenSpaceReconstruction.NumSamples:5]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.Lumen.Reflections.ScreenSpaceReconstruction.MinWeight:0]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyReflections.FrontLayer.Allow:1]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyReflections.FrontLayer.Enable:0]]
[2025.08.26-02.43.12:554][  0]LogConfig: Applying CVar settings from Section [PostProcessQuality@3] File [Scalability]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.MotionBlurQuality:4]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.MotionBlur.HalfResGather:0]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.AmbientOcclusionMipLevelFactor:0.4]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.AmbientOcclusionMaxQuality:100]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.AmbientOcclusionLevels:-1]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.AmbientOcclusionRadiusScale:1.0]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.DepthOfFieldQuality:2]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.RenderTargetPoolMin:400]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.LensFlareQuality:2]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.SceneColorFringeQuality:1]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.EyeAdaptationQuality:2]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.BloomQuality:5]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.Bloom.ScreenPercentage:50.000]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.FastBlurThreshold:100]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.Upscale.Quality:3]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.LightShaftQuality:1]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.Filter.SizeScale:1]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.Tonemapper.Quality:5]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.DOF.Gather.ResolutionDivisor:2         ; lower gathering resolution]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.DOF.Gather.AccumulatorQuality:1        ; higher gathering accumulator quality]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.DOF.Gather.PostfilterMethod:1          ; Median3x3 postfilering method]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.DOF.Gather.EnableBokehSettings:0       ; no bokeh simulation when gathering]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.DOF.Gather.RingCount:4                 ; medium number of samples when gathering]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.DOF.Scatter.ForegroundCompositing:1    ; additive foreground scattering]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.DOF.Scatter.BackgroundCompositing:2    ; additive background scattering]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.DOF.Scatter.EnableBokehSettings:1      ; bokeh simulation when scattering]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.DOF.Scatter.MaxSpriteRatio:0.1         ; only a maximum of 10% of scattered bokeh]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.DOF.Recombine.Quality:1                ; cheap slight out of focus]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.DOF.Recombine.EnableBokehSettings:0    ; no bokeh simulation on slight out of focus]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.DOF.TemporalAAQuality:1                ; more stable temporal accumulation]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.DOF.Kernel.MaxForegroundRadius:0.025]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.DOF.Kernel.MaxBackgroundRadius:0.025]]
[2025.08.26-02.43.12:554][  0]LogConfig: Applying CVar settings from Section [TextureQuality@3] File [Scalability]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.Streaming.MipBias:0]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.Streaming.AmortizeCPUToGPUCopy:0]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.Streaming.MaxNumTexturesToStreamPerFrame:0]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.Streaming.Boost:1]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.MaxAnisotropy:8]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.VT.MaxAnisotropy:8]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.Streaming.LimitPoolSizeToVRAM:0]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.Streaming.PoolSize:1000]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.Streaming.MaxEffectiveScreenSize:0]]
[2025.08.26-02.43.12:554][  0]LogConfig: Applying CVar settings from Section [EffectsQuality@3] File [Scalability]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.TranslucencyLightingVolumeDim:64]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.RefractionQuality:2]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.SceneColorFormat:4]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.DetailMode:3]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.TranslucencyVolumeBlur:1]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.MaterialQualityLevel:1 ; High quality]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.SSS.Scale:1]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.SSS.SampleSet:2]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.SSS.Quality:1]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.SSS.HalfRes:0]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.SSGI.Quality:3]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.EmitterSpawnRateScale:1.0]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.ParticleLightQuality:2]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.FastApplyOnOpaque:1 ; Always have FastSkyLUT 1 in this case to avoid wrong sky]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.SampleCountMaxPerSlice:4]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.DepthResolution:16.0]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT:1]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT.SampleCountMin:4.0]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT.SampleCountMax:128.0]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.SkyAtmosphere.SampleCountMin:4.0]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.SkyAtmosphere.SampleCountMax:128.0]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.SkyAtmosphere.TransmittanceLUT.UseSmallFormat:0]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.SkyAtmosphere.TransmittanceLUT.SampleCount:10.0]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.SkyAtmosphere.MultiScatteringLUT.SampleCount:15.0]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[fx.Niagara.QualityLevel:3]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.Refraction.OffsetQuality:1]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.DownsampleFactor:2]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.MaxStepCount:256]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.Shadows.Resolution:256]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.Shadows.MaxSampleCount:8]]
[2025.08.26-02.43.12:554][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.UseExistenceMask:0]]
[2025.08.26-02.43.12:555][  0]LogConfig: Applying CVar settings from Section [FoliageQuality@3] File [Scalability]
[2025.08.26-02.43.12:555][  0]LogConfig: Set CVar [[foliage.DensityScale:1.0]]
[2025.08.26-02.43.12:555][  0]LogConfig: Set CVar [[grass.DensityScale:1.0]]
[2025.08.26-02.43.12:555][  0]LogConfig: Applying CVar settings from Section [ShadingQuality@3] File [Scalability]
[2025.08.26-02.43.12:555][  0]LogConfig: Set CVar [[r.HairStrands.SkyLighting.IntegrationType:2]]
[2025.08.26-02.43.12:555][  0]LogConfig: Set CVar [[r.HairStrands.SkyAO.SampleCount:4]]
[2025.08.26-02.43.12:555][  0]LogConfig: Set CVar [[r.HairStrands.Visibility.MSAA.SamplePerPixel:4]]
[2025.08.26-02.43.12:555][  0]LogConfig: Set CVar [[r.AnisotropicMaterials:1]]
[2025.08.26-02.43.12:555][  0]LogConfig: Applying CVar settings from Section [LandscapeQuality@3] File [Scalability]
[2025.08.26-02.43.12:557][  0]LogRHI: Using Default RHI: D3D12
[2025.08.26-02.43.12:557][  0]LogRHI: Using Highest Feature Level of D3D12: SM6
[2025.08.26-02.43.12:557][  0]LogRHI: Loading RHI module D3D12RHI
[2025.08.26-02.43.12:559][  0]LogD3D12RHI: Loading WinPixEventRuntime.dll for PIX profiling (from ../../../Engine/Binaries/ThirdParty/Windows/WinPixEventRuntime/x64).
[2025.08.26-02.43.12:559][  0]LogRHI: Checking if RHI D3D12 with Feature Level SM6 is supported by your system.
[2025.08.26-02.43.12:644][  0]LogD3D12RHI: Intel Extensions Framework not supported by driver. Please check if a driver update is available.
[2025.08.26-02.43.12:668][  0]LogD3D12RHI: Found D3D12 adapter 0: Intel(R) Iris(R) Xe Graphics (VendorId: 8086, DeviceId: a7a1, SubSysId: c001028, Revision: 0004
[2025.08.26-02.43.12:668][  0]LogD3D12RHI:   Max supported Feature Level 12_1, shader model 6.7, binding tier 3, wave ops supported, atomic64 unsupported
[2025.08.26-02.43.12:668][  0]LogD3D12RHI:   Adapter has 128MB of dedicated video memory, 0MB of dedicated system memory, and 16226MB of shared system memory, 1 output[s], UMA:true
[2025.08.26-02.43.12:668][  0]LogD3D12RHI:   Driver Version: 32.0.101.6556 (internal:32.0.101.6556, unified:101.6556)
[2025.08.26-02.43.12:668][  0]LogD3D12RHI:      Driver Date: 1-23-2025
[2025.08.26-02.43.12:675][  0]LogD3D12RHI: Found D3D12 adapter 1: Microsoft Basic Render Driver (VendorId: 1414, DeviceId: 008c, SubSysId: 0000, Revision: 0000
[2025.08.26-02.43.12:675][  0]LogD3D12RHI:   Max supported Feature Level 12_1, shader model 6.7, binding tier 3, wave ops supported, atomic64 supported
[2025.08.26-02.43.12:675][  0]LogD3D12RHI:   Adapter has 0MB of dedicated video memory, 0MB of dedicated system memory, and 16226MB of shared system memory, 0 output[s], UMA:true
[2025.08.26-02.43.12:675][  0]LogD3D12RHI: DirectX Agility SDK runtime found.
[2025.08.26-02.43.12:675][  0]LogD3D12RHI: Chosen D3D12 Adapter Id = 0
[2025.08.26-02.43.12:675][  0]LogD3D12RHI: Adapter only supports up to Feature Level 'SM5', requested Feature Level was 'SM6'
[2025.08.26-02.43.12:675][  0]LogRHI: RHI D3D12 with Feature Level SM6 is not supported on your system, attempting to fall back to RHI D3D11 with Feature Level SM5
[2025.08.26-02.43.12:675][  0]LogRHI: Loading RHI module D3D11RHI
[2025.08.26-02.43.12:676][  0]LogRHI: Checking if RHI D3D11 with Feature Level SM5 is supported by your system.
[2025.08.26-02.43.12:676][  0]LogD3D11RHI: D3D11 min allowed feature level: 11_0
[2025.08.26-02.43.12:676][  0]LogD3D11RHI: D3D11 max allowed feature level: 11_1
[2025.08.26-02.43.12:676][  0]LogD3D11RHI: D3D11 adapters:
[2025.08.26-02.43.12:676][  0]LogD3D11RHI: Testing D3D11 Adapter 0:
[2025.08.26-02.43.12:676][  0]LogD3D11RHI:     Description : Intel(R) Iris(R) Xe Graphics
[2025.08.26-02.43.12:676][  0]LogD3D11RHI:     VendorId    : 8086
[2025.08.26-02.43.12:676][  0]LogD3D11RHI:     DeviceId    : a7a1
[2025.08.26-02.43.12:676][  0]LogD3D11RHI:     SubSysId    : c001028
[2025.08.26-02.43.12:676][  0]LogD3D11RHI:     Revision    : 0004
[2025.08.26-02.43.12:676][  0]LogD3D11RHI:     DedicatedVideoMemory : 134217728 bytes
[2025.08.26-02.43.12:676][  0]LogD3D11RHI:     DedicatedSystemMemory : 0 bytes
[2025.08.26-02.43.12:676][  0]LogD3D11RHI:     SharedSystemMemory : 17014562816 bytes
[2025.08.26-02.43.12:676][  0]LogD3D11RHI:     AdapterLuid : 0 310376352
[2025.08.26-02.43.12:855][  0]LogD3D11RHI:    0. 'Intel(R) Iris(R) Xe Graphics' (Feature Level 11_1)
[2025.08.26-02.43.12:855][  0]LogD3D11RHI:       128/0/16226 MB DedicatedVideo/DedicatedSystem/SharedSystem, Outputs:1, VendorId:0x8086 UMA:true
[2025.08.26-02.43.12:855][  0]LogD3D11RHI: Testing D3D11 Adapter 1:
[2025.08.26-02.43.12:855][  0]LogD3D11RHI:     Description : Microsoft Basic Render Driver
[2025.08.26-02.43.12:855][  0]LogD3D11RHI:     VendorId    : 1414
[2025.08.26-02.43.12:855][  0]LogD3D11RHI:     DeviceId    : 008c
[2025.08.26-02.43.12:855][  0]LogD3D11RHI:     SubSysId    : 0000
[2025.08.26-02.43.12:855][  0]LogD3D11RHI:     Revision    : 0000
[2025.08.26-02.43.12:855][  0]LogD3D11RHI:     DedicatedVideoMemory : 0 bytes
[2025.08.26-02.43.12:855][  0]LogD3D11RHI:     DedicatedSystemMemory : 0 bytes
[2025.08.26-02.43.12:855][  0]LogD3D11RHI:     SharedSystemMemory : 17014562816 bytes
[2025.08.26-02.43.12:855][  0]LogD3D11RHI:     AdapterLuid : 0 85646
[2025.08.26-02.43.12:858][  0]LogD3D11RHI:    1. 'Microsoft Basic Render Driver' (Feature Level 11_1)
[2025.08.26-02.43.12:858][  0]LogD3D11RHI:       0/0/16226 MB DedicatedVideo/DedicatedSystem/SharedSystem, Outputs:0, VendorId:0x1414 UMA:true
[2025.08.26-02.43.12:858][  0]LogD3D11RHI: Chosen D3D11 Adapter:
[2025.08.26-02.43.12:858][  0]LogD3D11RHI:     Description : Intel(R) Iris(R) Xe Graphics
[2025.08.26-02.43.12:858][  0]LogD3D11RHI:     VendorId    : 8086
[2025.08.26-02.43.12:858][  0]LogD3D11RHI:     DeviceId    : a7a1
[2025.08.26-02.43.12:858][  0]LogD3D11RHI:     SubSysId    : c001028
[2025.08.26-02.43.12:858][  0]LogD3D11RHI:     Revision    : 0004
[2025.08.26-02.43.12:858][  0]LogD3D11RHI:     DedicatedVideoMemory : 134217728 bytes
[2025.08.26-02.43.12:858][  0]LogD3D11RHI:     DedicatedSystemMemory : 0 bytes
[2025.08.26-02.43.12:858][  0]LogD3D11RHI:     SharedSystemMemory : 17014562816 bytes
[2025.08.26-02.43.12:858][  0]LogD3D11RHI:     AdapterLuid : 0 310376352
[2025.08.26-02.43.12:858][  0]LogD3D11RHI: Integrated GPU (iGPU): true
[2025.08.26-02.43.12:858][  0]LogRHI: RHI D3D11 with Feature Level SM5 is supported and will be used.
[2025.08.26-02.43.12:858][  0]LogInit: Selected Device Profile: [WindowsEditor]
[2025.08.26-02.43.12:858][  0]LogHAL: Display: Platform has ~ 32 GB [34029125632 / 34359738368 / 32], which maps to Largest [LargestMinGB=32, LargerMinGB=12, DefaultMinGB=8, SmallerMinGB=6, SmallestMinGB=0)
[2025.08.26-02.43.12:858][  0]LogDeviceProfileManager: Going up to parent DeviceProfile [Windows]
[2025.08.26-02.43.12:858][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.08.26-02.43.12:858][  0]LogDeviceProfileManager: Pushing Device Profile CVar: [[UI.SlateSDFText.RasterizationMode:Bitmap -> Msdf]]
[2025.08.26-02.43.12:858][  0]LogDeviceProfileManager: Pushing Device Profile CVar: [[UI.SlateSDFText.ResolutionLevel:2 -> 2]]
[2025.08.26-02.43.12:858][  0]LogConfig: Applying CVar settings from Section [Startup] File [../../../Engine/Config/ConsoleVariables.ini]
[2025.08.26-02.43.12:858][  0]LogConfig: Set CVar [[r.DumpShaderDebugInfo:2]]
[2025.08.26-02.43.12:858][  0]LogConfig: Set CVar [[p.chaos.AllowCreatePhysxBodies:1]]
[2025.08.26-02.43.12:858][  0]LogConfig: Set CVar [[fx.SkipVectorVMBackendOptimizations:1]]
[2025.08.26-02.43.12:858][  0]LogConfig: CVar [[ds.CADTranslator.Meshing.ActivateThinZoneMeshing:0]] deferred - dummy variable created
[2025.08.26-02.43.12:858][  0]LogConfig: CVar [[ds.CADTranslator.Stitching.RemoveThinFaces:0]] deferred - dummy variable created
[2025.08.26-02.43.12:858][  0]LogConfig: Applying CVar settings from Section [Startup_Windows] File [../../../Engine/Config/ConsoleVariables.ini]
[2025.08.26-02.43.12:858][  0]LogConfig: Applying CVar settings from Section [ConsoleVariables] File [Engine]
[2025.08.26-02.43.12:858][  0]LogConfig: Applying CVar settings from Section [ConsoleVariables] File [C:/Game/Auracron/Saved/Config/WindowsEditor/Editor.ini]
[2025.08.26-02.43.12:858][  0]LogInit: Computer: TKT
[2025.08.26-02.43.12:858][  0]LogInit: User: tktca
[2025.08.26-02.43.12:858][  0]LogInit: CPU Page size=4096, Cores=10
[2025.08.26-02.43.12:858][  0]LogInit: High frequency timer resolution =10.000000 MHz
[2025.08.26-02.43.12:858][  0]LogMemory: Process is running as part of a Windows Job with separate resource limits
[2025.08.26-02.43.12:859][  0]LogMemory: Memory total: Physical=31.7GB (32GB approx) Virtual=45.9GB
[2025.08.26-02.43.12:859][  0]LogMemory: Platform Memory Stats for WindowsEditor
[2025.08.26-02.43.12:859][  0]LogMemory: Process Physical Memory: 645.11 MB used, 707.98 MB peak
[2025.08.26-02.43.12:859][  0]LogMemory: Process Virtual Memory: 649.97 MB used, 693.23 MB peak
[2025.08.26-02.43.12:859][  0]LogMemory: Physical Memory: 16219.55 MB used,  16233.15 MB free, 32452.70 MB total
[2025.08.26-02.43.12:859][  0]LogMemory: Virtual Memory: 22228.60 MB used,  24802.61 MB free, 47031.21 MB total
[2025.08.26-02.43.12:859][  0]LogCsvProfiler: Display: Metadata set : extradevelopmentmemorymb="0"
[2025.08.26-02.43.12:861][  0]LogWindows: WindowsPlatformFeatures enabled
[2025.08.26-02.43.12:863][  0]LogChaosDD: Chaos Debug Draw Startup
[2025.08.26-02.43.12:864][  0]LogInit: Physics initialised using underlying interface: Chaos
[2025.08.26-02.43.12:864][  0]LogInit: Using OS detected language (pt-BR).
[2025.08.26-02.43.12:864][  0]LogInit: Using OS detected locale (pt-BR).
[2025.08.26-02.43.12:869][  0]LogInit: Setting process to per monitor DPI aware
[2025.08.26-02.43.13:269][  0]LogTextLocalizationResource: LocRes '../../../Engine/Content/Localization/Editor/pt/Editor.locres' could not be opened for reading!
[2025.08.26-02.43.13:269][  0]LogTextLocalizationResource: LocRes '../../../Engine/Content/Localization/EditorTutorials/pt/EditorTutorials.locres' could not be opened for reading!
[2025.08.26-02.43.13:269][  0]LogTextLocalizationResource: LocRes '../../../Engine/Content/Localization/Keywords/pt/Keywords.locres' could not be opened for reading!
[2025.08.26-02.43.13:269][  0]LogTextLocalizationResource: LocRes '../../../Engine/Content/Localization/Category/pt/Category.locres' could not be opened for reading!
[2025.08.26-02.43.13:269][  0]LogTextLocalizationResource: LocRes '../../../Engine/Content/Localization/ToolTips/pt/ToolTips.locres' could not be opened for reading!
[2025.08.26-02.43.13:269][  0]LogTextLocalizationResource: LocRes '../../../Engine/Content/Localization/PropertyNames/pt/PropertyNames.locres' could not be opened for reading!
[2025.08.26-02.43.13:269][  0]LogTextLocalizationResource: LocRes '../../../Engine/Content/Localization/Engine/pt/Engine.locres' could not be opened for reading!
[2025.08.26-02.43.13:269][  0]LogTextLocalizationResource: LocRes '../../../Engine/Plugins/Online/OnlineSubsystem/Content/Localization/OnlineSubsystem/pt/OnlineSubsystem.locres' could not be opened for reading!
[2025.08.26-02.43.13:269][  0]LogTextLocalizationResource: LocRes '../../../Engine/Plugins/Online/OnlineSubsystemUtils/Content/Localization/OnlineSubsystemUtils/pt/OnlineSubsystemUtils.locres' could not be opened for reading!
[2025.08.26-02.43.13:269][  0]LogTextLocalizationResource: LocRes '../../../Engine/Plugins/MetaHuman/MetaHumanSDK/Content/Localization/MetaHumanSDK/pt/MetaHumanSDK.locres' could not be opened for reading!
[2025.08.26-02.43.13:269][  0]LogTextLocalizationResource: LocRes '../../../Engine/Plugins/Online/IOS/OnlineSubsystemIOS/Content/Localization/OnlineSubsystemIOS/pt/OnlineSubsystemIOS.locres' could not be opened for reading!
[2025.08.26-02.43.13:269][  0]LogTextLocalizationResource: LocRes '../../../Engine/Plugins/Online/Android/OnlineSubsystemGooglePlay/Content/Localization/OnlineSubsystemGooglePlay/pt/OnlineSubsystemGooglePlay.locres' could not be opened for reading!
[2025.08.26-02.43.13:355][  0]LogWindowsTextInputMethodSystem: Available input methods:
[2025.08.26-02.43.13:355][  0]LogWindowsTextInputMethodSystem:   - Português (Brasil) - (Keyboard).
[2025.08.26-02.43.13:355][  0]LogWindowsTextInputMethodSystem:   - Português (Portugal) - (Keyboard).
[2025.08.26-02.43.13:355][  0]LogWindowsTextInputMethodSystem: Activated input method: Português (Brasil) - (Keyboard).
[2025.08.26-02.43.13:358][  0]LogWindowsTouchpad: Display: CacheForceMaxTouchpadSensitivityMode SetMaxTouchpadSensitivity
[2025.08.26-02.43.13:360][  0]LogObj: Display: Attempting to load config data for Default__SlateThemeManager before the Class has been constructed/registered/linked (likely during module loading or early startup). This will result in the load silently failing and should be fixed.
[2025.08.26-02.43.13:364][  0]LogSlate: New Slate User Created. Platform User Id 0, User Index 0, Is Virtual User: 0
[2025.08.26-02.43.13:364][  0]LogSlate: Slate User Registered.  User Index 0, Is Virtual User: 0
[2025.08.26-02.43.13:458][  0]LogRHI: Using Default RHI: D3D12
[2025.08.26-02.43.13:458][  0]LogRHI: Using Highest Feature Level of D3D12: SM6
[2025.08.26-02.43.13:458][  0]LogRHI: Loading RHI module D3D12RHI
[2025.08.26-02.43.13:458][  0]LogRHI: Checking if RHI D3D12 with Feature Level SM6 is supported by your system.
[2025.08.26-02.43.13:458][  0]LogD3D12RHI: Adapter only supports up to Feature Level 'SM5', requested Feature Level was 'SM6'
[2025.08.26-02.43.13:458][  0]LogRHI: RHI D3D12 with Feature Level SM6 is not supported on your system, attempting to fall back to RHI D3D11 with Feature Level SM5
[2025.08.26-02.43.13:458][  0]LogRHI: Loading RHI module D3D11RHI
[2025.08.26-02.43.13:458][  0]LogRHI: Checking if RHI D3D11 with Feature Level SM5 is supported by your system.
[2025.08.26-02.43.13:458][  0]LogRHI: RHI D3D11 with Feature Level SM5 is supported and will be used.
[2025.08.26-02.43.13:459][  0]LogWindows: Attached monitors:
[2025.08.26-02.43.13:459][  0]LogWindows:     resolution: 1920x1080, work area: (0, 0) -> (1920, 1020), device: '\\.\DISPLAY6' [PRIMARY]
[2025.08.26-02.43.13:459][  0]LogWindows: Found 1 attached monitors.
[2025.08.26-02.43.13:459][  0]LogWindows: Gathering driver information using Windows Setup API
[2025.08.26-02.43.13:459][  0]LogRHI: RHI Adapter Info:
[2025.08.26-02.43.13:459][  0]LogRHI:             Name: Intel(R) Iris(R) Xe Graphics
[2025.08.26-02.43.13:459][  0]LogRHI:   Driver Version: 32.0.101.6556 (internal:32.0.101.6556, unified:101.6556)
[2025.08.26-02.43.13:459][  0]LogRHI:      Driver Date: 1-23-2025
[2025.08.26-02.43.13:459][  0]LogD3D11RHI: Creating new Direct3DDevice
[2025.08.26-02.43.13:459][  0]LogD3D11RHI:     GPU DeviceId: 0xa7a1 (for the marketing name, search the web for "GPU Device Id")
[2025.08.26-02.43.13:459][  0]LogRHI: Texture pool is 1523 MB (70% of 2176 MB)
[2025.08.26-02.43.13:459][  0]LogNvidiaAftermath: Nvidia Aftermath is disabled in D3D11 due to instability issues.
[2025.08.26-02.43.13:459][  0]LogD3D11RHI: Creating D3DDevice using adapter:
[2025.08.26-02.43.13:459][  0]LogD3D11RHI:     Description : Intel(R) Iris(R) Xe Graphics
[2025.08.26-02.43.13:459][  0]LogD3D11RHI:     VendorId    : 8086
[2025.08.26-02.43.13:459][  0]LogD3D11RHI:     DeviceId    : a7a1
[2025.08.26-02.43.13:459][  0]LogD3D11RHI:     SubSysId    : c001028
[2025.08.26-02.43.13:459][  0]LogD3D11RHI:     Revision    : 0004
[2025.08.26-02.43.13:459][  0]LogD3D11RHI:     DedicatedVideoMemory : 134217728 bytes
[2025.08.26-02.43.13:459][  0]LogD3D11RHI:     DedicatedSystemMemory : 0 bytes
[2025.08.26-02.43.13:459][  0]LogD3D11RHI:     SharedSystemMemory : 17014562816 bytes
[2025.08.26-02.43.13:459][  0]LogD3D11RHI:     AdapterLuid : 0 310376352
[2025.08.26-02.43.13:616][  0]LogNvidiaAftermath: Aftermath is not loaded.
[2025.08.26-02.43.13:638][  0]LogD3D11RHI: Intel Extensions loaded requested version for UAVOverlap: 1.1.0
[2025.08.26-02.43.13:638][  0]LogD3D11RHI: Intel Extensions loaded requested version Atomics Version: 3.4.1
[2025.08.26-02.43.13:638][  0]LogD3D11RHI: Intel Extensions Framework enabled
[2025.08.26-02.43.13:638][  0]LogD3D11RHI: RHI has support for 64 bit atomics
[2025.08.26-02.43.13:638][  0]LogD3D11RHI: Async texture creation enabled
[2025.08.26-02.43.13:638][  0]LogD3D11RHI: D3D11_MAP_WRITE_NO_OVERWRITE for dynamic buffer SRVs is supported
[2025.08.26-02.43.13:638][  0]LogD3D11RHI: Array index from any shader is supported
[2025.08.26-02.43.13:649][  0]LogVRS: Current RHI does not support Variable Rate Shading
[2025.08.26-02.43.13:652][  0]LogCsvProfiler: Display: Metadata set : verbatimrhiname="D3D11"
[2025.08.26-02.43.13:652][  0]LogCsvProfiler: Display: Metadata set : rhiname="D3D11"
[2025.08.26-02.43.13:652][  0]LogCsvProfiler: Display: Metadata set : rhifeaturelevel="SM5"
[2025.08.26-02.43.13:652][  0]LogCsvProfiler: Display: Metadata set : shaderplatform="PCD3D_SM5"
[2025.08.26-02.43.13:652][  0]LogInit: Initializing FReadOnlyCVARCache
[2025.08.26-02.43.13:654][  0]LogTurnkeySupport: Running Turnkey SDK detection: ' -ScriptsForProject="C:/Game/Auracron/Auracron.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="C:/Game/Auracron/Intermediate/TurnkeyReport_0.log" -log="C:/Game/Auracron/Intermediate/TurnkeyLog_0.log" -project="C:/Game/Auracron/Auracron.uproject"  -platform=all'
[2025.08.26-02.43.13:654][  0]LogMonitoredProcess: Running Serialized UAT: [ cmd.exe /c ""C:/Program Files/Epic Games/UE_5.6/Engine/Build/BatchFiles/RunUAT.bat"  -ScriptsForProject="C:/Game/Auracron/Auracron.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="C:/Game/Auracron/Intermediate/TurnkeyReport_0.log" -log="C:/Game/Auracron/Intermediate/TurnkeyLog_0.log" -project="C:/Game/Auracron/Auracron.uproject"  -platform=all" ]
[2025.08.26-02.43.13:668][  0]LogTextureFormatASTC: Display: ASTCEnc version 5.0.1 library loaded
[2025.08.26-02.43.13:668][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatASTC
[2025.08.26-02.43.13:668][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatDXT
[2025.08.26-02.43.13:668][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatETC2
[2025.08.26-02.43.13:668][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatIntelISPCTexComp
[2025.08.26-02.43.13:668][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatUncompressed
[2025.08.26-02.43.13:668][  0]LogTextureFormatOodle: Display: Oodle Texture TFO init; latest sdk version = 2.9.13
[2025.08.26-02.43.13:668][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.13.dll
[2025.08.26-02.43.13:668][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.5.dll
[2025.08.26-02.43.13:668][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatOodle
[2025.08.26-02.43.13:697][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android'
[2025.08.26-02.43.13:697][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ASTC'
[2025.08.26-02.43.13:697][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_DXT'
[2025.08.26-02.43.13:697][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ETC2'
[2025.08.26-02.43.13:698][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_OpenXR'
[2025.08.26-02.43.13:698][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'AndroidClient'
[2025.08.26-02.43.13:698][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ASTCClient'
[2025.08.26-02.43.13:698][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_DXTClient'
[2025.08.26-02.43.13:698][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ETC2Client'
[2025.08.26-02.43.13:698][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_OpenXRClient'
[2025.08.26-02.43.13:698][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_Multi'
[2025.08.26-02.43.13:698][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_MultiClient'
[2025.08.26-02.43.13:709][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'IOS'
[2025.08.26-02.43.13:709][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'IOSClient'
[2025.08.26-02.43.13:720][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Linux'
[2025.08.26-02.43.13:720][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxEditor'
[2025.08.26-02.43.13:720][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxServer'
[2025.08.26-02.43.13:720][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxClient'
[2025.08.26-02.43.13:731][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Mac'
[2025.08.26-02.43.13:731][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacEditor'
[2025.08.26-02.43.13:731][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacServer'
[2025.08.26-02.43.13:731][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacClient'
[2025.08.26-02.43.13:743][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'TVOS'
[2025.08.26-02.43.13:743][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'TVOSClient'
[2025.08.26-02.43.13:754][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Windows'
[2025.08.26-02.43.13:754][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsEditor'
[2025.08.26-02.43.13:754][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsServer'
[2025.08.26-02.43.13:754][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsClient'
[2025.08.26-02.43.13:754][  0]LogTargetPlatformManager: Display: Building Assets For WindowsEditor
[2025.08.26-02.43.13:783][  0]LogTargetPlatformManager: Unable to find shader format SF_METAL_ES3_1_IOS from hinted modules, loading all potential format modules to find it
[2025.08.26-02.43.13:790][  0]LogTargetPlatformManager: Loaded format module MetalShaderFormat
[2025.08.26-02.43.13:790][  0]LogTargetPlatformManager:   SF_METAL_ES3_1_IOS
[2025.08.26-02.43.13:790][  0]LogTargetPlatformManager:   SF_METAL_SM5_IOS
[2025.08.26-02.43.13:790][  0]LogTargetPlatformManager:   SF_METAL_ES3_1_TVOS
[2025.08.26-02.43.13:790][  0]LogTargetPlatformManager:   SF_METAL_SM5_TVOS
[2025.08.26-02.43.13:790][  0]LogTargetPlatformManager:   SF_METAL_SM5
[2025.08.26-02.43.13:790][  0]LogTargetPlatformManager:   SF_METAL_SM6
[2025.08.26-02.43.13:790][  0]LogTargetPlatformManager:   SF_METAL_SIM
[2025.08.26-02.43.13:790][  0]LogTargetPlatformManager:   SF_METAL_ES3_1
[2025.08.26-02.43.13:790][  0]LogTargetPlatformManager: Loaded format module ShaderFormatD3D
[2025.08.26-02.43.13:790][  0]LogTargetPlatformManager:   PCD3D_SM6
[2025.08.26-02.43.13:790][  0]LogTargetPlatformManager:   PCD3D_SM5
[2025.08.26-02.43.13:790][  0]LogTargetPlatformManager:   PCD3D_ES31
[2025.08.26-02.43.13:790][  0]LogTargetPlatformManager: Loaded format module ShaderFormatOpenGL
[2025.08.26-02.43.13:790][  0]LogTargetPlatformManager:   GLSL_150_ES31
[2025.08.26-02.43.13:790][  0]LogTargetPlatformManager:   GLSL_ES3_1_ANDROID
[2025.08.26-02.43.13:790][  0]LogTargetPlatformManager: Loaded format module ShaderFormatVectorVM
[2025.08.26-02.43.13:790][  0]LogTargetPlatformManager:   VVM_1_0
[2025.08.26-02.43.13:790][  0]LogTargetPlatformManager: Loaded format module VulkanShaderFormat
[2025.08.26-02.43.13:790][  0]LogTargetPlatformManager:   SF_VULKAN_SM5
[2025.08.26-02.43.13:790][  0]LogTargetPlatformManager:   SF_VULKAN_ES31_ANDROID
[2025.08.26-02.43.13:790][  0]LogTargetPlatformManager:   SF_VULKAN_ES31
[2025.08.26-02.43.13:790][  0]LogTargetPlatformManager:   SF_VULKAN_SM5_ANDROID
[2025.08.26-02.43.13:790][  0]LogTargetPlatformManager:   SF_VULKAN_SM6
[2025.08.26-02.43.13:790][  0]LogRendererCore: Ray tracing is disabled. Reason: not supported by current RHI.
[2025.08.26-02.43.13:792][  0]LogDerivedDataCache: Display: Memory: Max Cache Size: -1 MB
[2025.08.26-02.43.13:793][  0]LogDerivedDataCache: FDerivedDataBackendGraph: Pak pak cache file ../../../../../../Game/Auracron/DerivedDataCache/DDC.ddp not found, will not use a pak cache.
[2025.08.26-02.43.13:793][  0]LogDerivedDataCache: Unable to find inner node Pak for hierarchy Hierarchy.
[2025.08.26-02.43.13:793][  0]LogDerivedDataCache: FDerivedDataBackendGraph: CompressedPak pak cache file ../../../../../../Game/Auracron/DerivedDataCache/Compressed.ddp not found, will not use a pak cache.
[2025.08.26-02.43.13:793][  0]LogDerivedDataCache: Unable to find inner node CompressedPak for hierarchy Hierarchy.
[2025.08.26-02.43.13:969][  0]LogDerivedDataCache: Display: ../../../Engine/DerivedDataCache/Compressed.ddp: Opened pak cache for reading. (1350 MiB)
[2025.08.26-02.43.13:969][  0]LogDerivedDataCache: FDerivedDataBackendGraph: EnterprisePak pak cache file ../../../Enterprise/DerivedDataCache/Compressed.ddp not found, will not use a pak cache.
[2025.08.26-02.43.13:969][  0]LogDerivedDataCache: Unable to find inner node EnterprisePak for hierarchy Hierarchy.
[2025.08.26-02.43.13:971][  0]LogZenServiceInstance: Found Zen config default=C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Data
[2025.08.26-02.43.13:972][  0]LogZenServiceInstance: InTree version at 'C:/Program Files/Epic Games/UE_5.6/Engine/Binaries/Win64/zenserver.exe' is '5.6.6-202504241958-windows-x64-release-f090a03'
[2025.08.26-02.43.13:972][  0]LogZenServiceInstance: Installed version at 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install/zenserver.exe' is '5.6.6-202504241958-windows-x64-release-f090a03'
[2025.08.26-02.43.13:972][  0]LogZenServiceInstance: No current process using the data dir found, launching a new instance
[2025.08.26-02.43.13:972][  0]LogZenServiceInstance: Display: Launching executable 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install/zenserver.exe', working dir 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install', data dir 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Data', args '--port 8558 --data-dir "C:\Users\<USER>\AppData\Local\UnrealEngine\Common\Zen\Data" --http asio --gc-cache-duration-seconds 1209600 --gc-interval-seconds 21600 --gc-low-diskspace-threshold 2147483648 --quiet --http-forceloopback --owner-pid 17364  --child-id Zen_17364_Startup'
[2025.08.26-02.43.14:102][  0]LogZenServiceInstance: Display: Unreal Zen Storage Server HTTP service at [::1]:8558 status: OK!.
[2025.08.26-02.43.14:102][  0]LogZenServiceInstance: Local ZenServer AutoLaunch initialization completed in 0.130 seconds
[2025.08.26-02.43.14:114][  0]LogDerivedDataCache: Display: ZenLocal: Using ZenServer HTTP service at http://[::1]:8558/ with namespace ue.ddc status: OK!.
[2025.08.26-02.43.14:120][  0]LogDerivedDataCache: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Speed tests took 0.01 seconds.
[2025.08.26-02.43.14:120][  0]LogDerivedDataCache: Display: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Performance: Latency=0.02ms. RandomReadSpeed=1303.54MBs, RandomWriteSpeed=189.70MBs. Assigned SpeedClass 'Local'
[2025.08.26-02.43.14:121][  0]LogDerivedDataCache: Local: Using data cache path C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: DeleteOnly
[2025.08.26-02.43.14:121][  0]LogDerivedDataCache: ZenShared: Disabled because Host is set to 'None'
[2025.08.26-02.43.14:121][  0]LogDerivedDataCache: Unable to find inner node ZenShared for hierarchy Hierarchy.
[2025.08.26-02.43.14:122][  0]LogDerivedDataCache: Shared: Disabled because no path is configured.
[2025.08.26-02.43.14:122][  0]LogDerivedDataCache: Unable to find inner node Shared for hierarchy Hierarchy.
[2025.08.26-02.43.14:122][  0]LogDerivedDataCache: Cloud: Disabled because Host is set to 'None'
[2025.08.26-02.43.14:122][  0]LogDerivedDataCache: Unable to find inner node Cloud for hierarchy Hierarchy.
[2025.08.26-02.43.14:122][  0]LogShaderCompilers: Guid format shader working directory is 14 characters bigger than the processId version (../../../../../../Game/Auracron/Intermediate/Shaders/WorkingDirectory/17364/).
[2025.08.26-02.43.14:123][  0]LogShaderCompilers: Cleaned the shader compiler working directory 'C:/Users/<USER>/AppData/Local/Temp/UnrealShaderWorkingDir/D7ADE470411F261E25F29A95C6C01C72/'.
[2025.08.26-02.43.14:123][  0]LogXGEController: Cannot use XGE Controller as Incredibuild is not installed on this machine.
[2025.08.26-02.43.14:124][  0]LogUbaHorde: Display: UBA/Horde Configuration [Uba.Provider.Horde]: Not Enabled
[2025.08.26-02.43.14:124][  0]LogShaderCompilers: Display: Using 9 local workers for shader compilation
[2025.08.26-02.43.14:125][  0]LogShaderCompilers: Display: Compiling shader autogen file: ../../../../../../Game/Auracron/Intermediate/ShaderAutogen/PCD3D_SM5/AutogenShaderHeaders.ush
[2025.08.26-02.43.14:126][  0]LogShaderCompilers: Display: Autogen file is unchanged, skipping write.
[2025.08.26-02.43.14:916][  0]LogTurnkeySupport: Completed SDK detection: ExitCode = 0
[2025.08.26-02.43.15:277][  0]LogSlate: Using FreeType 2.10.0
[2025.08.26-02.43.15:277][  0]LogSlate: SlateFontServices - WITH_FREETYPE: 1, WITH_HARFBUZZ: 1
[2025.08.26-02.43.15:280][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.08.26-02.43.15:280][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png] file: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png]
[2025.08.26-02.43.15:280][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.08.26-02.43.15:280][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png] file: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png]
[2025.08.26-02.43.15:294][  0]LogAssetRegistry: FAssetRegistry took 0.0027 seconds to start up
[2025.08.26-02.43.15:296][  0]LogEditorDomain: Display: EditorDomain is Disabled
[2025.08.26-02.43.15:331][  0]LogAssetRegistry: Display: AssetDataGatherer spent 0.000s loading caches ../../../../../../Game/Auracron/Intermediate/CachedAssetRegistry_*.bin.
[2025.08.26-02.43.15:509][  0]LogDeviceProfileManager: Display: Deviceprofile LinuxArm64Editor not found.
[2025.08.26-02.43.15:509][  0]LogDeviceProfileManager: Display: Deviceprofile LinuxArm64 not found.
[2025.08.26-02.43.15:534][  0]LogDeviceProfileManager: Active device profile: [000002A1975CDE80][000002A185982800 66] WindowsEditor
[2025.08.26-02.43.15:534][  0]LogCsvProfiler: Display: Metadata set : deviceprofile="WindowsEditor"
[2025.08.26-02.43.15:536][  0]LogStreaming: Display: FlushAsyncLoading(1): 1 QueuedPackages, 0 AsyncPackages
[2025.08.26-02.43.15:538][  0]LogTextureEncodingSettings: Display: Texture Encode Speed: FinalIfAvailable (editor).
[2025.08.26-02.43.15:538][  0]LogTextureEncodingSettings: Display: Oodle Texture Encode Speed settings: Fast: RDO Off Lambda=0, Effort=Normal Final: RDO Off Lambda=0, Effort=Normal
[2025.08.26-02.43.15:538][  0]LogTextureEncodingSettings: Display: Shared linear texture encoding: Disabled
[2025.08.26-02.43.15:544][  0]LogTurnkeySupport: Turnkey Platform: Android: (Status=Invalid, MinAllowed_Sdk=r25b, MaxAllowed_Sdk=r29, Current_Sdk=, Allowed_AutoSdk=r27c, Current_AutoSdk=, Flags="Platform_InvalidHostPrerequisites, Support_FullSdk", Error="Android Studio is not installed correctly.")
[2025.08.26-02.43.15:545][  0]LogTurnkeySupport: Turnkey Platform: IOS: (Status=Invalid, MinAllowed_Sdk=1100.0.0.0, MaxAllowed_Sdk=8999.0, Current_Sdk=, Allowed_AutoSdk=15.2, Current_AutoSdk=, Flags="Platform_ValidHostPrerequisites")
[2025.08.26-02.43.15:545][  0]LogTurnkeySupport: Turnkey Platform: Win64: (Status=Valid, MinAllowed_Sdk=10.0.19041.0, MaxAllowed_Sdk=10.9.99999.0, Current_Sdk=10.0.26100.0, Allowed_AutoSdk=10.0.22621.0, Current_AutoSdk=, Flags="InstalledSdk_ValidVersionExists")
[2025.08.26-02.43.15:546][  0]LogTurnkeySupport: Running Turnkey device detection: ' -ScriptsForProject="C:/Game/Auracron/Auracron.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="C:/Game/Auracron/Intermediate/TurnkeyReport_1.log" -log="C:/Game/Auracron/Intermediate/TurnkeyLog_1.log" -project="C:/Game/Auracron/Auracron.uproject"  -Device=Win64@TKT'
[2025.08.26-02.43.15:546][  0]LogMonitoredProcess: Running Serialized UAT: [ cmd.exe /c ""C:/Program Files/Epic Games/UE_5.6/Engine/Build/BatchFiles/RunUAT.bat"  -ScriptsForProject="C:/Game/Auracron/Auracron.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="C:/Game/Auracron/Intermediate/TurnkeyReport_1.log" -log="C:/Game/Auracron/Intermediate/TurnkeyLog_1.log" -project="C:/Game/Auracron/Auracron.uproject"  -Device=Win64@TKT" -nocompile -nocompileuat ]
[2025.08.26-02.43.15:571][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.26-02.43.15:572][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness because of a recursive sync load
[2025.08.26-02.43.15:572][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.26-02.43.15:572][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.26-02.43.15:573][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec because of a recursive sync load
[2025.08.26-02.43.15:573][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.26-02.43.15:574][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/WorldGridMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.26-02.43.15:576][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/EngineMaterials/T_Default_Material_Grid_M because of a recursive sync load
[2025.08.26-02.43.15:576][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/EngineMaterials/T_Default_Material_Grid_N because of a recursive sync load
[2025.08.26-02.43.15:578][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/Functions/Engine_MaterialFunctions01/Opacity/CameraDepthFade because of a recursive sync load
[2025.08.26-02.43.15:579][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.26-02.43.15:580][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.26-02.43.15:580][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.26-02.43.15:581][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultLightFunctionMaterial is adding a dynamic import to package /Engine/EngineMaterials/DefaultDiffuse because of a recursive sync load
[2025.08.26-02.43.15:581][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultLightFunctionMaterial is adding a dynamic import to package /Engine/Functions/Engine_MaterialFunctions02/Utility/BreakOutFloat2Components because of a recursive sync load
[2025.08.26-02.43.15:582][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultLightFunctionMaterial is adding a dynamic import to package /Engine/EngineMaterials/DefaultPostProcessMaterial because of a recursive sync load
[2025.08.26-02.43.15:582][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.26-02.43.15:610][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultDeferredDecalMaterial is adding a dynamic import to package /Engine/EngineMaterials/DefaultLightFunctionMaterial because of a recursive sync load
[2025.08.26-02.43.15:610][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.26-02.43.15:622][  0]LogStreaming: Display: Package /Engine/EngineMaterials/WorldGridMaterial is adding a dynamic import to package /Engine/EngineMaterials/DefaultDeferredDecalMaterial because of a recursive sync load
[2025.08.26-02.43.15:623][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.26-02.43.15:634][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/EngineMaterials/WorldGridMaterial because of a recursive sync load
[2025.08.26-02.43.15:634][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/WorldGridMaterial (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.26-02.43.15:728][  0]LogMeshReduction: Display: Using QuadricMeshReduction for automatic static mesh reduction
[2025.08.26-02.43.15:728][  0]LogMeshReduction: Display: Using SkeletalMeshReduction for automatic skeletal mesh reduction
[2025.08.26-02.43.15:728][  0]LogMeshReduction: Display: Using ProxyLODMeshReduction for automatic mesh merging
[2025.08.26-02.43.15:728][  0]LogMeshReduction: Display: No distributed automatic mesh merging module available
[2025.08.26-02.43.15:728][  0]LogMeshMerging: No distributed automatic mesh merging module available
[2025.08.26-02.43.16:151][  0]LogConfig: Applying CVar settings from Section [/Script/CQTest.CQTestSettings] File [Engine]
[2025.08.26-02.43.16:181][  0]LogVirtualization: Display: VirtualizationSystem name found in ini file: None
[2025.08.26-02.43.16:182][  0]LogVirtualization: Display: FNullVirtualizationSystem mounted, virtualization will be disabled
[2025.08.26-02.43.16:182][  0]LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: NetActorFactory name: NetActorFactory id: 0
[2025.08.26-02.43.16:182][  0]LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: NetSubObjectFactory name: NetSubObjectFactory id: 1
[2025.08.26-02.43.16:185][  0]LogLiveCoding: Display: Starting LiveCoding
[2025.08.26-02.43.16:185][  0]LogLiveCoding: Display: LiveCodingConsole Arguments: UnrealEditor Win64 Development
[2025.08.26-02.43.16:187][  0]LogLiveCoding: Display: First instance in process group "UE_Auracron_0xa5ca6502", spawning console
[2025.08.26-02.43.16:191][  0]LogLiveCoding: Display: Waiting for server
[2025.08.26-02.43.16:212][  0]LogSlate: Border
[2025.08.26-02.43.16:212][  0]LogSlate: BreadcrumbButton
[2025.08.26-02.43.16:212][  0]LogSlate: Brushes.Title
[2025.08.26-02.43.16:212][  0]LogSlate: ColorPicker.ColorThemes
[2025.08.26-02.43.16:212][  0]LogSlate: Default
[2025.08.26-02.43.16:212][  0]LogSlate: Icons.Save
[2025.08.26-02.43.16:212][  0]LogSlate: Icons.Toolbar.Settings
[2025.08.26-02.43.16:212][  0]LogSlate: ListView
[2025.08.26-02.43.16:212][  0]LogSlate: SoftwareCursor_CardinalCross
[2025.08.26-02.43.16:212][  0]LogSlate: SoftwareCursor_Grab
[2025.08.26-02.43.16:212][  0]LogSlate: TableView.DarkRow
[2025.08.26-02.43.16:212][  0]LogSlate: TableView.Row
[2025.08.26-02.43.16:212][  0]LogSlate: TreeView
[2025.08.26-02.43.16:287][  0]LogWorldPartition: Display: FWorldPartitionClassDescRegistry::Initialize started...
[2025.08.26-02.43.16:290][  0]LogWorldPartition: Display: FWorldPartitionClassDescRegistry::Initialize took 1.751 ms
[2025.08.26-02.43.16:303][  0]LogInit: XR: Instanced Stereo Rendering is Disabled
[2025.08.26-02.43.16:303][  0]LogInit: XR: MultiViewport is Disabled
[2025.08.26-02.43.16:303][  0]LogInit: XR: Mobile Multiview is Disabled
[2025.08.26-02.43.16:334][  0]LogStats: UGameplayTagsManager::InitializeManager -  0.000 s
[2025.08.26-02.43.16:555][  0]LogTurnkeySupport: Completed device detection: Code = 0
[2025.08.26-02.43.16:580][  0]LogNiagaraDebuggerClient: Niagara Debugger Client Initialized | Session: 0F8FBE08E42E4E618000000000008E00 | Instance: EE0B909542FAE9DC2CD11AB3E5AF6591 (TKT-17364).
[2025.08.26-02.43.16:621][  0]LogTcpMessaging: Initializing TcpMessaging bridge
[2025.08.26-02.43.16:624][  0]LogUdpMessaging: Display: Work queue size set to 1024.
[2025.08.26-02.43.16:624][  0]LogUdpMessaging: Initializing bridge on interface 0.0.0.0:0 to multicast group 230.0.0.1:6666.
[2025.08.26-02.43.16:624][  0]LogUdpMessaging: Display: Unicast socket bound to '0.0.0.0:57009'.
[2025.08.26-02.43.16:628][  0]LogUdpMessaging: Display: Added local interface '192.168.0.35' to multicast group '230.0.0.1:6666'
[2025.08.26-02.43.16:628][  0]LogUdpMessaging: Display: Added local interface '172.26.144.1' to multicast group '230.0.0.1:6666'
[2025.08.26-02.43.16:630][  0]LogUdpMessaging: Display: Using asynchronous task graph for message deserialization.
[2025.08.26-02.43.16:739][  0]LogMetaSound: Display: MetaSound Page Target Initialized to 'Default'
[2025.08.26-02.43.16:740][  0]LogAudio: Display: Registering Engine Module Parameter Interfaces...
[2025.08.26-02.43.16:756][  0]LogMetaSound: MetaSound Engine Initialized
[2025.08.26-02.43.16:786][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.10.dll
[2025.08.26-02.43.16:934][  0]LogNNERuntimeORT: Available graphics and compute adapters:
[2025.08.26-02.43.16:934][  0]LogNNERuntimeORT: Selecting NPU adapter: Intel(R) Iris(R) Xe Graphics
[2025.08.26-02.43.17:003][  0]LogNNERuntimeORT: MakeRuntimeORTDml:
[2025.08.26-02.43.17:003][  0]LogNNERuntimeORT:   DirectML:  yes
[2025.08.26-02.43.17:003][  0]LogNNERuntimeORT:   RHI D3D12: no
[2025.08.26-02.43.17:003][  0]LogNNERuntimeORT:   D3D12:     yes
[2025.08.26-02.43.17:003][  0]LogNNERuntimeORT:   NPU:       yes
[2025.08.26-02.43.17:003][  0]LogNNERuntimeORT: Interface availability:
[2025.08.26-02.43.17:003][  0]LogNNERuntimeORT:   GPU: yes
[2025.08.26-02.43.17:003][  0]LogNNERuntimeORT:   RDG: no
[2025.08.26-02.43.17:003][  0]LogNNERuntimeORT:   NPU: yes
[2025.08.26-02.43.17:069][  0]LogNNERuntimeORT: Available graphics and compute adapters:
[2025.08.26-02.43.17:069][  0]LogNNERuntimeORT: Selecting NPU adapter: Intel(R) Iris(R) Xe Graphics
[2025.08.26-02.43.17:147][  0]LogLiveCoding: Display: Successfully initialized, removing startup thread
[2025.08.26-02.43.17:177][  0]LogChaosVDEditor: [FChaosVDExtensionsManager::RegisterExtension] Registering CVD Extension [FChaosVDGenericDebugDrawExtension] ...
[2025.08.26-02.43.17:177][  0]LogChaosVDEditor: [FChaosVDExtensionsManager::RegisterExtension] Registering CVD Extension [FChaosVDAccelerationStructuresExtension] ...
[2025.08.26-02.43.17:182][  0]LogTimingProfiler: Initialize
[2025.08.26-02.43.17:182][  0]LogTimingProfiler: OnSessionChanged
[2025.08.26-02.43.17:182][  0]LoadingProfiler: Initialize
[2025.08.26-02.43.17:182][  0]LoadingProfiler: OnSessionChanged
[2025.08.26-02.43.17:182][  0]LogNetworkingProfiler: Initialize
[2025.08.26-02.43.17:182][  0]LogNetworkingProfiler: OnSessionChanged
[2025.08.26-02.43.17:182][  0]LogMemoryProfiler: Initialize
[2025.08.26-02.43.17:182][  0]LogMemoryProfiler: OnSessionChanged
[2025.08.26-02.43.17:215][  0]LogConfig: Applying CVar settings from Section [/Script/PCG.PCGEngineSettings] File [Engine]
[2025.08.26-02.43.17:418][  0]LogAndroidPermission: UAndroidPermissionCallbackProxy::GetInstance
[2025.08.26-02.43.17:423][  0]LogAudioCaptureCore: Display: No Audio Capture implementations found. Audio input will be silent.
[2025.08.26-02.43.17:423][  0]LogAudioCaptureCore: Display: No Audio Capture implementations found. Audio input will be silent.
[2025.08.26-02.43.17:496][  0]SourceControl: Controle de revisão desabilitado
[2025.08.26-02.43.17:504][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Content/Slate/Icons/doc_16x.png' error.
[2025.08.26-02.43.17:504][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Content/Slate/Icons/doc_16x.png] file: [../../../Engine/Content/Slate/Icons/doc_16x.png]
[2025.08.26-02.43.17:504][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Content/Slate/Common/ButtonHoverHint.png' error.
[2025.08.26-02.43.17:504][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Content/Slate/Common/ButtonHoverHint.png] file: [../../../Engine/Content/Slate/Common/ButtonHoverHint.png]
[2025.08.26-02.43.17:510][  0]SourceControl: Controle de revisão desabilitado
[2025.08.26-02.43.17:581][  0]LogConfig: Applying CVar settings from Section [/Script/NNEDenoiser.NNEDenoiserSettings] File [Engine]
[2025.08.26-02.43.17:621][  0]LogCollectionManager: Loaded 0 collections in 0.001150 seconds
[2025.08.26-02.43.17:623][  0]LogFileCache: Scanning file cache for directory 'C:/Game/Auracron/Saved/Collections/' took 0.00s
[2025.08.26-02.43.17:625][  0]LogFileCache: Scanning file cache for directory 'C:/Game/Auracron/Content/Developers/tktca/Collections/' took 0.00s
[2025.08.26-02.43.17:628][  0]LogFileCache: Scanning file cache for directory 'C:/Game/Auracron/Content/Collections/' took 0.00s
[2025.08.26-02.43.17:640][  0]LogTemp: Display: UnrealMCPCollisionCommands: Initialized
[2025.08.26-02.43.17:640][  0]LogTemp: UnrealMCPPathfindingCommands initialized. NavigationSystem will be obtained when needed.
[2025.08.26-02.43.17:640][  0]LogTemp: UnrealMCP AI Commands initialized
[2025.08.26-02.43.17:640][  0]LogTemp: FUnrealMCPRealmCommands: Sistema de Transição de Realms inicializado
[2025.08.26-02.43.17:640][  0]LogTemp: FUnrealMCPMultilayerMapCommands: Sistema de Mapa Multicamada inicializado
[2025.08.26-02.43.17:640][  0]LogTemp: FUnrealMCPLaneMechanicsCommands: Sistema de Mecânicas de Lane inicializado
[2025.08.26-02.43.17:640][  0]LogTemp: FUnrealMCPJungleSystemCommands: Sistema de Jungle inicializado
[2025.08.26-02.43.17:640][  0]LogTemp: FUnrealMCPVerticalNavigationCommands: Sistema de Navegação Vertical inicializado
[2025.08.26-02.43.17:640][  0]LogTemp: FUnrealMCPGamePhasesCommands: Sistema de Fases da Partida inicializado
[2025.08.26-02.43.17:640][  0]LogTemp: FUnrealMCPObjectivesStructuresCommands: Sistema de Objetivos e Estruturas inicializado
[2025.08.26-02.43.17:640][  0]LogTemp: FUnrealMCPCombatMechanicsCommands: Sistema de Mecânicas de Combate inicializado
[2025.08.26-02.43.17:640][  0]LogScript: Warning: Script Msg: A null object was passed as a world context object to UEngine::GetWorldFromContextObject().
[2025.08.26-02.43.17:640][  0]LogTemp: Display: Unreal MCP Module has started
[2025.08.26-02.43.17:674][  0]LogUObjectArray: 45742 objects as part of root set at end of initial load.
[2025.08.26-02.43.17:674][  0]LogUObjectArray: CloseDisregardForGC: 0/0 objects in disregard for GC pool
[2025.08.26-02.43.17:768][  0]LogAutomationTest: Error: Condition failed
[2025.08.26-02.43.17:768][  0]LogAutomationTest: Error: Condition failed
[2025.08.26-02.43.17:768][  0]LogAutomationTest: Error: Condition failed
[2025.08.26-02.43.17:769][  0]LogEngine: Initializing Engine...
[2025.08.26-02.43.17:873][  0]LogStylusInput: Initializing StylusInput subsystem.
[2025.08.26-02.43.17:873][  0]LogTedsSettings: UTedsSettingsEditorSubsystem::Initialize
[2025.08.26-02.43.17:873][  0]LogTemp: Display: UnrealMCPCollisionCommands: Initialized
[2025.08.26-02.43.17:873][  0]LogTemp: UnrealMCPPathfindingCommands initialized. NavigationSystem will be obtained when needed.
[2025.08.26-02.43.17:873][  0]LogTemp: UnrealMCP AI Commands initialized
[2025.08.26-02.43.17:873][  0]LogTemp: FUnrealMCPRealmCommands: Sistema de Transição de Realms inicializado
[2025.08.26-02.43.17:873][  0]LogTemp: FUnrealMCPMultilayerMapCommands: Sistema de Mapa Multicamada inicializado
[2025.08.26-02.43.17:873][  0]LogTemp: FUnrealMCPLaneMechanicsCommands: Sistema de Mecânicas de Lane inicializado
[2025.08.26-02.43.17:873][  0]LogTemp: FUnrealMCPJungleSystemCommands: Sistema de Jungle inicializado
[2025.08.26-02.43.17:873][  0]LogTemp: FUnrealMCPVerticalNavigationCommands: Sistema de Navegação Vertical inicializado
[2025.08.26-02.43.17:873][  0]LogTemp: FUnrealMCPGamePhasesCommands: Sistema de Fases da Partida inicializado
[2025.08.26-02.43.17:873][  0]LogTemp: FUnrealMCPObjectivesStructuresCommands: Sistema de Objetivos e Estruturas inicializado
[2025.08.26-02.43.17:873][  0]LogTemp: FUnrealMCPCombatMechanicsCommands: Sistema de Mecânicas de Combate inicializado
[2025.08.26-02.43.17:873][  0]LogScript: Warning: Script Msg: A null object was passed as a world context object to UEngine::GetWorldFromContextObject().
[2025.08.26-02.43.17:873][  0]LogTemp: Display: UnrealMCPBridge: Initializing
[2025.08.26-02.43.17:874][  0]LogTemp: Display: UnrealMCPBridge: Server started on 127.0.0.1:55557
[2025.08.26-02.43.17:874][  0]LogTemp: Display: MCPServerRunnable: Created server runnable
[2025.08.26-02.43.17:877][  0]LogTemp: Display: MCPServerRunnable: Server thread starting...
[2025.08.26-02.43.18:062][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.11.dll
[2025.08.26-02.43.18:081][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.12.dll
[2025.08.26-02.43.18:095][  0]LogChaosDD: Creating Chaos Debug Draw Scene for world Untitled
[2025.08.26-02.43.18:111][  0]LogNetVersion: Set ProjectVersion to *******. Version Checksum will be recalculated on next use.
[2025.08.26-02.43.18:112][  0]LogInit: Texture streaming: Enabled
[2025.08.26-02.43.18:116][  0]LogAnalytics: Display: [UEEditor.Rocket.Release] AnalyticsET::StartSession ( APIServer = https://datarouter.ol.epicgames.com/datarouter/api/v1/public/data. AppVersion = 5.6.1-44394996+++UE5+Release-5.6 )
[2025.08.26-02.43.18:119][  0]LogAudio: Display: Initializing Audio Device Manager...
[2025.08.26-02.43.18:125][  0]LogAudio: Display: Loading Default Audio Settings Objects...
[2025.08.26-02.43.18:125][  0]LogAudio: Display: No default SoundConcurrencyObject specified (or failed to load).
[2025.08.26-02.43.18:126][  0]LogAudio: Display: Audio Device Manager Initialized
[2025.08.26-02.43.18:126][  0]LogAudio: Display: Creating Audio Device:                 Id: 1, Scope: Shared, Realtime: True
[2025.08.26-02.43.18:126][  0]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.08.26-02.43.18:126][  0]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.08.26-02.43.18:126][  0]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.08.26-02.43.18:126][  0]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.08.26-02.43.18:126][  0]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.08.26-02.43.18:126][  0]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.08.26-02.43.18:126][  0]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.08.26-02.43.18:126][  0]LogAudio: Display: AudioDevice MaxSources: 32
[2025.08.26-02.43.18:126][  0]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.08.26-02.43.18:126][  0]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.08.26-02.43.18:126][  0]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.08.26-02.43.18:131][  0]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.08.26-02.43.18:978][  0]LogAudioMixer: Display: Using Audio Hardware Device Colunas (Realtek(R) Audio)
[2025.08.26-02.43.18:978][  0]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.08.26-02.43.18:979][  0]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.08.26-02.43.18:979][  0]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.08.26-02.43.18:980][  0]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=1
[2025.08.26-02.43.18:980][  0]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=1
[2025.08.26-02.43.18:983][  0]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=1
[2025.08.26-02.43.18:983][  0]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=1
[2025.08.26-02.43.18:983][  0]LogInit: FAudioDevice initialized with ID 1.
[2025.08.26-02.43.18:983][  0]LogAudio: Display: Audio Device (ID: 1) registered with world 'Untitled'.
[2025.08.26-02.43.18:983][  0]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 1
[2025.08.26-02.43.18:987][  0]LogCsvProfiler: Display: Metadata set : largeworldcoordinates="1"
[2025.08.26-02.43.18:994][  0]LogInit: Undo buffer set to 256 MB
[2025.08.26-02.43.18:994][  0]LogInit: Transaction tracking system initialized
[2025.08.26-02.43.19:002][  0]LogSourceControl: Display: Uncontrolled Changelist persistency file loaded ../../../../../../Game/Auracron/Saved/SourceControl/UncontrolledChangelists.json
[2025.08.26-02.43.19:034][  0]LocalizationService: O serviço de localização está desativado.
[2025.08.26-02.43.19:073][  0]LogTurnkeySupport: Turnkey Device: Win64@tkt: (Name=tkt, Type=Computer, Status=Valid, MinAllowed=10.0.19041.0, MaxAllowed=, Current=10.0.26100.0, Flags="Device_InstallSoftwareValid")
[2025.08.26-02.43.19:142][  0]LogFileCache: Scanning file cache for directory 'C:/Game/Auracron/Content/' took 0.00s
[2025.08.26-02.43.19:155][  0]LogNNEDenoiser: Ray Tracing is not enabled, therefore NNEDenoiser is not registered!
[2025.08.26-02.43.19:176][  0]LogPython: Python enabled via CVar 'Engine.Python.IsEnabledByDefault'
[2025.08.26-02.43.19:177][  0]LogPython: Using Python 3.11.8
[2025.08.26-02.43.19:199][  0]LogPython: Display: No pip-enabled plugins with python dependencies found, skipping
[2025.08.26-02.43.19:597][  0]LogLevelSequenceEditor: LevelSequenceEditor subsystem initialized.
[2025.08.26-02.43.19:634][  0]LogEditorDataStorage: Initializing
[2025.08.26-02.43.19:636][  0]LogEditorDataStorage: Initialized
[2025.08.26-02.43.19:640][  0]LogCore: Display: GameplayInsights module auto-connecting to local trace server...
[2025.08.26-02.43.19:641][  0]LogGameplayAbilityAudit: Selected GameplayAbilityAuditRow as the best Gameplay Ability Audit Functionality
[2025.08.26-02.43.19:720][  0]LogInit: Display: Engine is initialized. Leaving FEngineLoop::Init()
[2025.08.26-02.43.19:722][  0]SourceControl: Controle de revisão desabilitado
[2025.08.26-02.43.19:722][  0]LogUnrealEdMisc: Loading editor; pre map load, took 7.986
[2025.08.26-02.43.19:724][  0]Cmd: MAP LOAD FILE="../../../Engine/Content/Maps/Templates/OpenWorld.umap" TEMPLATE=1 SHOWPROGRESS=1 FEATURELEVEL=3
[2025.08.26-02.43.19:726][  0]LogWorld: UWorld::CleanupWorld for Untitled, bSessionEnded=true, bCleanupResources=true
[2025.08.26-02.43.19:726][  0]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.08.26-02.43.19:735][  0]LogAssetRegistry: Display: Triggering cache save on discovery complete
[2025.08.26-02.43.19:745][  0]LogAudio: Display: Audio Device unregistered from world 'None'.
[2025.08.26-02.43.19:747][  0]LogUObjectHash: Compacting FUObjectHashTables data took   0.51ms
[2025.08.26-02.43.19:754][  0]LogAudio: Display: Audio Device (ID: 1) registered with world 'Untitled_1'.
[2025.08.26-02.43.19:754][  0]LogChaosDD: Creating Chaos Debug Draw Scene for world Untitled_1
[2025.08.26-02.43.19:758][  0]LogWorldPartition: ULevel::OnLevelLoaded(Untitled_1)(bIsOwningWorldGameWorld=0, bIsOwningWorldPartitioned=1, InitializeForMainWorld=1, InitializeForEditor=1, InitializeForGame=0)
[2025.08.26-02.43.19:758][  0]LogWorldPartition: Display: WorldPartition initialize started...
[2025.08.26-02.43.19:758][  0]LogWorldPartition: UWorldPartition::Initialize : World = /Temp/Untitled_1.Untitled_1, World Type = Editor, IsMainWorldPartition = 1, Location = V(0), Rotation = R(0), IsEditor = 1, IsGame = 0, IsPIEWorldTravel = 0, IsCooking = 0
[2025.08.26-02.43.19:865][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.08.26-02.43.19:870][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.08.26-02.43.19:874][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.08.26-02.43.19:879][  0]LogDeviceProfileManager: Going up to parent DeviceProfile [AppleTV]
[2025.08.26-02.43.19:879][  0]LogDeviceProfileManager: Going up to parent DeviceProfile [IOS]
[2025.08.26-02.43.19:879][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.08.26-02.43.19:884][  0]LogDeviceProfileManager: Display: Deviceprofile None not found.
[2025.08.26-02.43.19:981][  0]LogWorldPartition: Display: WorldPartition initialize took 223.651 ms
[2025.08.26-02.43.20:124][  0]LogEditorServer: Finished looking for orphan Actors (0.000 secs)
[2025.08.26-02.43.20:144][  0]LogUObjectHash: Compacting FUObjectHashTables data took   0.45ms
[2025.08.26-02.43.20:145][  0]Cmd: MAP CHECKDEP NOCLEARLOG
[2025.08.26-02.43.20:147][  0]MapCheck: Verificação do mapa concluída: 0 erro(s), 0 aviso(s), levou 1,113ms para ser concluída.
[2025.08.26-02.43.20:154][  0]LogUnrealEdMisc: Total Editor Startup Time, took 8.418
[2025.08.26-02.43.20:232][  0]LogPlacementMode: Display: The Asset Registry is not yet fully loaded so some placeable classes might be missing.
[2025.08.26-02.43.20:250][  0]LogSlate: The tab "TopLeftModeTab" attempted to spawn in layout 'LevelEditor_Layout_v1.8' but failed for some reason. It will not be displayed.
[2025.08.26-02.43.20:338][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.08.26-02.43.20:414][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.08.26-02.43.20:493][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.08.26-02.43.20:574][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.08.26-02.43.20:659][  0]LogPakFile: Initializing PakPlatformFile
[2025.08.26-02.43.20:660][  0]LogPakFile: Display: Mounted Pak file 'C:/Program Files/Epic Games/UE_5.6/FeaturePacks/StarterContent.upack', mount point: 'root:/'
[2025.08.26-02.43.20:660][  0]LogPakFile: Initializing PakPlatformFile
[2025.08.26-02.43.20:660][  0]LogPakFile: Display: Mounted Pak file 'C:/Program Files/Epic Games/UE_5.6/FeaturePacks/TP_FirstPerson.upack', mount point: 'root:/'
[2025.08.26-02.43.20:661][  0]LogPakFile: Initializing PakPlatformFile
[2025.08.26-02.43.20:661][  0]LogPakFile: Display: Mounted Pak file 'C:/Program Files/Epic Games/UE_5.6/FeaturePacks/TP_FirstPersonBP.upack', mount point: 'root:/'
[2025.08.26-02.43.20:661][  0]LogPakFile: Initializing PakPlatformFile
[2025.08.26-02.43.20:661][  0]LogPakFile: Display: Mounted Pak file 'C:/Program Files/Epic Games/UE_5.6/FeaturePacks/TP_HandheldARBP.upack', mount point: 'root:/'
[2025.08.26-02.43.20:662][  0]LogPakFile: Initializing PakPlatformFile
[2025.08.26-02.43.20:662][  0]LogPakFile: Display: Mounted Pak file 'C:/Program Files/Epic Games/UE_5.6/FeaturePacks/TP_ThirdPerson.upack', mount point: 'root:/'
[2025.08.26-02.43.20:662][  0]LogPakFile: Initializing PakPlatformFile
[2025.08.26-02.43.20:662][  0]LogPakFile: Display: Mounted Pak file 'C:/Program Files/Epic Games/UE_5.6/FeaturePacks/TP_ThirdPersonBP.upack', mount point: 'root:/'
[2025.08.26-02.43.20:662][  0]LogPakFile: Initializing PakPlatformFile
[2025.08.26-02.43.20:662][  0]LogPakFile: Display: Mounted Pak file 'C:/Program Files/Epic Games/UE_5.6/FeaturePacks/TP_TopDown.upack', mount point: 'root:/'
[2025.08.26-02.43.20:664][  0]LogPakFile: Initializing PakPlatformFile
[2025.08.26-02.43.20:664][  0]LogPakFile: Display: Mounted Pak file 'C:/Program Files/Epic Games/UE_5.6/FeaturePacks/TP_TopDownBP.upack', mount point: 'root:/'
[2025.08.26-02.43.20:664][  0]LogPakFile: Initializing PakPlatformFile
[2025.08.26-02.43.20:664][  0]LogPakFile: Display: Mounted Pak file 'C:/Program Files/Epic Games/UE_5.6/FeaturePacks/TP_VehicleAdvBP.upack', mount point: 'root:/'
[2025.08.26-02.43.20:665][  0]LogPakFile: Initializing PakPlatformFile
[2025.08.26-02.43.20:665][  0]LogPakFile: Display: Mounted Pak file 'C:/Program Files/Epic Games/UE_5.6/FeaturePacks/TP_VirtualRealityBP.upack', mount point: 'root:/'
[2025.08.26-02.43.20:791][  0]LogAssetRegistry: Display: Asset registry cache written as 73.0 MiB to ../../../../../../Game/Auracron/Intermediate/CachedAssetRegistry_*.bin
[2025.08.26-02.43.20:909][  0]LogSlate: Took 0.000500 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Regular.ttf' (155K)
[2025.08.26-02.43.20:914][  0]LogSlate: Took 0.000352 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/DroidSansMono.ttf' (77K)
[2025.08.26-02.43.20:971][  0]LogSlate: Took 0.000313 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Bold.ttf' (160K)
[2025.08.26-02.43.21:148][  0]LogStall: Startup...
[2025.08.26-02.43.21:153][  0]LogStall: Startup complete.
[2025.08.26-02.43.21:159][  0]LogLoad: (Engine Initialization) Total time: 9.42 seconds
[2025.08.26-02.43.22:104][  0]LogContentStreaming: Texture pool size now 1000 MB
[2025.08.26-02.43.22:104][  0]LogCsvProfiler: Display: Metadata set : streamingpoolsizemb="1000"
[2025.08.26-02.43.22:163][  0]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.08.26-02.43.22:165][  0]LogPython: Display: Executando código de inicialização C:/Program Files/Epic Games/UE_5.6/Engine/Plugins/Animation/ControlRig/Content/Python/init_unreal.py... started...
[2025.08.26-02.43.22:202][  0]LogPython: registering <class 'ControlRigWorkflows.workflow_deformation_rig_preset.provider'>

[2025.08.26-02.43.22:206][  0]LogPython: Display: Executando código de inicialização C:/Program Files/Epic Games/UE_5.6/Engine/Plugins/Animation/ControlRig/Content/Python/init_unreal.py... took 41.701 ms
[2025.08.26-02.43.22:621][  1]LogAssetRegistry: AssetRegistryGather time 0.0970s: AssetDataDiscovery 0.0120s, AssetDataGather 0.0266s, StoreResults 0.0585s. Wall time 7.3290s.
	NumCachedDirectories 1423. NumUncachedDirectories 23. NumCachedFiles 7459. NumUncachedFiles 0.
	BackgroundTickInterruptions 0.
[2025.08.26-02.43.22:663][  1]LogPlacementMode: Display: The Asset Registry is done with its initial scan, the list of placeable classes has been updated.
[2025.08.26-02.43.22:673][  1]LogCollectionManager: Fixed up redirectors for 0 collections in 0.000000 seconds (updated 0 objects)
[2025.08.26-02.43.22:674][  1]LogSourceControl: Uncontrolled asset discovery started...
[2025.08.26-02.43.23:437][ 16]LogSourceControl: Uncontrolled asset discovery finished in 0.763445 seconds (Found 7435 uncontrolled assets)
[2025.08.26-02.43.25:503][ 72]LogSlate: Took 0.000268 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Light.ttf' (167K)
[2025.08.26-02.43.26:377][ 83]LogUObjectHash: Compacting FUObjectHashTables data took   0.74ms
[2025.08.26-02.43.26:380][ 83]Cmd: MAP LOAD FILE="../../../../../../Game/Auracron/Content/AURACRON.umap" TEMPLATE=0 SHOWPROGRESS=1 FEATURELEVEL=3
[2025.08.26-02.43.26:429][ 83]LogWorld: UWorld::CleanupWorld for Untitled_1, bSessionEnded=true, bCleanupResources=true
[2025.08.26-02.43.26:430][ 83]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.08.26-02.43.26:430][ 83]LogWorldPartition: UWorldPartition::Uninitialize : World = /Temp/Untitled_1.Untitled_1
[2025.08.26-02.43.26:465][ 83]LogAudio: Display: Audio Device unregistered from world 'None'.
[2025.08.26-02.43.26:480][ 83]LogUObjectHash: Compacting FUObjectHashTables data took   0.74ms
[2025.08.26-02.43.26:482][ 83]LogStreaming: Display: FlushAsyncLoading(479): 1 QueuedPackages, 0 AsyncPackages
[2025.08.26-02.43.26:483][ 83]LogAudio: Display: Audio Device (ID: 1) registered with world 'AURACRON'.
[2025.08.26-02.43.26:484][ 83]LogChaosDD: Creating Chaos Debug Draw Scene for world AURACRON
[2025.08.26-02.43.26:520][ 83]LogEditorServer: Finished looking for orphan Actors (0.000 secs)
[2025.08.26-02.43.26:544][ 83]LogUObjectHash: Compacting FUObjectHashTables data took   0.65ms
[2025.08.26-02.43.26:551][ 83]Cmd: MAP CHECKDEP NOCLEARLOG
[2025.08.26-02.43.26:551][ 83]MapCheck: Verificação do mapa concluída: 0 erro(s), 0 aviso(s), levou 0,063ms para ser concluída.
[2025.08.26-02.43.26:552][ 83]LogSlate: Window 'Abrir nível' being destroyed
[2025.08.26-02.44.09:924][431]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.44.09:924][431]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.44.09:924][431]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-02.44.10:025][432]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.44.10:025][432]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.44.10:025][432]LogTemp: Display: MCPServerRunnable: Received: {"type": "get_engine_version", "params": {}}
[2025.08.26-02.44.10:025][432]LogTemp: Display: UnrealMCPBridge: Executing command: get_engine_version
[2025.08.26-02.44.10:301][432]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"version": "5.6.1-44394996+++UE5+Release-5.6",
		"branch": "//UE5/Release-5.6",
		"major": 5,
		"minor": 6,
		"patch": 1
	}
}
[2025.08.26-02.44.10:301][432]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 176
[2025.08.26-02.44.10:301][432]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-02.44.10:402][433]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.44.10:402][433]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.44.10:402][433]LogTemp: Display: MCPServerRunnable: Received: {"type": "list_available_commands", "params": {}}
[2025.08.26-02.44.10:402][433]LogTemp: Display: UnrealMCPBridge: Executing command: list_available_commands
[2025.08.26-02.44.10:634][433]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"commands": [
			"ping",
			"get_engine_version",
			"get_project_name",
			"list_available_commands",
			"get_world_info",
			"get_level_info",
			"get_actors_in_level",
			"spawn_actor",
			"get_selected_actors",
			"set_actor_location",
			"create_blueprint_class",
			"create_actor_blueprint"
		]
	}
}
[2025.08.26-02.44.10:634][433]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 365
[2025.08.26-02.44.10:634][433]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-02.44.10:736][434]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.44.10:736][434]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.44.10:736][434]LogTemp: Display: MCPServerRunnable: Received: {"type": "get_world_info", "params": {}}
[2025.08.26-02.44.10:736][434]LogTemp: Display: UnrealMCPBridge: Executing command: get_world_info
[2025.08.26-02.44.10:968][434]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"world_name": "AURACRON",
		"world_type": "Editor",
		"is_game_world": false,
		"is_editor_world": true,
		"time_seconds": 34.920459676533937,
		"real_time_seconds": 34.920459676533937
	}
}
[2025.08.26-02.44.10:968][434]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 241
[2025.08.26-02.44.10:968][434]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-02.44.11:069][435]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.44.11:069][435]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.44.11:069][435]LogTemp: Display: MCPServerRunnable: Received: {"type": "get_level_info", "params": {}}
[2025.08.26-02.44.11:069][435]LogTemp: Display: UnrealMCPBridge: Executing command: get_level_info
[2025.08.26-02.44.11:301][435]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"level_name": "PersistentLevel",
		"package_name": "/Game/AURACRON",
		"actor_count": 8,
		"is_persistent_level": true
	}
}
[2025.08.26-02.44.11:301][435]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 173
[2025.08.26-02.44.11:301][435]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-02.44.11:402][436]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.44.11:402][436]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.44.11:402][436]LogTemp: Display: MCPServerRunnable: Received: {"type": "spawn_actor", "params": {"type": "StaticMeshActor", "name": "TestActor_Fix_001", "location": [400, 0, 100]}}
[2025.08.26-02.44.11:402][436]LogTemp: Display: UnrealMCPBridge: Executing command: spawn_actor
[2025.08.26-02.44.11:636][436]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"name": "TestActor_Fix_001",
		"class": "StaticMeshActor",
		"location": [ 400, 0, 100 ],
		"rotation": [ 0, 0, 0 ],
		"scale": [ 1, 1, 1 ]
	}
}
[2025.08.26-02.44.11:636][436]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 195
[2025.08.26-02.44.11:636][436]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-02.44.23:010][471]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.44.23:010][471]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.44.23:010][471]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-02.44.23:112][471]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.44.23:112][471]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.44.23:112][471]LogTemp: Display: MCPServerRunnable: Received: {"type": "ping", "params": {}}
[2025.08.26-02.44.23:112][471]LogTemp: Display: UnrealMCPBridge: Executing command: ping
[2025.08.26-02.44.23:303][471]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"message": "pong"
	}
}
[2025.08.26-02.44.23:304][471]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 69
[2025.08.26-02.44.23:304][471]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-02.44.23:404][472]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.44.23:404][472]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.44.23:405][472]LogTemp: Display: MCPServerRunnable: Received: {"type": "get_engine_version", "params": {}}
[2025.08.26-02.44.23:405][472]LogTemp: Display: UnrealMCPBridge: Executing command: get_engine_version
[2025.08.26-02.44.23:637][472]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"version": "5.6.1-44394996+++UE5+Release-5.6",
		"branch": "//UE5/Release-5.6",
		"major": 5,
		"minor": 6,
		"patch": 1
	}
}
[2025.08.26-02.44.23:639][472]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 176
[2025.08.26-02.44.23:639][472]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-02.44.23:739][473]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.44.23:739][473]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.44.23:739][473]LogTemp: Display: MCPServerRunnable: Received: {"type": "get_project_name", "params": {}}
[2025.08.26-02.44.23:739][473]LogTemp: Display: UnrealMCPBridge: Executing command: get_project_name
[2025.08.26-02.44.23:970][473]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"project_name": "Auracron",
		"game_name": "Auracron"
	}
}
[2025.08.26-02.44.23:970][473]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 106
[2025.08.26-02.44.23:970][473]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-02.44.24:071][474]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.44.24:071][474]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.44.24:071][474]LogTemp: Display: MCPServerRunnable: Received: {"type": "list_available_commands", "params": {}}
[2025.08.26-02.44.24:071][474]LogTemp: Display: UnrealMCPBridge: Executing command: list_available_commands
[2025.08.26-02.44.24:303][474]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"commands": [
			"ping",
			"get_engine_version",
			"get_project_name",
			"list_available_commands",
			"get_world_info",
			"get_level_info",
			"get_actors_in_level",
			"spawn_actor",
			"get_selected_actors",
			"set_actor_location",
			"create_blueprint_class",
			"create_actor_blueprint"
		]
	}
}
[2025.08.26-02.44.24:303][474]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 365
[2025.08.26-02.44.24:303][474]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-02.44.24:404][475]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.44.24:405][475]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.44.24:405][475]LogTemp: Display: MCPServerRunnable: Received: {"type": "get_world_info", "params": {}}
[2025.08.26-02.44.24:405][475]LogTemp: Display: UnrealMCPBridge: Executing command: get_world_info
[2025.08.26-02.44.24:637][475]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"world_name": "AURACRON",
		"world_type": "Editor",
		"is_game_world": false,
		"is_editor_world": true,
		"time_seconds": 48.58964179828763,
		"real_time_seconds": 48.58964179828763
	}
}
[2025.08.26-02.44.24:638][475]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 239
[2025.08.26-02.44.24:638][475]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-02.44.24:738][476]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.44.24:738][476]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.44.24:738][476]LogTemp: Display: MCPServerRunnable: Received: {"type": "get_level_info", "params": {}}
[2025.08.26-02.44.24:738][476]LogTemp: Display: UnrealMCPBridge: Executing command: get_level_info
[2025.08.26-02.44.24:971][476]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"level_name": "PersistentLevel",
		"package_name": "/Game/AURACRON",
		"actor_count": 9,
		"is_persistent_level": true
	}
}
[2025.08.26-02.44.24:971][476]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 173
[2025.08.26-02.44.24:971][476]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-02.44.25:072][477]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.44.25:072][477]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.44.25:072][477]LogTemp: Display: MCPServerRunnable: Received: {"type": "get_actors_in_level", "params": {"limit": 5}}
[2025.08.26-02.44.25:072][477]LogTemp: Display: UnrealMCPBridge: Executing command: get_actors_in_level
[2025.08.26-02.44.25:305][477]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"actors": [
			{
				"name": "WorldSettings",
				"class": "WorldSettings",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "Brush_0",
				"class": "Brush",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "MassVisualizer_0",
				"class": "MassVisualizer",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "DefaultPhysicsVolume_0",
				"class": "DefaultPhysicsVolume",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "GameplayDebuggerPlayerManager_0",
				"class": "GameplayDebuggerPlayerManager",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			}
		],
		"total_actors": 9,
		"returned_actors": 5,
		"limited": true
	}
}
[2025.08.26-02.44.25:305][477]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 974
[2025.08.26-02.44.25:305][477]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-02.44.25:406][478]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.44.25:406][478]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.44.25:406][478]LogTemp: Display: MCPServerRunnable: Received: {"type": "get_selected_actors", "params": {}}
[2025.08.26-02.44.25:406][478]LogTemp: Display: UnrealMCPBridge: Executing command: get_selected_actors
[2025.08.26-02.44.25:637][478]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"actors": [],
		"count": 0
	}
}
[2025.08.26-02.44.25:637][478]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 79
[2025.08.26-02.44.25:637][478]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-02.44.25:738][479]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.44.25:738][479]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.44.25:738][479]LogTemp: Display: MCPServerRunnable: Received: {"type": "spawn_actor", "params": {"type": "StaticMeshActor", "name": "TestActor_Robust_001", "location": [200, 0, 100]}}
[2025.08.26-02.44.25:738][479]LogTemp: Display: UnrealMCPBridge: Executing command: spawn_actor
[2025.08.26-02.44.25:971][479]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"name": "TestActor_Robust_001",
		"class": "StaticMeshActor",
		"location": [ 200, 0, 100 ],
		"rotation": [ 0, 0, 0 ],
		"scale": [ 1, 1, 1 ]
	}
}
[2025.08.26-02.44.25:971][479]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 198
[2025.08.26-02.44.25:971][479]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-02.44.26:072][480]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.44.26:072][480]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.44.26:072][480]LogTemp: Display: MCPServerRunnable: Received: {"type": "spawn_actor", "params": {"type": "PointLight", "name": "TestLight_Robust_001", "location": [300, 0, 150]}}
[2025.08.26-02.44.26:072][480]LogTemp: Display: UnrealMCPBridge: Executing command: spawn_actor
[2025.08.26-02.44.26:305][480]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"name": "TestLight_Robust_001",
		"class": "PointLight",
		"location": [ 300, 0, 150 ],
		"rotation": [ 0, 0, 0 ],
		"scale": [ 1, 1, 1 ]
	}
}
[2025.08.26-02.44.26:305][480]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 193
[2025.08.26-02.44.26:305][480]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-02.44.26:407][481]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.44.26:407][481]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.44.26:407][481]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_blueprint_class", "params": {"name": "BP_TestClass_Robust", "parent_class": "Actor", "package_path": "/Game/Blueprints/"}}
[2025.08.26-02.44.26:407][481]LogTemp: Display: UnrealMCPBridge: Executing command: create_blueprint_class
[2025.08.26-02.44.26:642][481]LogFileHelpers: InternalPromptForCheckoutAndSave started...
[2025.08.26-02.44.26:699][481]OBJ SavePackage: Generating thumbnails for [0] asset(s) in package [/Game/Blueprints/BP_TestClass_Robust] ([2] browsable assets)...
[2025.08.26-02.44.26:699][481]OBJ SavePackage: Finished generating thumbnails for package [/Game/Blueprints/BP_TestClass_Robust]
[2025.08.26-02.44.26:699][481]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Blueprints/BP_TestClass_Robust" FILE="../../../../../../Game/Auracron/Content/Blueprints/BP_TestClass_Robust.uasset" SILENT=true
[2025.08.26-02.44.26:711][481]LogSavePackage: Moving output files for package: /Game/Blueprints/BP_TestClass_Robust
[2025.08.26-02.44.26:711][481]LogSavePackage: Moving '../../../../../../Game/Auracron/Saved/BP_TestClass_RobustB7698C6540E7FB8A34EAA0ADA9BA85DF.tmp' to '../../../../../../Game/Auracron/Content/Blueprints/BP_TestClass_Robust.uasset'
[2025.08.26-02.44.26:727][481]LogFileHelpers: InternalPromptForCheckoutAndSave took 85.831 ms
[2025.08.26-02.44.26:727][481]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"name": "BP_TestClass_Robust",
		"path": "/Game/Blueprints/BP_TestClass_Robust",
		"parent_class": "Actor",
		"saved": true
	}
}
[2025.08.26-02.44.26:727][481]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 178
[2025.08.26-02.44.26:728][481]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-02.44.26:734][481]LogSourceControl: Display: Uncontrolled Changelist persistency file saved ../../../../../../Game/Auracron/Saved/SourceControl/UncontrolledChangelists.json
[2025.08.26-02.44.26:829][482]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.44.26:829][482]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.44.26:829][482]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_actor_blueprint", "params": {"name": "BP_TestActor_Robust", "package_path": "/Game/Blueprints/Actors/"}}
[2025.08.26-02.44.26:829][482]LogTemp: Display: UnrealMCPBridge: Executing command: create_actor_blueprint
[2025.08.26-02.44.26:972][482]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.26-02.44.26:973][482]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.26-02.44.26:974][482]LogFileHelpers: InternalPromptForCheckoutAndSave started...
[2025.08.26-02.44.26:974][482]OBJ SavePackage: Generating thumbnails for [0] asset(s) in package [/Game/Blueprints/Actors/BP_TestActor_Robust] ([2] browsable assets)...
[2025.08.26-02.44.26:974][482]OBJ SavePackage: Finished generating thumbnails for package [/Game/Blueprints/Actors/BP_TestActor_Robust]
[2025.08.26-02.44.26:974][482]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Blueprints/Actors/BP_TestActor_Robust" FILE="../../../../../../Game/Auracron/Content/Blueprints/Actors/BP_TestActor_Robust.uasset" SILENT=true
[2025.08.26-02.44.26:982][482]LogSavePackage: Moving output files for package: /Game/Blueprints/Actors/BP_TestActor_Robust
[2025.08.26-02.44.26:983][482]LogSavePackage: Moving '../../../../../../Game/Auracron/Saved/BP_TestActor_Robust20D339464FCA032A48D61CA3013E7A7A.tmp' to '../../../../../../Game/Auracron/Content/Blueprints/Actors/BP_TestActor_Robust.uasset'
[2025.08.26-02.44.26:986][482]LogFileHelpers: InternalPromptForCheckoutAndSave took 11.933 ms (total: 97.765 ms)
[2025.08.26-02.44.26:986][482]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"name": "BP_TestActor_Robust",
		"path": "/Game/Blueprints/Actors/BP_TestActor_Robust",
		"parent_class": "Actor",
		"saved": true
	}
}
[2025.08.26-02.44.26:986][482]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 185
[2025.08.26-02.44.26:986][482]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-02.44.27:047][482]LogContentValidation: Display: Starting to validate 1 assets
[2025.08.26-02.44.27:047][482]LogContentValidation: Enabled validators:
[2025.08.26-02.44.27:047][482]LogContentValidation: 	/Script/DataValidation.EditorValidator_Material
[2025.08.26-02.44.27:047][482]LogContentValidation: 	/Script/DataValidation.DirtyFilesChangelistValidator
[2025.08.26-02.44.27:047][482]LogContentValidation: 	/Script/DataValidation.EditorValidator_Localization
[2025.08.26-02.44.27:047][482]LogContentValidation: 	/Script/DataValidation.PackageFileValidator
[2025.08.26-02.44.27:047][482]LogContentValidation: 	/Script/DataValidation.WorldPartitionChangelistValidator
[2025.08.26-02.44.27:047][482]LogContentValidation: 	/Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
[2025.08.26-02.44.27:047][482]AssetCheck: /Game/Blueprints/BP_TestClass_Robust Validando ativo
[2025.08.26-02.44.27:068][482]LogSourceControl: Display: Uncontrolled Changelist persistency file saved ../../../../../../Game/Auracron/Saved/SourceControl/UncontrolledChangelists.json
[2025.08.26-02.45.14:123][624]LogDerivedDataCache: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Maintenance finished in +00:00:00.000 and deleted 0 files with total size 0 MiB and 0 empty folders. Scanned 0 files in 1 folders with total size 0 MiB.
[2025.08.26-02.46.36:598][ 62]LogSlate: Window 'Abrir nível' being destroyed
[2025.08.26-02.46.42:985][221]LogUObjectHash: Compacting FUObjectHashTables data took   0.58ms
[2025.08.26-02.46.43:183][221]LogSlate: Last resort fallback font was requested. Font: '<unknown>', Character: '<unknown>'
[2025.08.26-02.46.43:185][221]LogSlate: Took 0.001844 seconds to synchronously load lazily loaded font '../../../Engine/Content/SlateDebug/Fonts/LastResort.ttf' (5269K)
[2025.08.26-02.46.46:005][221]LogSlate: Window 'Delete Assets' being destroyed
[2025.08.26-02.46.46:046][221]LogUObjectHash: Compacting FUObjectHashTables data took   0.47ms
[2025.08.26-02.46.46:064][221]LogUObjectHash: Compacting FUObjectHashTables data took   0.39ms
[2025.08.26-02.46.46:081][221]LogUObjectHash: Compacting FUObjectHashTables data took   0.23ms
[2025.08.26-02.46.50:627][460]LogUObjectHash: Compacting FUObjectHashTables data took   0.24ms
[2025.08.26-02.46.51:809][460]LogSlate: Window 'Delete Assets' being destroyed
[2025.08.26-02.46.51:847][460]LogUObjectHash: Compacting FUObjectHashTables data took   0.39ms
[2025.08.26-02.46.51:866][460]LogUObjectHash: Compacting FUObjectHashTables data took   0.24ms
[2025.08.26-02.46.51:884][460]LogUObjectHash: Compacting FUObjectHashTables data took   0.35ms
[2025.08.26-02.46.58:532][605]Cmd: DELETE
[2025.08.26-02.46.58:532][605]Cmd: ACTOR DELETE
[2025.08.26-02.46.58:535][605]LogEditorActor: Deleted Actor: StaticMeshActor
[2025.08.26-02.46.58:536][605]LogEditorActor: Deleted Actor: StaticMeshActor
[2025.08.26-02.46.58:537][605]LogEditorActor: Deleted Actor: PointLight
[2025.08.26-02.46.58:567][605]LogUObjectHash: Compacting FUObjectHashTables data took   0.47ms
[2025.08.26-02.46.58:569][605]LogEditorActor: Deleted 3 Actors (0.037 secs)
[2025.08.26-02.48.41:272][105]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.48.41:272][105]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.48.41:272][105]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-02.48.41:373][105]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.48.41:373][105]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.48.41:373][105]LogTemp: Display: MCPServerRunnable: Received: {"type": "get_actors_in_level", "params": {}}
[2025.08.26-02.48.41:373][105]LogTemp: Display: UnrealMCPBridge: Executing command: get_actors_in_level
[2025.08.26-02.48.41:518][105]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"actors": [
			{
				"name": "WorldSettings",
				"class": "WorldSettings",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "Brush_0",
				"class": "Brush",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "MassVisualizer_0",
				"class": "MassVisualizer",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "DefaultPhysicsVolume_0",
				"class": "DefaultPhysicsVolume",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "GameplayDebuggerPlayerManager_0",
				"class": "GameplayDebuggerPlayerManager",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "ChaosDebugDrawActor",
				"class": "Actor",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "SmartObjectSubsystemRenderingActor_0",
				"class": "SmartObjectSubsystemRenderingActor",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "AbstractNavData-Default",
				"class": "AbstractNavData",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			}
		],
		"total_actors": 8,
		"returned_actors": 8,
		"limited": false
	}
}
[2025.08.26-02.48.41:519][105]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 1509
[2025.08.26-02.48.41:519][105]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-02.50.27:216][680]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.50.27:216][680]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.50.27:216][680]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-02.50.27:317][680]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.50.27:317][680]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.50.27:317][680]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_planicie_radiante_layer", "params": {"layer_name": "planicie_radiante", "z_range": {"min": 0, "max": 2000}, "enable_light_crystals": true, "solar_tower_enhancement": true, "regeneration_buffs": true, "jungle_camps_count": 24, "lane_layout": {"top": true, "mid": true, "bot": true}, "special_mechanics": ["accelerated_minion_growth_near_crystals", "solar_enhanced_towers", "regeneration_sustain_buffs"]}}
[2025.08.26-02.50.27:317][680]LogTemp: Display: UnrealMCPBridge: Executing command: create_planicie_radiante_layer
[2025.08.26-02.50.27:510][680]LogTemp: Criando camada Planície Radiante
[2025.08.26-02.50.27:510][680]LogTemp: Camada Planície Radiante criada com sucesso
[2025.08.26-02.50.27:510][680]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"status": "success",
		"message": "Camada Planície Radiante criada com sucesso",
		"data":
		{
			"layer_config":
			{
				"layer_name": "planicie_radiante",
				"z_min": 0,
				"z_max": 2000,
				"enable_light_crystals": true,
				"solar_tower_enhancement": true,
				"regeneration_buffs": true,
				"jungle_camps_count": 24,
				"special_mechanics": [
					"accelerated_minion_growth_near_crystals",
					"solar_enhanced_towers",
					"regeneration_sustain_buffs"
				],
				"lane_layout":
				{
					"top": true,
					"mid": true,
					"bot": true
				}
			},
			"layer_state":
			{
				"status": "created",
				"creation_time": "2025.08.25-23.50.27",
				"active": true
			},
			"layer_type": "Planície Radiante - Camada Inferior",
			"description": "Camada familiar com 3 lanes tradicionais e jungle terrestre"
		},
		"timestamp": "2025.08.25-23.50.27"
	}
}
[2025.08.26-02.50.27:510][680]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 945
[2025.08.26-02.50.27:510][680]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-02.50.35:867][706]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.50.35:867][706]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.50.35:867][706]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-02.50.35:968][706]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.50.35:968][706]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.50.35:968][706]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_firmamento_zephyr_layer", "params": {"layer_name": "firmamento_zephyr", "z_range": {"min": 2000, "max": 4000}, "wind_currents_count": 2, "central_zone": true, "mobile_platforms": true, "wind_speed_boost": 0.3, "platform_change_interval": 180, "enable_vortices": true, "special_mechanics": ["dynamic_layout_wind_currents", "mobile_platforms_3min_cycle", "wind_acceleration_30_percent", "floating_energy_bridges", "rapid_movement_vortices"]}}
[2025.08.26-02.50.35:968][706]LogTemp: Display: UnrealMCPBridge: Executing command: create_firmamento_zephyr_layer
[2025.08.26-02.50.36:184][706]LogTemp: Criando camada Firmamento Zephyr
[2025.08.26-02.50.36:185][706]LogTemp: Camada Firmamento Zephyr criada com sucesso
[2025.08.26-02.50.36:185][706]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"status": "success",
		"message": "Camada Firmamento Zephyr criada com sucesso",
		"data":
		{
			"layer_config":
			{
				"layer_name": "firmamento_zephyr",
				"z_min": 2000,
				"z_max": 4000,
				"wind_currents_count": 2,
				"central_zone": true,
				"mobile_platforms": true,
				"wind_speed_boost": 0.30000001192092896,
				"platform_change_interval": 180,
				"enable_vortices": true,
				"special_mechanics": [
					"dynamic_layout_wind_currents",
					"mobile_platforms_3min_cycle",
					"wind_acceleration_30_percent",
					"floating_energy_bridges",
					"rapid_movement_vortices"
				]
			},
			"layer_state":
			{
				"status": "created",
				"creation_time": "2025.08.25-23.50.36",
				"active": true
			},
			"layer_type": "Firmamento Zephyr - Camada Média",
			"description": "Camada dinâmica com correntes de vento e plataformas móveis"
		},
		"timestamp": "2025.08.25-23.50.36"
	}
}
[2025.08.26-02.50.36:185][706]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 983
[2025.08.26-02.50.36:185][706]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-02.50.40:820][720]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.50.40:820][720]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.50.40:820][720]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-02.50.40:921][721]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.50.40:921][721]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.50.40:921][721]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_abismo_umbral_layer", "params": {"layer_name": "abismo_umbral", "z_range": {"min": 4000, "max": 6000}, "labyrinthine_layout": true, "shadow_zones": true, "visibility_reduction": 0.5, "shadow_fog": true, "proximity_traps": true, "secret_chambers": true, "special_mechanics": ["labyrinthine_corridors_chambers", "shadow_zones_50_percent_visibility", "concealing_shadow_fog", "proximity_environmental_traps", "secret_reward_chambers"]}}
[2025.08.26-02.50.40:921][721]LogTemp: Display: UnrealMCPBridge: Executing command: create_abismo_umbral_layer
[2025.08.26-02.50.41:185][721]LogTemp: Criando camada Abismo Umbral
[2025.08.26-02.50.41:185][721]LogTemp: Camada Abismo Umbral criada com sucesso
[2025.08.26-02.50.41:186][721]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"status": "success",
		"message": "Camada Abismo Umbral criada com sucesso",
		"data":
		{
			"layer_config":
			{
				"layer_name": "abismo_umbral",
				"z_min": 4000,
				"z_max": 6000,
				"labyrinthine_layout": true,
				"shadow_zones": true,
				"visibility_reduction": 0.5,
				"shadow_fog": true,
				"proximity_traps": true,
				"secret_chambers": true,
				"special_mechanics": [
					"labyrinthine_corridors_chambers",
					"shadow_zones_50_percent_visibility",
					"concealing_shadow_fog",
					"proximity_environmental_traps",
					"secret_reward_chambers"
				]
			},
			"layer_state":
			{
				"status": "created",
				"creation_time": "2025.08.25-23.50.41",
				"active": true
			},
			"layer_type": "Abismo Umbral - Camada Superior",
			"description": "Camada labiríntica com zonas de sombra e armadilhas"
		},
		"timestamp": "2025.08.25-23.50.41"
	}
}
[2025.08.26-02.50.41:186][721]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 949
[2025.08.26-02.50.41:186][721]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-02.51.10:070][971]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.51.10:070][971]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.51.10:070][971]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-02.51.10:172][972]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.51.10:172][972]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.51.10:172][972]LogTemp: Display: MCPServerRunnable: Received: {"type": "setup_complete_multilayer_system", "params": {"enable_all_layers": true, "auto_connect_layers": true, "planicie_config": {"enable_light_crystals": true, "solar_tower_enhancement": true}, "firmamento_config": {"wind_currents_count": 2, "mobile_platforms": true}, "abismo_config": {"labyrinthine_layout": true, "shadow_zones": true}}}
[2025.08.26-02.51.10:172][972]LogTemp: Display: UnrealMCPBridge: Executing command: setup_complete_multilayer_system
[2025.08.26-02.51.10:462][972]LogTemp: Configurando sistema completo de três camadas
[2025.08.26-02.51.10:462][972]LogTemp: Criando camada Planície Radiante
[2025.08.26-02.51.10:462][972]LogTemp: Camada Planície Radiante criada com sucesso
[2025.08.26-02.51.10:462][972]LogTemp: Criando camada Firmamento Zephyr
[2025.08.26-02.51.10:462][972]LogTemp: Camada Firmamento Zephyr criada com sucesso
[2025.08.26-02.51.10:462][972]LogTemp: Criando camada Abismo Umbral
[2025.08.26-02.51.10:462][972]LogTemp: Camada Abismo Umbral criada com sucesso
[2025.08.26-02.51.10:462][972]LogTemp: Conectividade entre camadas configurada automaticamente
[2025.08.26-02.51.10:462][972]LogTemp: Sistema completo de mapa multicamada configurado com sucesso
[2025.08.26-02.51.10:463][972]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"status": "success",
		"message": "Sistema completo de mapa multicamada configurado com sucesso",
		"data":
		{
			"system_config":
			{
				"created_layers": [
					{
						"status": "success",
						"message": "Camada Planície Radiante criada com sucesso",
						"data":
						{
							"layer_config":
							{
								"layer_name": "planicie_radiante",
								"z_min": 0,
								"z_max": 2000,
								"enable_light_crystals": true,
								"solar_tower_enhancement": true,
								"regeneration_buffs": true,
								"jungle_camps_count": 24,
								"special_mechanics": [
									"accelerated_minion_growth_near_crystals",
									"solar_enhanced_towers",
									"regeneration_sustain_buffs"
								],
								"lane_layout":
								{
									"top": true,
									"mid": true,
									"bot": true
								}
							},
							"layer_state":
							{
								"status": "created",
								"creation_time": "2025.08.25-23.51.10",
								"active": true
							},
							"layer_type": "Planície Radiante - Camada Inferior",
							"description": "Camada familiar com 3 lanes tradicionais e jungle terrestre"
						},
						"timestamp": "2025.08.25-23.51.10"
					},
					{
						"status": "success",
						"message": "Camada Firmamento Zephyr criada com sucesso",
						"data":
						{
							"layer_config":
							{
								"layer_name": "firmamento_zephyr",
								"z_min": 2000,
								"z_max": 4000,
								"wind_currents_count": 2,
								"central_zone": true,
								"mobile_platforms": true,
								"wind_speed_boost": 0.30000001192092896,
								"platform_change_interval": 180,
								"enable_vortices": true,
								"special_mechanics": [
									"dynamic_layout_wind_currents",
									"mobile_platforms_3min_cycle",
									"wind_acceleration_30_percent",
									"floating_energy_bridges",
									"rapid_movement_vortices"
								]
							},
							"layer_state":
							{
								"status": "created",
								"creation_time": "2025.08.25-23.51.10",
								"active": true
							},
							"layer_type": "Firmamento Zephyr - Camada Média",
							"description": "Camada dinâmica com correntes de vento e plataformas móveis"
						},
						"timestamp": "2025.08.25-23.51.10"
					},
					{
						"status": "success",
						"message": "Camada Abismo Umbral criada com sucesso",
						"data":
						{
							"layer_config":
							{
								"layer_name": "abismo_umbral",
								"z_min": 4000,
								"z_max": 6000,
								"labyrinthine_layout": true,
								"shadow_zones": true,
								"visibility_reduction": 0.5,
								"shadow_fog": true,
								"proximity_traps": true,
								"secret_chambers": true,
								"special_mechanics": [
									"labyrinthine_corridors_chambers",
									"shadow_zones_50_percent_visibility",
									"concealing_shadow_fog",
									"proximity_environmental_traps",
									"secret_reward_chambers"
								]
							},
							"layer_state":
							{
								"status": "created",
								"creation_time": "2025.08.25-23.51.10",
								"active": true
							},
							"layer_type": "Abismo Umbral - Camada Superior",
							"description": "Camada labiríntica com zonas de sombra e armadilhas"
						},
						"timestamp": "2025.08.25-23.51.10"
					}
				],
				"auto_connect_layers": true,
				"total_layers": 3,
				"system_status": "fully_configured",
				"creation_time": "2025.08.25-23.51.10",
				"connectivity":
				{
					"portals_per_layer": 4,
					"elevators_total": 4,
					"dimensional_bridges_enabled": true
				}
			},
			"system_type": "AURACRON Multilayer Map System",
			"description": "Sistema completo de três camadas interconectadas"
		},
		"timestamp": "2025.08.25-23.51.10"
	}
}
[2025.08.26-02.51.10:463][972]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 3868
[2025.08.26-02.51.10:463][972]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-02.51.24:053][ 13]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.51.24:053][ 13]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.51.24:053][ 13]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-02.51.24:153][ 14]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.51.24:153][ 14]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.51.24:153][ 14]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_primary_portals_system", "params": {"system_name": "primary_portals_system", "portals_per_layer": 4, "activation_time": 3, "cooldown_time": 10, "enable_combat_restriction": true, "portal_connections": {"planicie_to_firmamento": {"count": 4, "locations": ["Norte", "Sul", "Leste", "Oeste"], "activation_cost": "Nenhum", "travel_time": 2.0}, "firmamento_to_abismo": {"count": 4, "locations": ["Norte", "Sul", "Leste", "Oeste"], "activation_cost": "Nenhum", "travel_time": 2.5}, "planicie_to_abismo": {"count": 2, "locations": ["Central Norte", "Central Sul"], "activation_cost": "50 Mana", "travel_time": 4.0, "special_requirement": "N\u00edvel 6+"}}, "portal_mechanics": ["instant_travel_between_layers", "combat_restriction_active", "cooldown_per_champion", "visual_telegraph_3_seconds", "strategic_positioning_required"]}}
[2025.08.26-02.51.24:153][ 14]LogTemp: Display: UnrealMCPBridge: Executing command: create_primary_portals_system
[2025.08.26-02.51.24:466][ 14]LogTemp: Criando sistema de portais primários
[2025.08.26-02.51.24:466][ 14]LogTemp: Sistema de portais primários criado com sucesso
[2025.08.26-02.51.24:466][ 14]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"status": "success",
		"message": "Sistema de portais primários criado com sucesso",
		"data":
		{
			"system_config":
			{
				"system_name": "primary_portals_system",
				"portals_per_layer": 4,
				"cooldown_seconds": 10,
				"travel_time_seconds": 2,
				"enable_all_layers": true,
				"layer_portals":
				{
					"planicie_radiante":
					{
						"layer_name": "planicie_radiante",
						"portal_count": 4,
						"portal_type": "terrestrial",
						"connections": [
							"firmamento_zephyr",
							"abismo_umbral"
						],
						"locations": [
							"Próximo à Top Lane",
							"Próximo à Mid Lane",
							"Próximo à Bot Lane",
							"Centro da Jungle"
						]
					},
					"firmamento_zephyr":
					{
						"layer_name": "firmamento_zephyr",
						"portal_count": 4,
						"portal_type": "aerial",
						"connections": [
							"planicie_radiante",
							"abismo_umbral"
						],
						"locations": [
							"Corrente de Vento Norte",
							"Corrente de Vento Sul",
							"Zona Central",
							"Plataforma Móvel"
						]
					},
					"abismo_umbral":
					{
						"layer_name": "abismo_umbral",
						"portal_count": 4,
						"portal_type": "underground",
						"connections": [
							"planicie_radiante",
							"firmamento_zephyr"
						],
						"locations": [
							"Corredor Principal",
							"Câmara Secreta",
							"Zona de Sombras",
							"Túnel Labiríntico"
						]
					}
				},
				"portal_mechanics":
				{
					"activation_time": 1,
					"travel_time": 2,
					"cooldown_time": 10,
					"interruptible": true,
					"visible_to_enemies": true,
					"strategic_uses": [
						"Escape Routes",
						"Flanking",
						"Objective Control",
						"Split Push"
					]
				}
			},
			"system_state":
			{
				"status": "created",
				"creation_time": "2025.08.25-23.51.24",
				"active": true,
				"total_portals": 12
			},
			"system_description": "Sistema de portais primários para navegação rápida entre camadas"
		},
		"timestamp": "2025.08.25-23.51.24"
	}
}
[2025.08.26-02.51.24:466][ 14]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 2137
[2025.08.26-02.51.24:466][ 14]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-02.51.29:098][ 28]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.51.29:098][ 28]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.51.29:098][ 28]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-02.51.29:198][ 29]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.51.29:198][ 29]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.51.29:198][ 29]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_mystic_elevators_system", "params": {"system_name": "mystic_elevators_system", "total_elevators": 4, "travel_time": 5, "capacity": 5, "enable_combat_usage": false, "elevator_locations": {"elevator_1": {"name": "Elevador Cristalino", "position": "Nordeste", "connects": ["Plan\u00edcie Radiante", "Firmamento Zephyr"], "special_effect": "Regenera\u00e7\u00e3o durante viagem"}, "elevator_2": {"name": "Elevador Sombrio", "position": "Noroeste", "connects": ["Firmamento Zephyr", "Abismo Umbral"], "special_effect": "Invisibilidade tempor\u00e1ria"}, "elevator_3": {"name": "Elevador Ventoso", "position": "Sudeste", "connects": ["Plan\u00edcie Radiante", "Firmamento Zephyr"], "special_effect": "Velocidade aumentada"}, "elevator_4": {"name": "Elevador Abissal", "position": "Sudoeste", "connects": ["Todas as camadas"], "special_effect": "Teletransporte direto", "activation_requirement": "Controle de equipe"}}, "elevator_mechanics": ["slower_than_portals_but_safer", "team_coordination_advantage", "special_buffs_during_travel", "strategic_control_points", "multi_champion_transport"]}}
[2025.08.26-02.51.29:198][ 29]LogTemp: Display: UnrealMCPBridge: Executing command: create_mystic_elevators_system
[2025.08.26-02.51.29:467][ 29]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "error",
	"error": "Unknown command: create_mystic_elevators_system"
}
[2025.08.26-02.51.29:467][ 29]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 87
[2025.08.26-02.51.29:467][ 29]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-02.51.50:494][269]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.51.50:494][269]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.51.50:494][269]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-02.51.50:595][271]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.51.50:595][271]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.51.50:595][271]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_mystic_elevators_system", "params": {"system_name": "mystic_elevators_system", "total_elevators": 4, "travel_time": 5, "capacity": 5, "enable_combat_usage": false, "elevator_locations": {"elevator_1": {"name": "Elevador Cristalino", "position": "Nordeste", "connects": ["Plan\u00edcie Radiante", "Firmamento Zephyr"], "special_effect": "Regenera\u00e7\u00e3o durante viagem"}, "elevator_2": {"name": "Elevador Sombrio", "position": "Noroeste", "connects": ["Firmamento Zephyr", "Abismo Umbral"], "special_effect": "Invisibilidade tempor\u00e1ria"}, "elevator_3": {"name": "Elevador Ventoso", "position": "Sudeste", "connects": ["Plan\u00edcie Radiante", "Firmamento Zephyr"], "special_effect": "Velocidade aumentada"}, "elevator_4": {"name": "Elevador Abissal", "position": "Sudoeste", "connects": ["Todas as camadas"], "special_effect": "Teletransporte direto", "activation_requirement": "Controle de equipe"}}, "elevator_mechanics": ["slower_than_portals_but_safer", "team_coordination_advantage", "special_buffs_during_travel", "strategic_control_points", "multi_champion_transport"]}}
[2025.08.26-02.51.50:595][271]LogTemp: Display: UnrealMCPBridge: Executing command: create_mystic_elevators_system
[2025.08.26-02.51.50:596][271]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "error",
	"error": "Unknown command: create_mystic_elevators_system"
}
[2025.08.26-02.51.50:596][271]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 87
[2025.08.26-02.51.50:596][271]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-02.52.12:435][388]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.52.12:435][388]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.52.12:435][388]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-02.52.12:536][388]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.52.12:536][388]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.52.12:536][388]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_mystical_elevators_system", "params": {"total_elevators": 4, "travel_time": 5.0, "capacity": 5, "enable_combat_usage": false}}
[2025.08.26-02.52.12:536][388]LogTemp: Display: UnrealMCPBridge: Executing command: create_mystical_elevators_system
[2025.08.26-02.52.12:686][388]LogTemp: Criando sistema de elevadores místicos
[2025.08.26-02.52.12:686][388]LogTemp: Sistema de elevadores místicos criado com sucesso
[2025.08.26-02.52.12:686][388]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"status": "success",
		"message": "Sistema de elevadores místicos criado com sucesso",
		"data":
		{
			"system_config":
			{
				"system_name": "mystical_elevators_system",
				"total_elevators": 4,
				"travel_time_seconds": 5,
				"max_capacity": 5,
				"activation_time_seconds": 3,
				"require_teamwork": true,
				"elevators":
				{
					"elevator_north":
					{
						"name": "Elevador Místico Norte",
						"location": "Região Norte do Mapa",
						"connection_type": "Planície ↔ Firmamento",
						"capacity": 5,
						"travel_time": 5,
						"vulnerable_during_travel": true,
						"strategic_uses": [
							"Team Rotations",
							"Objective Rushes"
						]
					},
					"elevator_south":
					{
						"name": "Elevador Místico Sul",
						"location": "Região Sul do Mapa",
						"connection_type": "Planície ↔ Abismo",
						"capacity": 5,
						"travel_time": 5,
						"vulnerable_during_travel": true,
						"strategic_uses": [
							"Defensive Retreats",
							"Surprise Engages"
						]
					},
					"elevator_east":
					{
						"name": "Elevador Místico Leste",
						"location": "Região Leste do Mapa",
						"connection_type": "Firmamento ↔ Abismo",
						"capacity": 5,
						"travel_time": 5,
						"vulnerable_during_travel": true,
						"strategic_uses": [
							"Cross-Layer Flanks",
							"Multi-Layer Pressure"
						]
					},
					"elevator_west":
					{
						"name": "Elevador Místico Oeste",
						"location": "Região Oeste do Mapa",
						"connection_type": "Tri-Layer Connection",
						"capacity": 5,
						"travel_time": 7.5,
						"vulnerable_during_travel": true,
						"special_elevator": true,
						"strategic_uses": [
							"Ultimate Team Rotations",
							"Emergency Evacuations"
						]
					}
				},
				"elevator_mechanics":
				{
					"activation_time": 3,
					"travel_time": 5,
					"cooldown_after_use": 60,
					"team_priority_system": true,
					"progress_bar_visible": true,
					"interruptible_by_damage": true
				}
			},
			"system_state":
			{
				"status": "created",
				"creation_time": "2025.08.25-23.52.12",
				"active": true,
				"total_elevators": 4
			},
			"system_description": "Sistema de elevadores místicos para movimentação coordenada de equipe"
		},
		"timestamp": "2025.08.25-23.52.12"
	}
}
[2025.08.26-02.52.12:687][388]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 2426
[2025.08.26-02.52.12:687][388]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-02.52.17:921][404]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.52.17:921][404]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.52.17:921][404]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-02.52.18:022][404]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.52.18:022][404]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.52.18:022][404]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_dimensional_bridges_system", "params": {"system_name": "dimensional_bridges_system", "bridge_duration": 30, "activation_cost": 100, "cooldown_time": 120, "enable_team_requirement": true, "bridge_types": {"energy_bridge": {"name": "Ponte de Energia", "connects": ["Plan\u00edcie Radiante", "Firmamento Zephyr"], "duration": 30.0, "width": "2 champions lado a lado", "special_effect": "Velocidade aumentada em 25%"}, "shadow_bridge": {"name": "Ponte Sombria", "connects": ["Firmamento Zephyr", "Abismo Umbral"], "duration": 25.0, "width": "1 champion por vez", "special_effect": "Invisibilidade durante travessia"}, "crystal_bridge": {"name": "Ponte Cristalina", "connects": ["Plan\u00edcie Radiante", "Abismo Umbral"], "duration": 20.0, "width": "3 champions lado a lado", "special_effect": "Escudo m\u00e1gico durante travessia", "activation_requirement": "2+ champions pr\u00f3ximos"}}, "bridge_mechanics": ["temporary_connections_strategic_timing", "team_coordination_required", "high_risk_high_reward", "limited_duration_creates_urgency", "multiple_activation_points"]}}
[2025.08.26-02.52.18:022][404]LogTemp: Display: UnrealMCPBridge: Executing command: create_dimensional_bridges_system
[2025.08.26-02.52.18:022][404]LogTemp: Criando sistema de pontes dimensionais
[2025.08.26-02.52.18:022][404]LogTemp: Sistema de pontes dimensionais criado com sucesso
[2025.08.26-02.52.18:022][404]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"status": "success",
		"message": "Sistema de pontes dimensionais criado com sucesso",
		"data":
		{
			"system_config":
			{
				"system_name": "dimensional_bridges_system",
				"enable_objective_bridges": true,
				"enable_temporary_bridges": true,
				"bridge_duration_minutes": 2,
				"temporary_bridge_duration_seconds": 90,
				"objective_bridges":
				{
					"guardian_bridge":
					{
						"name": "Ponte do Guardião da Aurora",
						"activation_objective": "Guardião da Aurora",
						"connection": "Planície ↔ Firmamento",
						"duration_minutes": 2,
						"bidirectional": true,
						"bridge_type": "light_energy"
					},
					"wind_lord_bridge":
					{
						"name": "Ponte do Senhor dos Ventos",
						"activation_objective": "Senhor dos Ventos",
						"connection": "Firmamento ↔ Abismo",
						"duration_minutes": 2,
						"bidirectional": true,
						"bridge_type": "wind_energy"
					},
					"arch_shadow_bridge":
					{
						"name": "Ponte do Arqui-Sombra",
						"activation_objective": "Arqui-Sombra",
						"connection": "Abismo ↔ Planície",
						"duration_minutes": 2,
						"bidirectional": true,
						"bridge_type": "shadow_energy"
					}
				},
				"temporary_bridges":
				{
					"resource_bridges":
					{
						"name": "Pontes de Recursos",
						"duration_seconds": 90,
						"location_specific": true,
						"activation_resources": [
							"Cristais de Luz",
							"Esferas de Vento",
							"Fragmentos de Sombra"
						]
					}
				},
				"bridge_mechanics":
				{
					"activation_delay": 5,
					"visible_to_all_players": true,
					"destructible": false,
					"provides_vision": true,
					"movement_speed_bonus": 0.10000000149011612,
					"tactical_advantages": [
						"Direct Layer Access",
						"Bypass Portal Cooldowns",
						"Team Coordination",
						"Strategic Positioning"
					]
				}
			},
			"system_state":
			{
				"status": "created",
				"creation_time": "2025.08.25-23.52.18",
				"active": true,
				"objective_bridges_count": 3
			},
			"system_description": "Sistema de pontes dimensionais ativadas por objetivos"
		},
		"timestamp": "2025.08.25-23.52.18"
	}
}
[2025.08.26-02.52.18:022][404]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 2261
[2025.08.26-02.52.18:022][404]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-02.52.22:381][458]LogSlate: Took 0.010150 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/DroidSansFallback.ttf' (3848K)
[2025.08.26-02.52.22:449][463]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.52.22:449][463]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.52.22:449][463]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-02.52.22:551][468]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.52.22:551][468]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.52.22:551][468]LogTemp: Display: MCPServerRunnable: Received: {"type": "setup_vertical_navigation_strategies", "params": {"system_name": "vertical_navigation_strategies", "strategies": {"early_game": {"name": "Estrat\u00e9gias Early Game", "enabled": true, "focus": "Explora\u00e7\u00e3o e estabelecimento de controle", "recommended_methods": ["Portais prim\u00e1rios", "Elevadores seguros"], "avoid_methods": ["Pontes dimensionais"], "key_objectives": ["Estabelecer vis\u00e3o em m\u00faltiplas camadas", "Controlar pontos de transi\u00e7\u00e3o", "Evitar confrontos em territ\u00f3rio desconhecido"]}, "mid_game": {"name": "Estrat\u00e9gias Mid Game", "enabled": true, "focus": "Controle de objetivos e rota\u00e7\u00f5es", "recommended_methods": ["Todos os m\u00e9todos dispon\u00edveis"], "key_objectives": ["Rota\u00e7\u00f5es r\u00e1pidas entre objetivos", "Flanqueamento atrav\u00e9s de camadas", "Controle de pontos estrat\u00e9gicos"]}, "late_game": {"name": "Estrat\u00e9gias Late Game", "enabled": true, "focus": "Execu\u00e7\u00e3o de teamfights e finaliza\u00e7\u00e3o", "recommended_methods": ["Pontes dimensionais", "Elevadores coordenados"], "key_objectives": ["Engajamentos coordenados", "Escape routes planejadas", "Controle total do mapa vertical"]}}, "enable_early_game": true, "enable_mid_game": true, "enable_late_game": true}}
[2025.08.26-02.52.22:551][468]LogTemp: Display: UnrealMCPBridge: Executing command: setup_vertical_navigation_strategies
[2025.08.26-02.52.22:552][468]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "error",
	"error": "Unknown command: setup_vertical_navigation_strategies"
}
[2025.08.26-02.52.22:552][468]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 93
[2025.08.26-02.52.22:552][468]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-02.52.30:298][646]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.52.30:298][646]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.52.30:298][646]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-02.52.30:401][646]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.52.30:401][646]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.52.30:401][646]LogTemp: Display: MCPServerRunnable: Received: {"type": "setup_navigation_strategies", "params": {"enable_early_game": true, "enable_mid_game": true, "enable_late_game": true}}
[2025.08.26-02.52.30:401][646]LogTemp: Display: UnrealMCPBridge: Executing command: setup_navigation_strategies
[2025.08.26-02.52.30:467][646]LogTemp: Configurando estratégias de navegação
[2025.08.26-02.52.30:468][646]LogTemp: Estratégias de navegação configuradas com sucesso
[2025.08.26-02.52.30:468][646]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"status": "success",
		"message": "Estratégias de navegação configuradas com sucesso",
		"data":
		{
			"system_config":
			{
				"system_name": "navigation_strategies",
				"strategies":
				{
					"early_game":
					{
						"phase_name": "Early Game",
						"start_time": 0,
						"end_time": 15,
						"focus": "Estabelecer controle na Planície Radiante",
						"strategies": [
							"Transições limitadas, apenas para objetivos específicos",
							"Prioridade: Portais próximos às lanes para ganks",
							"Evitar elevadores (muito arriscado)",
							"Usar portais para escape emergencial"
						],
						"priority_locations": [
							"Portal próximo à Top Lane",
							"Portal próximo à Bot Lane",
							"Portal central para Mid Lane"
						]
					},
					"mid_game":
					{
						"phase_name": "Mid Game",
						"start_time": 15,
						"end_time": 30,
						"focus": "Controle de múltiplas camadas",
						"strategies": [
							"Transições frequentes para controle de objetivos",
							"Prioridade: Elevadores para movimentação de equipe",
							"Usar pontes dimensionais quando disponíveis",
							"Coordenar rotações entre camadas"
						],
						"priority_locations": [
							"Elevador Norte para teamfights",
							"Elevador Sul para objetivos épicos",
							"Portais para flanks rápidos"
						]
					},
					"late_game":
					{
						"phase_name": "Late Game",
						"start_time": 30,
						"end_time": "game_end",
						"focus": "Controle total do mapa vertical",
						"strategies": [
							"Transições constantes para pressão máxima",
							"Usar todos os sistemas de navegação",
							"Coordenação perfeita entre camadas",
							"Controle de todas as pontes dimensionais"
						],
						"priority_locations": [
							"Elevador Oeste para rotações ultimates",
							"Todas as pontes dimensionais ativas",
							"Controle total dos portais"
						]
					}
				},
				"strategic_rotation":
				{
					"system_name": "strategic_rotation",
					"rotation_principles": [
						"Sempre manter controle de pelo menos uma camada",
						"Usar navegação vertical para flanks inesperados",
						"Coordenar transições com objetivos neutros",
						"Priorizar segurança da equipe durante transições"
					]
				}
			},
			"system_description": "Estratégias de navegação vertical por fase da partida"
		},
		"timestamp": "2025.08.25-23.52.30"
	}
}
[2025.08.26-02.52.30:469][646]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 2538
[2025.08.26-02.52.30:469][646]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-02.52.35:903][663]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.52.35:903][663]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.52.35:903][663]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-02.52.36:004][663]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.52.36:004][663]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.52.36:004][663]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_primary_portals_system", "params": {}}
[2025.08.26-02.52.36:004][663]LogTemp: Display: UnrealMCPBridge: Executing command: create_primary_portals_system
[2025.08.26-02.52.36:134][663]LogTemp: Criando sistema de portais primários
[2025.08.26-02.52.36:134][663]LogTemp: Sistema de portais primários criado com sucesso
[2025.08.26-02.52.36:134][663]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"status": "success",
		"message": "Sistema de portais primários criado com sucesso",
		"data":
		{
			"system_config":
			{
				"system_name": "primary_portals_system",
				"portals_per_layer": 4,
				"cooldown_seconds": 10,
				"travel_time_seconds": 2,
				"enable_all_layers": true,
				"layer_portals":
				{
					"planicie_radiante":
					{
						"layer_name": "planicie_radiante",
						"portal_count": 4,
						"portal_type": "terrestrial",
						"connections": [
							"firmamento_zephyr",
							"abismo_umbral"
						],
						"locations": [
							"Próximo à Top Lane",
							"Próximo à Mid Lane",
							"Próximo à Bot Lane",
							"Centro da Jungle"
						]
					},
					"firmamento_zephyr":
					{
						"layer_name": "firmamento_zephyr",
						"portal_count": 4,
						"portal_type": "aerial",
						"connections": [
							"planicie_radiante",
							"abismo_umbral"
						],
						"locations": [
							"Corrente de Vento Norte",
							"Corrente de Vento Sul",
							"Zona Central",
							"Plataforma Móvel"
						]
					},
					"abismo_umbral":
					{
						"layer_name": "abismo_umbral",
						"portal_count": 4,
						"portal_type": "underground",
						"connections": [
							"planicie_radiante",
							"firmamento_zephyr"
						],
						"locations": [
							"Corredor Principal",
							"Câmara Secreta",
							"Zona de Sombras",
							"Túnel Labiríntico"
						]
					}
				},
				"portal_mechanics":
				{
					"activation_time": 1,
					"travel_time": 2,
					"cooldown_time": 10,
					"interruptible": true,
					"visible_to_enemies": true,
					"strategic_uses": [
						"Escape Routes",
						"Flanking",
						"Objective Control",
						"Split Push"
					]
				}
			},
			"system_state":
			{
				"status": "created",
				"creation_time": "2025.08.25-23.52.36",
				"active": true,
				"total_portals": 12
			},
			"system_description": "Sistema de portais primários para navegação rápida entre camadas"
		},
		"timestamp": "2025.08.25-23.52.36"
	}
}
[2025.08.26-02.52.36:134][663]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 2137
[2025.08.26-02.52.36:134][663]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-02.52.36:235][664]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.52.36:235][664]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.52.36:235][664]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_mystic_elevators_system", "params": {}}
[2025.08.26-02.52.36:235][664]LogTemp: Display: UnrealMCPBridge: Executing command: create_mystic_elevators_system
[2025.08.26-02.52.36:469][664]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "error",
	"error": "Unknown command: create_mystic_elevators_system"
}
[2025.08.26-02.52.36:469][664]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 87
[2025.08.26-02.52.36:469][664]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-02.52.36:570][665]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.52.36:570][665]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.52.36:570][665]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_dimensional_bridges_system", "params": {}}
[2025.08.26-02.52.36:570][665]LogTemp: Display: UnrealMCPBridge: Executing command: create_dimensional_bridges_system
[2025.08.26-02.52.36:802][665]LogTemp: Criando sistema de pontes dimensionais
[2025.08.26-02.52.36:802][665]LogTemp: Sistema de pontes dimensionais criado com sucesso
[2025.08.26-02.52.36:802][665]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"status": "success",
		"message": "Sistema de pontes dimensionais criado com sucesso",
		"data":
		{
			"system_config":
			{
				"system_name": "dimensional_bridges_system",
				"enable_objective_bridges": true,
				"enable_temporary_bridges": true,
				"bridge_duration_minutes": 2,
				"temporary_bridge_duration_seconds": 90,
				"objective_bridges":
				{
					"guardian_bridge":
					{
						"name": "Ponte do Guardião da Aurora",
						"activation_objective": "Guardião da Aurora",
						"connection": "Planície ↔ Firmamento",
						"duration_minutes": 2,
						"bidirectional": true,
						"bridge_type": "light_energy"
					},
					"wind_lord_bridge":
					{
						"name": "Ponte do Senhor dos Ventos",
						"activation_objective": "Senhor dos Ventos",
						"connection": "Firmamento ↔ Abismo",
						"duration_minutes": 2,
						"bidirectional": true,
						"bridge_type": "wind_energy"
					},
					"arch_shadow_bridge":
					{
						"name": "Ponte do Arqui-Sombra",
						"activation_objective": "Arqui-Sombra",
						"connection": "Abismo ↔ Planície",
						"duration_minutes": 2,
						"bidirectional": true,
						"bridge_type": "shadow_energy"
					}
				},
				"temporary_bridges":
				{
					"resource_bridges":
					{
						"name": "Pontes de Recursos",
						"duration_seconds": 90,
						"location_specific": true,
						"activation_resources": [
							"Cristais de Luz",
							"Esferas de Vento",
							"Fragmentos de Sombra"
						]
					}
				},
				"bridge_mechanics":
				{
					"activation_delay": 5,
					"visible_to_all_players": true,
					"destructible": false,
					"provides_vision": true,
					"movement_speed_bonus": 0.10000000149011612,
					"tactical_advantages": [
						"Direct Layer Access",
						"Bypass Portal Cooldowns",
						"Team Coordination",
						"Strategic Positioning"
					]
				}
			},
			"system_state":
			{
				"status": "created",
				"creation_time": "2025.08.25-23.52.36",
				"active": true,
				"objective_bridges_count": 3
			},
			"system_description": "Sistema de pontes dimensionais ativadas por objetivos"
		},
		"timestamp": "2025.08.25-23.52.36"
	}
}
[2025.08.26-02.52.36:802][665]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 2261
[2025.08.26-02.52.36:802][665]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-02.52.36:904][666]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.52.36:904][666]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.52.36:904][666]LogTemp: Display: MCPServerRunnable: Received: {"type": "setup_vertical_navigation_strategies", "params": {}}
[2025.08.26-02.52.36:904][666]LogTemp: Display: UnrealMCPBridge: Executing command: setup_vertical_navigation_strategies
[2025.08.26-02.52.37:136][666]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "error",
	"error": "Unknown command: setup_vertical_navigation_strategies"
}
[2025.08.26-02.52.37:136][666]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 93
[2025.08.26-02.52.37:136][666]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-02.52.51:524][855]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.52.51:524][855]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.52.51:524][855]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-02.52.51:625][855]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.52.51:625][855]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.52.51:625][855]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_planicie_radiante_jungle", "params": {"jungle_name": "planicie_radiante_jungle", "layer_name": "planicie_radiante", "jungle_type": "terrestrial", "camps_count": 24, "monsters": {"red_buff": {"name": "Guardi\u00e3o Carmesim", "enabled": true, "health": 1800, "health_per_minute": 180, "damage": 78, "damage_per_minute": 6, "buff_effects": ["+20% Slow em ataques", "+5 HP/s regenera\u00e7\u00e3o"], "buff_duration": 90, "respawn_time": 300}, "blue_buff": {"name": "Sentinela Azul", "enabled": true, "health": 1700, "health_per_minute": 170, "damage": 72, "damage_per_minute": 5, "buff_effects": ["+10% CDR", "+5 MP/s regenera\u00e7\u00e3o"], "buff_duration": 90, "respawn_time": 300}, "wolves": {"name": "Matilha Sombria", "enabled": true, "large_wolf_health": 1200, "large_wolf_health_per_minute": 120, "small_wolves_health": 400, "small_wolves_health_per_minute": 40, "small_wolves_count": 2, "total_gold": 95, "respawn_time": 120}, "gromp": {"name": "Sapo Gigante", "enabled": true, "health": 1500, "health_per_minute": 150, "damage": 84, "damage_per_minute": 7, "passive": "Poison que causa dano ao longo do tempo", "gold": 105, "respawn_time": 120}, "krugs": {"name": "Golems de Pedra", "enabled": true, "large_krug_health": 1000, "large_krug_health_per_minute": 100, "medium_krug_health": 600, "medium_krug_health_per_minute": 60, "small_krugs_health": 200, "small_krugs_health_per_minute": 20, "small_krugs_count": 4, "total_gold": 84, "respawn_time": 135}, "raptors": {"name": "Aves Predadoras", "enabled": true, "large_raptor_health": 800, "large_raptor_health_per_minute": 80, "small_raptors_health": 250, "small_raptors_health_per_minute": 25, "small_raptors_count": 5, "total_gold": 100, "respawn_time": 120}}, "enable_red_buff": true, "enable_blue_buff": true, "enable_wolves": true, "enable_gromp": true, "enable_krugs": true, "enable_raptors": true}}
[2025.08.26-02.52.51:625][855]LogTemp: Display: UnrealMCPBridge: Executing command: create_planicie_radiante_jungle
[2025.08.26-02.52.51:759][855]LogTemp: Criando jungle da Planície Radiante
[2025.08.26-02.52.51:759][855]LogTemp: Jungle da Planície Radiante criado com sucesso
[2025.08.26-02.52.51:759][855]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"status": "success",
		"message": "Jungle da Planície Radiante criado com sucesso",
		"data":
		{
			"jungle_config":
			{
				"jungle_name": "planicie_radiante_jungle",
				"jungle_type": "terrestrial",
				"camps_count": 24,
				"monsters":
				{
					"red_buff":
					{
						"name": "Guardião Carmesim",
						"enabled": true,
						"health": 1800,
						"health_per_minute": 180,
						"damage": 78,
						"damage_per_minute": 6,
						"buff_duration": 90,
						"respawn_time": 300,
						"buff_effects": [
							"+20% Slow em ataques",
							"+5 HP/s regeneração"
						]
					},
					"blue_buff":
					{
						"name": "Sentinela Azul",
						"enabled": true,
						"health": 1700,
						"health_per_minute": 170,
						"damage": 72,
						"damage_per_minute": 5,
						"buff_duration": 90,
						"respawn_time": 300,
						"buff_effects": [
							"+10% CDR",
							"+5 MP/s regeneração"
						]
					},
					"wolves":
					{
						"name": "Matilha Sombria",
						"enabled": true,
						"large_wolf_health": 1200,
						"large_wolf_health_per_minute": 120,
						"small_wolves_health": 400,
						"small_wolves_health_per_minute": 40,
						"small_wolves_count": 2,
						"total_gold": 95,
						"respawn_time": 120
					},
					"gromp":
					{
						"name": "Sapo Gigante",
						"enabled": true,
						"health": 1500,
						"health_per_minute": 150,
						"damage": 84,
						"damage_per_minute": 7,
						"passive": "Poison que causa dano ao longo do tempo",
						"gold": 105,
						"respawn_time": 120
					},
					"krugs":
					{
						"name": "Golems de Pedra",
						"enabled": true,
						"large_krug_health": 1000,
						"large_krug_health_per_minute": 100,
						"medium_krug_health": 600,
						"medium_krug_health_per_minute": 60,
						"small_krugs_health": 200,
						"small_krugs_health_per_minute": 20,
						"small_krugs_count": 4,
						"total_gold": 84,
						"respawn_time": 135
					},
					"raptors":
					{
						"name": "Aves Predadoras",
						"enabled": true,
						"large_raptor_health": 800,
						"large_raptor_health_per_minute": 80,
						"small_raptors_health": 250,
						"small_raptors_health_per_minute": 25,
						"small_raptors_count": 5,
						"total_gold": 100,
						"respawn_time": 120
					}
				}
			},
			"jungle_state":
			{
				"status": "created",
				"creation_time": "2025.08.25-23.52.51",
				"active": true
			},
			"jungle_description": "Jungle terrestre com monstros tradicionais e buffs"
		},
		"timestamp": "2025.08.25-23.52.51"
	}
}
[2025.08.26-02.52.51:760][855]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 2655
[2025.08.26-02.52.51:760][855]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-02.52.56:692][870]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.52.56:692][870]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.52.56:692][870]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-02.52.56:793][871]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.52.56:793][871]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.52.56:793][871]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_firmamento_zephyr_jungle", "params": {"jungle_name": "firmamento_zephyr_jungle", "layer_name": "firmamento_zephyr", "jungle_type": "aerial", "monsters": {"storm_elemental": {"name": "Elemental da Tempestade", "enabled": true, "health": 2200, "health_per_minute": 200, "abilities": ["Raios que saltam entre alvos"], "buff_effects": ["+15% Velocidade de Movimento", "imunidade a slows"], "buff_duration": 120, "respawn_time": 360}, "wind_spirits": {"name": "Esp\u00edritos do Vento", "enabled": true, "large_spirit_health": 900, "large_spirit_health_per_minute": 90, "small_spirits_health": 300, "small_spirits_health_per_minute": 30, "small_spirits_count": 2, "passive": "Empurram inimigos ao morrer", "total_gold": 110, "respawn_time": 150}, "cloud_drakes": {"name": "Drag\u00f5es das Nuvens", "enabled": true, "alpha_drake_health": 1100, "alpha_drake_health_per_minute": 110, "beta_drakes_health": 400, "beta_drakes_health_per_minute": 40, "beta_drakes_count": 2, "ability": "Voo curto que evita ataques terrestres", "total_gold": 95, "respawn_time": 135}}, "enable_storm_elemental": true, "enable_wind_spirits": true, "enable_cloud_drakes": true}}
[2025.08.26-02.52.56:793][871]LogTemp: Display: UnrealMCPBridge: Executing command: create_firmamento_zephyr_jungle
[2025.08.26-02.52.57:093][871]LogTemp: Criando jungle do Firmamento Zephyr
[2025.08.26-02.52.57:093][871]LogTemp: Jungle do Firmamento Zephyr criado com sucesso
[2025.08.26-02.52.57:093][871]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"status": "success",
		"message": "Jungle do Firmamento Zephyr criado com sucesso",
		"data":
		{
			"jungle_config":
			{
				"jungle_name": "firmamento_zephyr_jungle",
				"jungle_type": "aerial",
				"monsters":
				{
					"storm_elemental":
					{
						"name": "Elemental da Tempestade",
						"enabled": true,
						"health": 2200,
						"health_per_minute": 200,
						"buff_duration": 120,
						"respawn_time": 360,
						"abilities": [
							"Raios que saltam entre alvos"
						],
						"buff_effects": [
							"+15% Velocidade de Movimento",
							"imunidade a slows"
						]
					},
					"wind_spirits":
					{
						"name": "Espíritos do Vento",
						"enabled": true,
						"large_spirit_health": 900,
						"large_spirit_health_per_minute": 90,
						"small_spirits_health": 300,
						"small_spirits_health_per_minute": 30,
						"small_spirits_count": 2,
						"passive": "Empurram inimigos ao morrer",
						"total_gold": 110,
						"respawn_time": 150
					},
					"cloud_drakes":
					{
						"name": "Dragões das Nuvens",
						"enabled": true,
						"alpha_drake_health": 1100,
						"alpha_drake_health_per_minute": 110,
						"beta_drakes_health": 400,
						"beta_drakes_health_per_minute": 40,
						"beta_drakes_count": 2,
						"ability": "Voo curto que evita ataques terrestres",
						"total_gold": 95,
						"respawn_time": 135
					}
				}
			},
			"jungle_state":
			{
				"status": "created",
				"creation_time": "2025.08.25-23.52.57",
				"active": true
			},
			"jungle_description": "Jungle aéreo com elementais e criaturas voadoras"
		},
		"timestamp": "2025.08.25-23.52.57"
	}
}
[2025.08.26-02.52.57:093][871]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 1734
[2025.08.26-02.52.57:093][871]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-02.53.01:521][885]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.53.01:521][885]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.53.01:521][885]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-02.53.01:623][885]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.53.01:623][885]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.53.01:623][885]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_abismo_umbral_jungle", "params": {"jungle_name": "abismo_umbral_jungle", "layer_name": "abismo_umbral", "jungle_type": "underground", "monsters": {"shadow_wraith": {"name": "Espectro Sombrio", "enabled": true, "health": 2000, "health_per_minute": 180, "abilities": ["Invisibilidade tempor\u00e1ria"], "buff_effects": ["+20% Lethality", "+10% Spell Vamp"], "buff_duration": 100, "respawn_time": 330}, "void_spiders": {"name": "Aranhas do Vazio", "enabled": true, "queen_spider_health": 1000, "queen_spider_health_per_minute": 100, "spiderlings_health": 200, "spiderlings_health_per_minute": 20, "spiderlings_count": 4, "passive": "Spawnam filhotes ao morrer", "total_gold": 105, "respawn_time": 165}, "bone_collectors": {"name": "Coletores de Ossos", "enabled": true, "large_collector_health": 850, "large_collector_health_per_minute": 85, "small_collectors_health": 350, "small_collectors_health_per_minute": 35, "small_collectors_count": 2, "ability": "Ressuscitam uma vez com 50% da vida", "total_gold": 90, "respawn_time": 150}}, "enable_shadow_wraith": true, "enable_void_spiders": true, "enable_bone_collectors": true}}
[2025.08.26-02.53.01:623][885]LogTemp: Display: UnrealMCPBridge: Executing command: create_abismo_umbral_jungle
[2025.08.26-02.53.01:760][885]LogTemp: Criando jungle do Abismo Umbral
[2025.08.26-02.53.01:760][885]LogTemp: Jungle do Abismo Umbral criado com sucesso
[2025.08.26-02.53.01:760][885]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"status": "success",
		"message": "Jungle do Abismo Umbral criado com sucesso",
		"data":
		{
			"jungle_config":
			{
				"jungle_name": "abismo_umbral_jungle",
				"jungle_type": "underground",
				"monsters":
				{
					"shadow_wraith":
					{
						"name": "Espectro Sombrio",
						"enabled": true,
						"health": 2000,
						"health_per_minute": 180,
						"buff_duration": 100,
						"respawn_time": 330,
						"abilities": [
							"Invisibilidade temporária"
						],
						"buff_effects": [
							"+20% Lethality",
							"+10% Spell Vamp"
						]
					},
					"void_spiders":
					{
						"name": "Aranhas do Vazio",
						"enabled": true,
						"queen_spider_health": 1000,
						"queen_spider_health_per_minute": 100,
						"spiderlings_health": 200,
						"spiderlings_health_per_minute": 20,
						"spiderlings_count": 4,
						"passive": "Spawnam filhotes ao morrer",
						"total_gold": 105,
						"respawn_time": 165
					},
					"bone_collectors":
					{
						"name": "Coletores de Ossos",
						"enabled": true,
						"large_collector_health": 850,
						"large_collector_health_per_minute": 85,
						"small_collectors_health": 350,
						"small_collectors_health_per_minute": 35,
						"small_collectors_count": 2,
						"ability": "Ressuscitam uma vez com 50% da vida",
						"total_gold": 90,
						"respawn_time": 150
					}
				}
			},
			"jungle_state":
			{
				"status": "created",
				"creation_time": "2025.08.25-23.53.01",
				"active": true
			},
			"jungle_description": "Jungle subterrâneo com criaturas sombrias e espectrais"
		},
		"timestamp": "2025.08.25-23.53.01"
	}
}
[2025.08.26-02.53.01:761][885]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 1722
[2025.08.26-02.53.01:761][885]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-02.53.06:792][901]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.53.06:792][901]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.53.06:792][901]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-02.53.06:893][901]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.53.06:893][901]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.53.06:893][901]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_scuttle_crabs_system", "params": {"system_name": "scuttle_crabs_system", "scuttle_crabs": {"terrestrial": {"name": "Scuttle Crab Terrestre", "enabled": true, "location": "Rio da Plan\u00edcie Radiante", "health": 1000, "health_per_minute": 35, "buff_effects": ["Vis\u00e3o", "velocidade em rio"], "gold_min": 70, "gold_max": 140, "respawn_time": 150}, "ethereal": {"name": "Scuttle Crab Et\u00e9rea", "enabled": true, "location": "Correntes de ar do Firmamento", "health": 1200, "health_per_minute": 40, "buff_effects": ["Vis\u00e3o", "velocidade de movimento a\u00e9reo"], "gold_min": 80, "gold_max": 150, "respawn_time": 180}, "shadow": {"name": "Scuttle Crab Sombria", "enabled": true, "location": "T\u00faneis do Abismo", "health": 900, "health_per_minute": 30, "buff_effects": ["Vis\u00e3o", "detec\u00e7\u00e3o de invisibilidade"], "gold_min": 75, "gold_max": 145, "respawn_time": 165}}, "enable_terrestrial": true, "enable_ethereal": true, "enable_shadow": true}}
[2025.08.26-02.53.06:893][901]LogTemp: Display: UnrealMCPBridge: Executing command: create_scuttle_crabs_system
[2025.08.26-02.53.07:095][901]LogTemp: Criando sistema de Scuttle Crabs
[2025.08.26-02.53.07:095][901]LogTemp: Sistema de Scuttle Crabs criado com sucesso
[2025.08.26-02.53.07:095][901]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"status": "success",
		"message": "Sistema de Scuttle Crabs criado com sucesso",
		"data":
		{
			"system_config":
			{
				"system_name": "scuttle_crabs_system",
				"scuttle_crabs":
				{
					"terrestrial":
					{
						"name": "Scuttle Crab Terrestre",
						"enabled": true,
						"location": "Rio da Planície Radiante",
						"health": 1000,
						"health_per_minute": 35,
						"gold_min": 70,
						"gold_max": 140,
						"respawn_time": 150,
						"buff_effects": [
							"Visão",
							"velocidade em rio"
						]
					},
					"ethereal":
					{
						"name": "Scuttle Crab Etérea",
						"enabled": true,
						"location": "Correntes de ar do Firmamento",
						"health": 1200,
						"health_per_minute": 40,
						"gold_min": 80,
						"gold_max": 150,
						"respawn_time": 180,
						"buff_effects": [
							"Visão",
							"velocidade de movimento aéreo"
						]
					},
					"shadow":
					{
						"name": "Scuttle Crab Sombria",
						"enabled": true,
						"location": "Túneis do Abismo",
						"health": 900,
						"health_per_minute": 30,
						"gold_min": 75,
						"gold_max": 145,
						"respawn_time": 165,
						"buff_effects": [
							"Visão",
							"detecção de invisibilidade"
						]
					}
				}
			},
			"system_description": "Sistema de Scuttle Crabs especiais para todas as camadas"
		},
		"timestamp": "2025.08.25-23.53.07"
	}
}
[2025.08.26-02.53.07:095][901]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 1481
[2025.08.26-02.53.07:095][901]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-02.53.12:331][917]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.53.12:331][917]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.53.12:331][917]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-02.53.12:432][917]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.53.12:432][917]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.53.12:432][917]LogTemp: Display: MCPServerRunnable: Received: {"type": "setup_jungle_clear_routes", "params": {"system_name": "jungle_clear_routes", "routes": {"red_start": {"name": "Clear Iniciando no Red", "enabled": true, "sequence": ["Red Buff", "Krugs", "Raptors", "Wolves", "Blue Buff", "Gromp"], "total_time_minutes": 3.5, "level_reached": 4, "health_remaining_percent": 60}, "blue_start": {"name": "Clear Iniciando no Blue", "enabled": true, "sequence": ["Blue Buff", "Gromp", "Wolves", "Raptors", "Red Buff", "Krugs"], "total_time_minutes": 3.75, "level_reached": 4, "health_remaining_percent": 55}, "vertical_clear": {"name": "Clear Vertical (Cross-Realm)", "enabled": true, "sequence": ["Red Terrestre", "Storm Elemental", "Shadow Wraith", "Blue Terrestre"], "total_time_minutes": 4.25, "level_reached": 4, "unique_buffs": 3}}, "enable_red_start": true, "enable_blue_start": true, "enable_vertical_clear": true}}
[2025.08.26-02.53.12:432][917]LogTemp: Display: UnrealMCPBridge: Executing command: setup_jungle_clear_routes
[2025.08.26-02.53.12:433][917]LogTemp: Configurando rotas de clear da jungle
[2025.08.26-02.53.12:433][917]LogTemp: Rotas de clear da jungle configuradas com sucesso
[2025.08.26-02.53.12:433][917]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"status": "success",
		"message": "Rotas de clear da jungle configuradas com sucesso",
		"data":
		{
			"system_config":
			{
				"system_name": "jungle_clear_routes",
				"routes":
				{
					"red_start":
					{
						"name": "Clear Iniciando no Red",
						"enabled": true,
						"total_time_minutes": 3.5,
						"level_reached": 4,
						"health_remaining_percent": 60,
						"sequence": [
							"Red Buff",
							"Krugs",
							"Raptors",
							"Wolves",
							"Blue Buff",
							"Gromp"
						]
					},
					"blue_start":
					{
						"name": "Clear Iniciando no Blue",
						"enabled": true,
						"total_time_minutes": 3.75,
						"level_reached": 4,
						"health_remaining_percent": 55,
						"sequence": [
							"Blue Buff",
							"Gromp",
							"Wolves",
							"Raptors",
							"Red Buff",
							"Krugs"
						]
					},
					"vertical_clear":
					{
						"name": "Clear Vertical (Cross-Realm)",
						"enabled": true,
						"total_time_minutes": 4.25,
						"level_reached": 4,
						"unique_buffs": 3,
						"sequence": [
							"Red Terrestre",
							"Storm Elemental",
							"Shadow Wraith",
							"Blue Terrestre"
						]
					}
				}
			},
			"system_description": "Rotas de clear otimizadas para jungle multicamada"
		},
		"timestamp": "2025.08.25-23.53.12"
	}
}
[2025.08.26-02.53.12:434][918]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 1401
[2025.08.26-02.53.12:434][918]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-02.53.26:329][964]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.53.26:329][964]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.53.26:329][964]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-02.53.26:430][967]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.53.26:430][967]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.53.26:430][967]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_top_lane_mechanics", "params": {"lane_name": "top_lane", "lane_type": "solo_lane", "length_multiplier": 1.2, "isolation_level": 0.8, "teleport_importance": 0.9, "freeze_zone_enabled": true, "gank_paths_count": 3, "tp_flank_positions": 4, "champion_types": ["Tank", "Fighter", "Bruiser"], "nearby_objectives": ["Herald", "Scuttle Superior"], "special_mechanics": ["extended_trades_favored", "1v1_focus", "teleport_crucial", "freeze_zone_near_tower", "multiple_gank_paths", "strategic_tp_flanks"]}}
[2025.08.26-02.53.26:430][967]LogTemp: Display: UnrealMCPBridge: Executing command: create_top_lane_mechanics
[2025.08.26-02.53.26:432][967]LogTemp: Criando mecânicas da Top Lane
[2025.08.26-02.53.26:432][967]LogTemp: Mecânicas da Top Lane criadas com sucesso
[2025.08.26-02.53.26:432][967]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"status": "success",
		"message": "Mecânicas da Top Lane criadas com sucesso",
		"data":
		{
			"lane_config":
			{
				"lane_name": "top_lane",
				"lane_type": "solo_lane",
				"length_multiplier": 1.2000000476837158,
				"isolation_level": 0.80000001192092896,
				"teleport_importance": 0.89999997615814209,
				"freeze_zone_enabled": true,
				"gank_paths_count": 3,
				"tp_flank_positions": 4,
				"champion_types": [
					"Tanks",
					"Bruisers",
					"Split Pushers"
				],
				"nearby_objectives": [
					"Herald",
					"Scuttle Superior"
				],
				"special_mechanics": [
					"extended_trades_favored",
					"1v1_focus",
					"teleport_crucial",
					"freeze_zone_near_tower",
					"multiple_gank_paths",
					"strategic_tp_flanks"
				]
			},
			"lane_state":
			{
				"status": "created",
				"creation_time": "2025.08.25-23.53.26",
				"active": true
			},
			"lane_description": "Lane isolada com foco em 1v1 e teleport crucial"
		},
		"timestamp": "2025.08.25-23.53.26"
	}
}
[2025.08.26-02.53.26:432][967]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 1080
[2025.08.26-02.53.26:432][967]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-02.53.27:288][983]LogUObjectHash: Compacting FUObjectHashTables data took   0.40ms
[2025.08.26-02.53.27:290][983]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/AURACRON" FILE="../../../../../../Game/Auracron/Saved/Autosaves/Game/AURACRON_Auto1.umap" SILENT=true AUTOSAVING=true KEEPDIRTY=false
[2025.08.26-02.53.27:300][983]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/AURACRON_Auto1
[2025.08.26-02.53.27:300][983]LogSavePackage: Moving '../../../../../../Game/Auracron/Saved/AURACRON_Auto10CD41767466ACDF18733BB8BC05E9109.tmp' to '../../../../../../Game/Auracron/Saved/Autosaves/Game/AURACRON_Auto1.umap'
[2025.08.26-02.53.27:302][983]LogFileHelpers: Editor autosave (incl. external actors) for '/Game/AURACRON' took 0.034
[2025.08.26-02.53.27:302][983]LogFileHelpers: Editor autosave (incl. sublevels & external actors) for all levels took 0.034
[2025.08.26-02.53.31:665][ 85]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.53.31:665][ 85]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.53.31:665][ 85]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-02.53.31:767][ 87]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.53.31:767][ 87]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.53.31:767][ 87]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_mid_lane_mechanics", "params": {"lane_name": "mid_lane", "lane_type": "solo_lane", "length_multiplier": 0.8, "centrality_factor": 1, "roam_accessibility": 0.9, "river_control_importance": 0.8, "wave_clear_priority": 0.9, "champion_types": ["Mage", "Assassin", "Control"], "nearby_objectives": ["Ambos Scuttles", "Controle de Rio"], "special_mechanics": ["shortest_lane_fast_trades", "central_map_access", "roam_windows", "river_control", "wave_clear_priority", "both_sides_access"]}}
[2025.08.26-02.53.31:767][ 87]LogTemp: Display: UnrealMCPBridge: Executing command: create_mid_lane_mechanics
[2025.08.26-02.53.31:768][ 87]LogTemp: Criando mecânicas da Mid Lane
[2025.08.26-02.53.31:768][ 87]LogTemp: Mecânicas da Mid Lane criadas com sucesso
[2025.08.26-02.53.31:768][ 87]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"status": "success",
		"message": "Mecânicas da Mid Lane criadas com sucesso",
		"data":
		{
			"lane_config":
			{
				"lane_name": "mid_lane",
				"lane_type": "solo_lane",
				"length_multiplier": 0.80000001192092896,
				"centrality_factor": 1,
				"roam_accessibility": 0.89999997615814209,
				"river_control_importance": 0.80000001192092896,
				"wave_clear_priority": 0.89999997615814209,
				"champion_types": [
					"Mages",
					"Assassinos",
					"Roamers"
				],
				"nearby_objectives": [
					"Ambos Scuttles",
					"Controle de Rio"
				],
				"special_mechanics": [
					"shortest_lane_fast_trades",
					"central_map_access",
					"roam_windows",
					"river_control",
					"wave_clear_priority",
					"both_sides_access"
				]
			},
			"lane_state":
			{
				"status": "created",
				"creation_time": "2025.08.25-23.53.31",
				"active": true
			},
			"lane_description": "Lane central com foco em roaming e controle de rio"
		},
		"timestamp": "2025.08.25-23.53.31"
	}
}
[2025.08.26-02.53.31:768][ 87]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 1077
[2025.08.26-02.53.31:768][ 87]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-02.53.36:015][297]LogUObjectHash: Compacting FUObjectHashTables data took   0.31ms
[2025.08.26-02.53.36:019][297]LogFileHelpers: InternalPromptForCheckoutAndSave started...
[2025.08.26-02.53.36:079][297]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/AURACRON" FILE="../../../../../../Game/Auracron/Content/AURACRON.umap" SILENT=true AUTOSAVING=false KEEPDIRTY=false
[2025.08.26-02.53.36:112][297]LogUObjectHash: Compacting FUObjectHashTables data took   0.23ms
[2025.08.26-02.53.36:121][297]LogSavePackage: Moving output files for package: /Game/AURACRON
[2025.08.26-02.53.36:122][297]LogSavePackage: Moving '../../../../../../Game/Auracron/Saved/AURACRONEB6B1B0D4274F743B6024CBE6230190A.tmp' to '../../../../../../Game/Auracron/Content/AURACRON.umap'
[2025.08.26-02.53.36:129][297]LogFileHelpers: Saving map 'AURACRON' took 0.050
[2025.08.26-02.53.36:148][297]LogFileHelpers: InternalPromptForCheckoutAndSave took 129.409 ms (total: 227.174 ms)
[2025.08.26-02.53.36:210][297]LogContentValidation: Display: Starting to validate 1 assets
[2025.08.26-02.53.36:210][297]LogContentValidation: Enabled validators:
[2025.08.26-02.53.36:210][297]LogContentValidation: 	/Script/DataValidation.EditorValidator_Material
[2025.08.26-02.53.36:210][297]LogContentValidation: 	/Script/DataValidation.DirtyFilesChangelistValidator
[2025.08.26-02.53.36:210][297]LogContentValidation: 	/Script/DataValidation.EditorValidator_Localization
[2025.08.26-02.53.36:210][297]LogContentValidation: 	/Script/DataValidation.PackageFileValidator
[2025.08.26-02.53.36:210][297]LogContentValidation: 	/Script/DataValidation.WorldPartitionChangelistValidator
[2025.08.26-02.53.36:210][297]LogContentValidation: 	/Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
[2025.08.26-02.53.36:210][297]AssetCheck: /Game/AURACRON Validando ativo
[2025.08.26-02.53.36:699][308]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.53.36:699][308]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.53.36:699][308]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-02.53.36:799][310]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.53.36:799][310]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.53.36:799][310]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_bot_lane_mechanics", "params": {"lane_name": "bot_lane", "lane_type": "duo_lane", "length_multiplier": 1, "duo_lane_synergy": 0.9, "dragon_control_importance": 0.8, "all_in_potential": 0.7, "champion_types": ["ADC", "Support", "Marksman"], "nearby_objectives": ["Drag\u00e3o", "Scuttle Inferior"], "special_mechanics": ["standard_length_2v2_focus", "duo_synergy_crucial", "all_in_windows", "dragon_control", "bot_lane_synergy_combos", "epic_objectives_access"]}}
[2025.08.26-02.53.36:799][310]LogTemp: Display: UnrealMCPBridge: Executing command: create_bot_lane_mechanics
[2025.08.26-02.53.36:801][310]LogTemp: Criando mecânicas da Bot Lane
[2025.08.26-02.53.36:801][310]LogTemp: Mecânicas da Bot Lane criadas com sucesso
[2025.08.26-02.53.36:801][310]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"status": "success",
		"message": "Mecânicas da Bot Lane criadas com sucesso",
		"data":
		{
			"lane_config":
			{
				"lane_name": "bot_lane",
				"lane_type": "duo_lane",
				"length_multiplier": 1,
				"duo_lane_synergy": 0.89999997615814209,
				"dragon_control_importance": 0.80000001192092896,
				"all_in_potential": 0.69999998807907104,
				"champion_types": [
					"ADC",
					"Support"
				],
				"nearby_objectives": [
					"Dragão",
					"Scuttle Inferior"
				],
				"special_mechanics": [
					"standard_length_2v2_focus",
					"duo_synergy_crucial",
					"all_in_windows",
					"dragon_control",
					"bot_lane_synergy_combos",
					"epic_objectives_access"
				]
			},
			"lane_state":
			{
				"status": "created",
				"creation_time": "2025.08.25-23.53.36",
				"active": true
			},
			"lane_description": "Lane duo com foco em sinergia e controle de dragão"
		},
		"timestamp": "2025.08.25-23.53.36"
	}
}
[2025.08.26-02.53.36:801][310]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 1011
[2025.08.26-02.53.36:801][310]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-02.53.42:339][439]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.53.42:339][439]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.53.42:339][439]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-02.53.42:441][440]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.53.42:441][440]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.53.42:441][440]LogTemp: Display: MCPServerRunnable: Received: {"type": "setup_lane_phases_system", "params": {"system_name": "lane_phases_system", "early_laning_duration": 10, "mid_laning_duration": 10, "late_laning_start": 20, "enable_phase_transitions": true, "phases": {"early_laning": {"duration_minutes": 10, "objectives": ["establish_cs_advantage", "control_wave_positioning", "avoid_ganks_while_pressuring", "establish_vision_control"], "key_mechanics": ["last_hitting", "trading_stance", "wave_management", "back_timing"]}, "mid_laning": {"duration_minutes": 10, "objectives": ["transition_to_teamfights", "control_neutral_objectives", "roaming_and_map_pressure", "establish_item_advantages"], "key_mechanics": ["roam_timing", "objective_setup", "vision_control", "power_spikes"]}, "late_laning": {"start_minute": 20, "objectives": ["teamfight_positioning", "objective_control", "split_push_pressure", "end_game_execution"], "key_mechanics": ["teamfight_execution", "objective_prioritization", "split_push_management", "end_game_calls"]}}}}
[2025.08.26-02.53.42:441][440]LogTemp: Display: UnrealMCPBridge: Executing command: setup_lane_phases_system
[2025.08.26-02.53.42:442][440]LogTemp: Configurando sistema de fases de lane
[2025.08.26-02.53.42:442][440]LogTemp: Sistema de fases de lane configurado com sucesso
[2025.08.26-02.53.42:442][440]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"status": "success",
		"message": "Sistema de fases de lane configurado com sucesso",
		"data":
		{
			"system_config":
			{
				"system_name": "lane_phases_system",
				"early_laning_duration": 10,
				"mid_laning_duration": 10,
				"late_laning_start": 20,
				"enable_phase_transitions": true,
				"phases":
				{
					"early_laning":
					{
						"duration_minutes": 10,
						"objectives": [
							"establish_cs_advantage",
							"control_wave_positioning",
							"avoid_ganks_while_pressuring",
							"establish_vision_control"
						]
					},
					"mid_laning":
					{
						"duration_minutes": 10,
						"objectives": [
							"transition_to_teamfights",
							"control_neutral_objectives",
							"roaming_and_map_pressure",
							"establish_item_advantages"
						]
					},
					"late_laning":
					{
						"start_minute": 20,
						"objectives": [
							"teamfight_positioning",
							"objective_control",
							"split_push_pressure",
							"end_game_execution"
						]
					}
				}
			},
			"system_description": "Sistema completo de fases de lane configurado"
		},
		"timestamp": "2025.08.25-23.53.42"
	}
}
[2025.08.26-02.53.42:443][440]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 1223
[2025.08.26-02.53.42:443][440]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-02.53.52:711][619]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.53.52:711][619]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.53.52:711][619]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-02.53.52:812][619]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.53.52:812][619]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.53.52:812][619]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_ward_system", "params": {"system_name": "comprehensive_ward_system", "ward_types": {"stealth_ward": {"name": "Stealth Ward", "enabled": true, "health": 3, "duration": 150, "vision_range": 900, "cost": 0, "charges": 2, "charge_recharge_time": 240, "placement_range": 600, "stealth": true, "reveals_stealth": false, "special_mechanics": ["invisible_to_enemies", "destroyed_by_3_auto_attacks", "grants_gold_when_killed", "trinket_item"]}, "control_ward": {"name": "Control Ward", "enabled": true, "health": 4, "duration": "infinite", "vision_range": 900, "cost": 75, "charges": "unlimited", "placement_range": 600, "stealth": false, "reveals_stealth": true, "disable_range": 900, "special_mechanics": ["visible_to_enemies", "reveals_enemy_wards", "disables_enemy_wards", "destroyed_by_4_auto_attacks", "grants_gold_when_killed", "purchasable_item"]}, "farsight_ward": {"name": "Farsight Ward", "enabled": true, "health": 1, "duration": "infinite", "vision_range": 1200, "cost": 0, "charges": 1, "charge_recharge_time": 198, "placement_range": 4000, "stealth": false, "reveals_stealth": false, "special_mechanics": ["visible_to_enemies", "extremely_long_placement_range", "destroyed_by_1_auto_attack", "grants_gold_when_killed", "trinket_upgrade"]}}, "ward_mechanics": {"max_wards_per_player": 3, "ward_limit_stealth": 2, "ward_limit_control": 1, "ward_limit_farsight": 1, "vision_score_calculation": true, "ward_kill_gold": 30, "ward_placement_delay": 0.5, "ward_vision_delay": 1.0}, "vision_interactions": {"stealth_detection": {"control_ward_reveals": true, "sweeper_reveals": true, "champion_abilities_reveal": true}, "ward_interactions": {"control_ward_disables_others": true, "wards_grant_vision_score": true, "destroying_wards_grants_gold": true}}, "enable_stealth_ward": true, "enable_control_ward": true, "enable_farsight_ward": true}}
[2025.08.26-02.53.52:812][619]LogTemp: Display: UnrealMCPBridge: Executing command: create_ward_system
[2025.08.26-02.53.53:036][619]LogTemp: Criando sistema completo de wards
[2025.08.26-02.53.53:036][619]LogTemp: Sistema de wards criado com sucesso
[2025.08.26-02.53.53:036][619]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"status": "success",
		"message": "Sistema de wards criado com sucesso",
		"data":
		{
			"system_config":
			{
				"system_name": "comprehensive_ward_system",
				"ward_types":
				{
					"stealth_ward":
					{
						"name": "Stealth Ward",
						"enabled": true,
						"health": 3,
						"duration": 150,
						"vision_range": 900,
						"cost": 0,
						"charges": 2,
						"charge_recharge_time": 240,
						"placement_range": 600,
						"stealth": true,
						"reveals_stealth": false,
						"special_mechanics": [
							"invisible_to_enemies",
							"destroyed_by_3_auto_attacks",
							"grants_gold_when_killed",
							"trinket_item"
						]
					},
					"control_ward":
					{
						"name": "Control Ward",
						"enabled": true,
						"health": 4,
						"duration": "infinite",
						"vision_range": 900,
						"cost": 75,
						"charges": "unlimited",
						"placement_range": 600,
						"stealth": false,
						"reveals_stealth": true,
						"disable_range": 900,
						"special_mechanics": [
							"visible_to_enemies",
							"reveals_enemy_wards",
							"disables_enemy_wards",
							"destroyed_by_4_auto_attacks",
							"grants_gold_when_killed",
							"purchasable_item"
						]
					},
					"farsight_ward":
					{
						"name": "Farsight Ward",
						"enabled": true,
						"health": 1,
						"duration": "infinite",
						"vision_range": 1200,
						"cost": 0,
						"charges": 1,
						"charge_recharge_time": 198,
						"placement_range": 4000,
						"stealth": false,
						"reveals_stealth": false,
						"special_mechanics": [
							"visible_to_enemies",
							"extremely_long_placement_range",
							"destroyed_by_1_auto_attack",
							"grants_gold_when_killed",
							"trinket_upgrade"
						]
					}
				},
				"ward_mechanics":
				{
					"max_wards_per_player": 3,
					"ward_limit_stealth": 2,
					"ward_limit_control": 1,
					"ward_limit_farsight": 1,
					"vision_score_calculation": true,
					"ward_kill_gold": 30,
					"ward_placement_delay": 0.5,
					"ward_vision_delay": 1
				},
				"vision_interactions":
				{
					"stealth_detection":
					{
						"control_ward_reveals": true,
						"sweeper_reveals": true,
						"champion_abilities_reveal": true
					},
					"ward_interactions":
					{
						"control_ward_disables_others": true,
						"wards_grant_vision_score": true,
						"destroying_wards_grants_gold": true
					}
				}
			},
			"system_description": "Sistema completo de wards configurado"
		}
	}
}
[2025.08.26-02.53.53:036][619]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 2615
[2025.08.26-02.53.53:036][619]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-02.53.58:872][637]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.53.58:872][637]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.53.58:872][637]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-02.53.58:973][637]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.53.58:973][637]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.53.58:973][637]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_multilayer_vision_system", "params": {"system_name": "multilayer_vision_system", "layer_vision_mechanics": {"planicie_radiante": {"layer_name": "Plan\u00edcie Radiante", "base_vision_range": 1200, "fog_of_war_enabled": true, "special_vision_mechanics": ["light_crystals_extend_vision_range", "solar_towers_provide_area_vision", "clear_terrain_standard_vision"], "vision_modifiers": {"near_light_crystals": 1.2, "near_solar_towers": 1.5, "in_jungle": 0.8}}, "firmamento_zephyr": {"layer_name": "Firmamento Zephyr", "base_vision_range": 1000, "fog_of_war_enabled": true, "special_vision_mechanics": ["wind_currents_create_vision_corridors", "mobile_platforms_dynamic_vision", "elevation_provides_extended_vision"], "vision_modifiers": {"on_wind_currents": 1.3, "on_mobile_platforms": 1.1, "at_high_elevation": 1.4}}, "abismo_umbral": {"layer_name": "Abismo Umbral", "base_vision_range": 800, "fog_of_war_enabled": true, "special_vision_mechanics": ["shadow_zones_reduce_vision_50_percent", "shadow_fog_conceals_movements", "labyrinthine_layout_blocks_vision"], "vision_modifiers": {"in_shadow_zones": 0.5, "in_shadow_fog": 0.3, "in_corridors": 0.7, "in_chambers": 1.0}}}, "cross_layer_vision": {"enabled": true, "vision_range_between_layers": 400, "requires_line_of_sight": true, "special_locations": {"portals": "provide_vision_to_connected_layer", "elevators": "provide_vision_during_travel", "bridges": "provide_vision_across_layers"}}, "vision_score_system": {"enabled": true, "ward_placement_score": 1.0, "ward_duration_score_per_minute": 0.5, "enemy_ward_destruction_score": 1.0, "vision_denial_score": 0.5}, "enable_layer_specific_vision": true, "enable_cross_layer_vision": true}}
[2025.08.26-02.53.58:974][637]LogTemp: Display: UnrealMCPBridge: Executing command: create_multilayer_vision_system
[2025.08.26-02.53.59:038][637]LogTemp: Criando sistema de visão multicamada
[2025.08.26-02.53.59:038][637]LogTemp: Sistema de visão multicamada criado com sucesso
[2025.08.26-02.53.59:038][637]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"status": "success",
		"message": "Sistema de visão multicamada criado com sucesso",
		"data":
		{
			"system_config":
			{
				"system_name": "multilayer_vision_system",
				"layer_vision_mechanics":
				{
					"planicie_radiante":
					{
						"layer_name": "Planície Radiante",
						"base_vision_range": 1200,
						"fog_of_war_enabled": true,
						"special_vision_mechanics": [
							"light_crystals_extend_vision_range",
							"solar_towers_provide_area_vision",
							"clear_terrain_standard_vision"
						],
						"vision_modifiers":
						{
							"near_light_crystals": 1.2,
							"near_solar_towers": 1.5,
							"in_jungle": 0.80000000000000004
						}
					},
					"firmamento_zephyr":
					{
						"layer_name": "Firmamento Zephyr",
						"base_vision_range": 1000,
						"fog_of_war_enabled": true,
						"special_vision_mechanics": [
							"wind_currents_create_vision_corridors",
							"mobile_platforms_dynamic_vision",
							"elevation_provides_extended_vision"
						],
						"vision_modifiers":
						{
							"on_wind_currents": 1.3,
							"on_mobile_platforms": 1.1000000000000001,
							"at_high_elevation": 1.3999999999999999
						}
					},
					"abismo_umbral":
					{
						"layer_name": "Abismo Umbral",
						"base_vision_range": 800,
						"fog_of_war_enabled": true,
						"special_vision_mechanics": [
							"shadow_zones_reduce_vision_50_percent",
							"shadow_fog_conceals_movements",
							"labyrinthine_layout_blocks_vision"
						],
						"vision_modifiers":
						{
							"in_shadow_zones": 0.5,
							"in_shadow_fog": 0.29999999999999999,
							"in_corridors": 0.69999999999999996,
							"in_chambers": 1
						}
					}
				},
				"cross_layer_vision":
				{
					"enabled": true,
					"vision_range_between_layers": 400,
					"requires_line_of_sight": true,
					"special_locations":
					{
						"portals": "provide_vision_to_connected_layer",
						"elevators": "provide_vision_during_travel",
						"bridges": "provide_vision_across_layers"
					}
				},
				"vision_score_system":
				{
					"enabled": true,
					"ward_placement_score": 1,
					"ward_duration_score_per_minute": 0.5,
					"enemy_ward_destruction_score": 1,
					"vision_denial_score": 0.5
				}
			},
			"system_description": "Sistema de visão multicamada configurado"
		}
	}
}
[2025.08.26-02.53.59:038][637]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 2442
[2025.08.26-02.53.59:038][637]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-02.54.03:365][650]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.54.03:365][650]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.54.03:365][650]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-02.54.03:466][651]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.54.03:466][651]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.54.03:466][651]LogTemp: Display: MCPServerRunnable: Received: {"type": "configure_auracron_vision_layers", "params": {}}
[2025.08.26-02.54.03:466][651]LogTemp: Display: UnrealMCPBridge: Executing command: configure_auracron_vision_layers
[2025.08.26-02.54.03:705][651]LogTemp: Configuring Auracron vision layers
[2025.08.26-02.54.03:705][651]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"status": "success",
		"message": "Auracron vision layers configured successfully",
		"data":
		{
			"layer_configurations":
			{
				"planicie":
				{
					"layer_name": "Planicie",
					"layer_type": "Radiante",
					"vision_range": 1800,
					"vision_height": 200,
					"layer_index": 1,
					"can_see_vertical_connectors": true,
					"cross_layer_vision_penalty": 0.29999999999999999
				},
				"firmamento":
				{
					"layer_name": "Firmamento",
					"layer_type": "Zephyr",
					"vision_range": 2200,
					"vision_height": 300,
					"layer_index": 2,
					"can_see_vertical_connectors": true,
					"cross_layer_vision_penalty": 0.20000000000000001
				},
				"abismo":
				{
					"layer_name": "Abismo",
					"layer_type": "Umbral",
					"vision_range": 1400,
					"vision_height": 150,
					"layer_index": 0,
					"can_see_vertical_connectors": true,
					"cross_layer_vision_penalty": 0.5
				}
			},
			"architecture": "Auracron",
			"total_layers": 3
		}
	}
}
[2025.08.26-02.54.03:705][651]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 1056
[2025.08.26-02.54.03:705][651]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-02.54.09:345][668]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.54.09:345][668]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.54.09:345][668]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-02.54.09:446][669]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.54.09:446][669]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.54.09:446][669]LogTemp: Display: MCPServerRunnable: Received: {"type": "setup_multilayer_vision_system", "params": {"vision_update_frequency": 30.0, "max_actors_per_frame": 50, "enable_fog_of_war": true, "enable_cross_layer_vision": true}}
[2025.08.26-02.54.09:446][669]LogTemp: Display: UnrealMCPBridge: Executing command: setup_multilayer_vision_system
[2025.08.26-02.54.09:705][669]LogTemp: Setting up multilayer vision system
[2025.08.26-02.54.09:705][669]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"status": "success",
		"message": "Multilayer vision system setup completed",
		"data":
		{
			"system_configuration":
			{
				"vision_update_frequency": 30,
				"max_actors_per_frame": 50,
				"enable_fog_of_war": true,
				"enable_cross_layer_vision": true,
				"performance":
				{
					"max_vision_traces_per_frame": 100,
					"vision_culling_distance": 5000,
					"enable_occlusion_culling": true,
					"enable_frustum_culling": true
				},
				"debug":
				{
					"show_vision_ranges": false,
					"show_line_of_sight_traces": false,
					"show_fog_of_war_overlay": false
				}
			},
			"status": "initialized"
		}
	}
}
[2025.08.26-02.54.09:705][669]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 698
[2025.08.26-02.54.09:705][669]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-02.54.14:137][683]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.54.14:137][683]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.54.14:137][683]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-02.54.14:238][683]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.54.14:238][683]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.54.14:238][683]LogTemp: Display: MCPServerRunnable: Received: {"type": "configure_layer_vision_ranges", "params": {"layer_name": "planicie_radiante", "vision_range": 1800.0}}
[2025.08.26-02.54.14:238][683]LogTemp: Display: UnrealMCPBridge: Executing command: configure_layer_vision_ranges
[2025.08.26-02.54.14:372][683]LogTemp: Configuring layer vision ranges
[2025.08.26-02.54.14:372][683]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"status": "success",
		"message": "Vision range configured for layer planicie_radiante",
		"data":
		{
			"range_configuration":
			{
				"layer_name": "planicie_radiante",
				"vision_range": 1800,
				"vision_height": 270,
				"range_configured": true
			}
		}
	}
}
[2025.08.26-02.54.14:372][683]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 325
[2025.08.26-02.54.14:372][683]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-02.54.18:501][696]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.54.18:501][696]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.54.18:501][696]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-02.54.18:602][696]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.54.18:602][696]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.54.18:602][696]LogTemp: Display: MCPServerRunnable: Received: {"type": "configure_layer_vision_ranges", "params": {"layer_name": "firmamento_zephyr", "vision_range": 2200.0}}
[2025.08.26-02.54.18:602][696]LogTemp: Display: UnrealMCPBridge: Executing command: configure_layer_vision_ranges
[2025.08.26-02.54.18:705][696]LogTemp: Configuring layer vision ranges
[2025.08.26-02.54.18:705][696]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"status": "success",
		"message": "Vision range configured for layer firmamento_zephyr",
		"data":
		{
			"range_configuration":
			{
				"layer_name": "firmamento_zephyr",
				"vision_range": 2200,
				"vision_height": 330,
				"range_configured": true
			}
		}
	}
}
[2025.08.26-02.54.18:705][696]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 325
[2025.08.26-02.54.18:705][696]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-02.54.24:147][730]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.54.24:147][730]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.54.24:147][730]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-02.54.24:248][732]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.54.24:248][732]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.54.24:248][732]LogTemp: Display: MCPServerRunnable: Received: {"type": "configure_layer_vision_ranges", "params": {"layer_name": "abismo_umbral", "vision_range": 1400.0}}
[2025.08.26-02.54.24:248][732]LogTemp: Display: UnrealMCPBridge: Executing command: configure_layer_vision_ranges
[2025.08.26-02.54.24:249][732]LogTemp: Configuring layer vision ranges
[2025.08.26-02.54.24:249][732]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"status": "success",
		"message": "Vision range configured for layer abismo_umbral",
		"data":
		{
			"range_configuration":
			{
				"layer_name": "abismo_umbral",
				"vision_range": 1400,
				"vision_height": 210,
				"range_configured": true
			}
		}
	}
}
[2025.08.26-02.54.24:249][732]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 317
[2025.08.26-02.54.24:249][732]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-02.54.35:308][979]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.54.35:308][979]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.54.35:308][979]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-02.54.35:409][981]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.54.35:409][981]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.54.35:409][981]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_primordial_dragons_system", "params": {"system_name": "primordial_dragons_system", "spawn_timer": 300, "dragons": {"infernal_dragon": {"name": "Drag\u00e3o Infernal", "enabled": true, "health": 3500, "health_per_minute": 240, "damage": 300, "damage_per_minute": 18, "abilities": ["Breath of Fire - AoE burn damage", "Molten Armor - Reflects damage", "Infernal Charge - Gap closer with knockback"], "team_buff": {"name": "Infernal Might", "effect": "+8% Attack Damage and Ability Power", "duration": 150, "stacks": true, "max_stacks": 3}, "gold_reward": 25, "experience_reward": 75}, "ocean_dragon": {"name": "Drag\u00e3o Oce\u00e2nico", "enabled": true, "health": 3200, "health_per_minute": 220, "damage": 280, "damage_per_minute": 16, "abilities": ["Tidal Wave - Line skillshot with slow", "Healing Waters - AoE heal over time", "Aquatic Shield - Damage absorption"], "team_buff": {"name": "Ocean's Blessing", "effect": "+6% missing health and mana regeneration", "duration": 150, "stacks": true, "max_stacks": 3}, "gold_reward": 25, "experience_reward": 75}, "mountain_dragon": {"name": "Drag\u00e3o da Montanha", "enabled": true, "health": 4000, "health_per_minute": 280, "damage": 320, "damage_per_minute": 20, "abilities": ["Boulder Throw - High damage projectile", "Stone Skin - Damage reduction", "Earthquake - AoE knockup"], "team_buff": {"name": "Mountain's Fortitude", "effect": "+6% bonus armor and magic resistance", "duration": 150, "stacks": true, "max_stacks": 3}, "gold_reward": 25, "experience_reward": 75}, "cloud_dragon": {"name": "Drag\u00e3o das Nuvens", "enabled": true, "health": 3000, "health_per_minute": 200, "damage": 260, "damage_per_minute": 14, "abilities": ["Lightning Strike - Instant damage", "Wind Barrier - Projectile blocking", "Storm Flight - Temporary untargetability"], "team_buff": {"name": "Cloud's Swiftness", "effect": "+3% movement speed and +6% ultimate CDR", "duration": 150, "stacks": true, "max_stacks": 3}, "gold_reward": 25, "experience_reward": 75}, "elder_dragon": {"name": "Drag\u00e3o Anci\u00e3o", "enabled": true, "health": 6000, "health_per_minute": 180, "damage": 400, "damage_per_minute": 45, "spawn_requirement": "35 minutes + 2 dragon souls", "abilities": ["Elder's Wrath - Execute threshold", "Ancient Roar - Fear and damage", "Draconic Fury - Enrage mechanic"], "team_buff": {"name": "Aspect of the Dragon", "effect": "Burning damage on attacks and spells", "duration": 150, "execute_threshold": "20% max health"}, "gold_reward": 50, "experience_reward": 150}}, "enable_infernal_dragon": true, "enable_ocean_dragon": true, "enable_mountain_dragon": true, "enable_cloud_dragon": true, "enable_elder_dragon": true}}
[2025.08.26-02.54.35:409][982]LogTemp: Display: UnrealMCPBridge: Executing command: create_primordial_dragons_system
[2025.08.26-02.54.35:410][982]LogTemp: Criando sistema de Dragões Primordiais
[2025.08.26-02.54.35:410][982]LogTemp: Sistema de Dragões Primordiais criado com sucesso
[2025.08.26-02.54.35:410][982]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"status": "success",
		"message": "Sistema de Dragões Primordiais criado com sucesso",
		"data":
		{
			"system_config":
			{
				"system_name": "primordial_dragons_system",
				"dragons":
				{
					"infernal_dragon":
					{
						"name": "Dragão Infernal",
						"enabled": true,
						"health": 3500,
						"health_per_minute": 240,
						"spawn_time": 5,
						"respawn_time": 300,
						"buff_effect": "+8% AD e +12 AP",
						"soul_effect": "Ataques e habilidades causam explosões"
					},
					"ocean_dragon":
					{
						"name": "Dragão Oceânico",
						"enabled": true,
						"health": 3500,
						"health_per_minute": 240,
						"spawn_time": 5,
						"respawn_time": 300,
						"buff_effect": "Regeneração de HP e MP",
						"soul_effect": "Habilidades curam aliados próximos"
					},
					"mountain_dragon":
					{
						"name": "Dragão da Montanha",
						"enabled": true,
						"health": 3500,
						"health_per_minute": 240,
						"spawn_time": 5,
						"respawn_time": 300,
						"buff_effect": "+6% Armor e +6% MR",
						"soul_effect": "Escudo após usar habilidade"
					},
					"cloud_dragon":
					{
						"name": "Dragão das Nuvens",
						"enabled": true,
						"health": 3500,
						"health_per_minute": 240,
						"spawn_time": 5,
						"respawn_time": 300,
						"buff_effect": "+3% Velocidade de Movimento",
						"soul_effect": "Velocidade extra após usar ultimate"
					},
					"elder_dragon":
					{
						"name": "Dragão Ancião",
						"enabled": true,
						"health": 6400,
						"health_per_minute": 180,
						"spawn_time": 35,
						"respawn_time": 360,
						"buff_effect": "Queimadura que executa inimigos com HP baixo",
						"execute_threshold": 20
					}
				}
			},
			"system_description": "Sistema de Dragões Primordiais configurado"
		},
		"timestamp": "2025.08.25-23.54.35"
	}
}
[2025.08.26-02.54.35:410][982]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 1953
[2025.08.26-02.54.35:410][982]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-02.54.40:844][ 59]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.54.40:844][ 59]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.54.40:844][ 59]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-02.54.40:945][ 59]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.54.40:945][ 59]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.54.40:945][ 59]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_baron_auracron_system", "params": {"system_name": "baron_auracron_system", "spawn_time": 20, "respawn_timer": 420, "enable_phases": true, "baron_stats": {"name": "Baron Auracron", "health": 8000, "health_per_minute": 180, "damage": 400, "damage_per_minute": 50, "armor": 120, "magic_resistance": 70, "location": "Baron Pit - Upper River"}, "abilities": {"auracron_slam": {"name": "Auracron Slam", "type": "AoE Knockback", "damage": 300, "cooldown": 8, "range": 400}, "void_spikes": {"name": "Void Spikes", "type": "Line Skillshot", "damage": 250, "cooldown": 6, "range": 900, "effect": "Reduces healing by 50%"}, "baron_gaze": {"name": "Baron's Gaze", "type": "Debuff", "effect": "Reduces damage by 50%", "duration": 4, "cooldown": 20}, "auracron_wrath": {"name": "Auracron's Wrath", "type": "Enrage", "trigger": "Below 25% health", "effect": "+100% attack speed, +50% damage"}}, "team_buff": {"name": "Hand of Baron", "effects": ["+40 Attack Damage", "+40 Ability Power", "Empowered Recall (4 seconds)", "Enhanced minions with Baron aura", "Minions gain +50% damage to structures"], "duration": 180, "aura_range": 1200}, "rewards": {"gold_per_player": 300, "experience_per_player": 150, "team_gold": 1500}}}
[2025.08.26-02.54.40:945][ 59]LogTemp: Display: UnrealMCPBridge: Executing command: create_baron_auracron_system
[2025.08.26-02.54.40:996][ 59]LogTemp: Criando sistema de Baron Auracron
[2025.08.26-02.54.40:997][ 59]LogTemp: Sistema de Baron Auracron criado com sucesso
[2025.08.26-02.54.40:997][ 59]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"status": "success",
		"message": "Sistema de Baron Auracron criado com sucesso",
		"data":
		{
			"system_config":
			{
				"system_name": "baron_auracron_system",
				"baron_auracron":
				{
					"name": "Baron Auracron",
					"enabled": true,
					"health": 9000,
					"health_per_minute": 180,
					"spawn_time": 20,
					"respawn_time": 420,
					"buff_duration": 180,
					"hand_of_baron":
					{
						"name": "Hand of Baron",
						"duration": 180,
						"buff_effects": [
							"+40 AD",
							"+40 AP",
							"Recall aprimorado (4s)",
							"Minions empoderados",
							"Dano extra a estruturas"
						]
					}
				}
			},
			"system_description": "Sistema de Baron Auracron configurado"
		},
		"timestamp": "2025.08.25-23.54.40"
	}
}
[2025.08.26-02.54.40:997][ 59]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 828
[2025.08.26-02.54.40:997][ 59]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-02.54.45:726][ 74]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.54.45:726][ 74]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.54.45:726][ 74]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-02.54.45:827][ 74]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.54.45:827][ 74]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.54.45:827][ 74]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_towers_system", "params": {"system_name": "towers_system", "tower_types": {"outer_tower": {"name": "Torre Externa", "enabled": true, "health": 2500, "armor": 40, "magic_resistance": 40, "damage": 152, "damage_per_minute": 4, "attack_speed": 0.83, "range": 775, "gold_reward": 650, "local_gold": 100, "special_mechanics": ["Fortification - 66% damage reduction for 5 minutes", "Warming Up - Increasing damage on same target", "Backdoor Protection - Extra resistances without minions"]}, "inner_tower": {"name": "Torre Interna", "enabled": true, "health": 3000, "armor": 55, "magic_resistance": 55, "damage": 170, "damage_per_minute": 4, "attack_speed": 0.83, "range": 775, "gold_reward": 700, "local_gold": 125, "special_mechanics": ["Enhanced Fortification - 75% damage reduction", "Laser Focus - True damage after 3 shots", "Structural Integrity - Requires outer tower down"]}, "inhibitor_tower": {"name": "Torre do Inibidor", "enabled": true, "health": 4000, "armor": 65, "magic_resistance": 65, "damage": 200, "damage_per_minute": 4, "attack_speed": 0.83, "range": 775, "gold_reward": 750, "local_gold": 150, "special_mechanics": ["Maximum Fortification - 80% damage reduction", "Guardian Protocol - Shields nearby structures", "Requires inner tower destruction"]}, "nexus_tower": {"name": "Torre do Nexus", "enabled": true, "health": 4500, "armor": 75, "magic_resistance": 75, "damage": 230, "damage_per_minute": 4, "attack_speed": 0.83, "range": 775, "gold_reward": 50, "local_gold": 50, "special_mechanics": ["Ultimate Fortification - 85% damage reduction", "Nexus Defense - Regenerates health", "Final Stand - Increased damage when alone"]}}, "global_mechanics": {"fortification_duration": 300, "backdoor_protection": true, "warming_up_stacks": 6, "warming_up_damage_per_stack": 25, "structure_regeneration": true, "regeneration_delay": 30}, "enable_outer_towers": true, "enable_inner_towers": true, "enable_inhibitor_towers": true, "enable_nexus_towers": true}}
[2025.08.26-02.54.45:827][ 74]LogTemp: Display: UnrealMCPBridge: Executing command: create_towers_system
[2025.08.26-02.54.45:997][ 74]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "error",
	"error": "Unknown command: create_towers_system"
}
[2025.08.26-02.54.45:997][ 74]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 77
[2025.08.26-02.54.45:997][ 74]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-02.54.54:449][100]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.54.54:449][100]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.54.54:449][100]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-02.54.54:550][100]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.54.54:550][100]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.54.54:550][100]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_towers_system", "params": {"enable_outer_towers": true, "enable_inner_towers": true, "enable_inhibitor_towers": true, "enable_nexus_towers": true}}
[2025.08.26-02.54.54:550][100]LogTemp: Display: UnrealMCPBridge: Executing command: create_towers_system
[2025.08.26-02.54.54:664][100]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "error",
	"error": "Unknown command: create_towers_system"
}
[2025.08.26-02.54.54:664][100]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 77
[2025.08.26-02.54.54:664][100]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-02.55.13:898][158]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.55.13:898][158]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.55.13:898][158]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-02.55.13:999][158]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.55.13:999][158]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.55.13:999][158]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_tower_system", "params": {"enable_outer_towers": true, "enable_inner_towers": true, "enable_inhibitor_towers": true, "enable_nexus_towers": true}}
[2025.08.26-02.55.13:999][158]LogTemp: Display: UnrealMCPBridge: Executing command: create_tower_system
[2025.08.26-02.55.14:001][158]LogTemp: Criando sistema de Torres
[2025.08.26-02.55.14:001][158]LogTemp: Sistema de Torres criado com sucesso
[2025.08.26-02.55.14:001][158]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"status": "success",
		"message": "Sistema de Torres criado com sucesso",
		"data":
		{
			"system_config":
			{
				"system_name": "tower_system",
				"tower_types":
				{
					"outer_tower":
					{
						"name": "Torre Externa",
						"health": 5000,
						"damage": 152,
						"gold_reward": 650,
						"fortification": true
					},
					"inner_tower":
					{
						"name": "Torre Interna",
						"health": 5500,
						"damage": 170,
						"gold_reward": 700,
						"fortification": false
					},
					"inhibitor_tower":
					{
						"name": "Torre do Inibidor",
						"health": 4000,
						"damage": 190,
						"gold_reward": 750,
						"fortification": false
					},
					"nexus_towers":
					{
						"name": "Torres do Nexus",
						"health": 4000,
						"damage": 230,
						"gold_reward": 50,
						"count": 2
					}
				}
			},
			"system_description": "Sistema de Torres configurado"
		},
		"timestamp": "2025.08.25-23.55.14"
	}
}
[2025.08.26-02.55.14:002][158]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 1032
[2025.08.26-02.55.14:002][158]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-02.55.21:047][180]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.55.21:047][180]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.55.21:047][180]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-02.55.21:148][180]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.55.21:148][180]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.55.21:148][180]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_inhibitor_system", "params": {"respawn_timer": 300, "enable_super_minions": true}}
[2025.08.26-02.55.21:148][180]LogTemp: Display: UnrealMCPBridge: Executing command: create_inhibitor_system
[2025.08.26-02.55.21:337][180]LogTemp: Criando sistema de Inibidores
[2025.08.26-02.55.21:337][180]LogTemp: Sistema de Inibidores criado com sucesso
[2025.08.26-02.55.21:337][180]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"status": "success",
		"message": "Sistema de Inibidores criado com sucesso",
		"data":
		{
			"system_config":
			{
				"system_name": "inhibitor_system",
				"inhibitor":
				{
					"name": "Inibidor",
					"health": 4000,
					"respawn_time": 300,
					"gold_reward": 50,
					"enable_super_minions": true,
					"super_minions":
					{
						"name": "Super Minions",
						"health": 1500,
						"damage": 190,
						"armor": 30,
						"magic_resist": 30,
						"siege_damage": true
					}
				}
			},
			"system_description": "Sistema de Inibidores configurado"
		},
		"timestamp": "2025.08.25-23.55.21"
	}
}
[2025.08.26-02.55.21:337][180]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 685
[2025.08.26-02.55.21:337][180]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-02.55.35:319][429]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.55.35:319][429]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.55.35:319][429]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-02.55.35:420][432]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.55.35:420][432]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.55.35:420][432]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_damage_system", "params": {"system_name": "comprehensive_damage_system", "damage_types": {"physical_damage": {"enabled": true, "calculation": "base_damage * (100 / (100 + armor))", "penetration_types": ["flat_armor_penetration", "percent_armor_penetration", "lethality"], "reduction_sources": ["armor", "damage_reduction"], "amplification_sources": ["critical_strike", "damage_amplification"]}, "magic_damage": {"enabled": true, "calculation": "base_damage * (100 / (100 + magic_resistance))", "penetration_types": ["flat_magic_penetration", "percent_magic_penetration", "magic_penetration"], "reduction_sources": ["magic_resistance", "magic_damage_reduction"], "amplification_sources": ["ability_power_scaling", "magic_amplification"]}, "true_damage": {"enabled": true, "calculation": "base_damage", "description": "Ignores all resistances and damage reduction", "sources": ["specific_abilities", "items", "runes"], "cannot_be_reduced": true}, "mixed_damage": {"enabled": true, "calculation": "physical_portion + magic_portion", "description": "Combination of physical and magic damage", "split_ratios": {"50_50": "Equal physical and magic", "60_40": "Physical favored", "40_60": "Magic favored", "custom": "Ability-specific ratios"}}}, "damage_modifiers": {"critical_strike": {"base_multiplier": 2.0, "infinity_edge_multiplier": 2.35, "crit_chance_cap": 1.0, "applies_to": ["physical_damage", "some_abilities"]}, "armor_penetration": {"calculation_order": ["percent_bonus_armor_penetration", "flat_armor_penetration", "percent_armor_penetration", "lethality"]}, "magic_penetration": {"calculation_order": ["percent_magic_penetration", "flat_magic_penetration"]}}, "special_damage_mechanics": {"execute_damage": {"description": "Deals damage based on missing health", "calculation": "base_damage + (missing_health_percent * scaling)"}, "max_health_damage": {"description": "Deals damage based on maximum health", "calculation": "base_damage + (max_health_percent * scaling)", "damage_type": "usually_magic_or_true"}, "current_health_damage": {"description": "Deals damage based on current health", "calculation": "base_damage + (current_health_percent * scaling)"}}, "enable_physical_damage": true, "enable_magic_damage": true, "enable_true_damage": true, "enable_mixed_damage": true}}
[2025.08.26-02.55.35:420][432]LogTemp: Display: UnrealMCPBridge: Executing command: create_damage_system
[2025.08.26-02.55.35:420][432]LogTemp: Criando sistema de Dano
[2025.08.26-02.55.35:420][432]LogTemp: Sistema de Dano criado com sucesso
[2025.08.26-02.55.35:421][432]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"status": "success",
		"message": "Sistema de Dano criado com sucesso",
		"data":
		{
			"system_config":
			{
				"system_name": "damage_system",
				"damage_types":
				{
					"physical_damage":
					{
						"name": "Dano Físico",
						"enabled": true,
						"resistance_stat": "Armor",
						"penetration_stat": "Armor Penetration / Lethality",
						"scaling_stat": "Attack Damage",
						"damage_sources": [
							"Auto-ataques",
							"Habilidades AD",
							"Itens físicos"
						]
					},
					"magic_damage":
					{
						"name": "Dano Mágico",
						"enabled": true,
						"resistance_stat": "Magic Resist",
						"penetration_stat": "Magic Penetration",
						"scaling_stat": "Ability Power",
						"damage_sources": [
							"Habilidades AP",
							"Itens mágicos",
							"Efeitos passivos"
						]
					},
					"true_damage":
					{
						"name": "Dano Verdadeiro",
						"enabled": true,
						"resistance_stat": "Nenhuma",
						"penetration_stat": "Não aplicável",
						"special_property": "Ignora todas as resistências",
						"damage_sources": [
							"Habilidades especiais",
							"Itens únicos",
							"Execuções"
						]
					},
					"mixed_damage":
					{
						"name": "Dano Misto",
						"enabled": true,
						"description": "Combinação de tipos de dano",
						"damage_sources": [
							"Champions híbridos",
							"Itens híbridos",
							"Conversões de dano"
						]
					}
				}
			},
			"system_description": "Sistema de Dano configurado"
		},
		"timestamp": "2025.08.25-23.55.35"
	}
}
[2025.08.26-02.55.35:421][432]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 1653
[2025.08.26-02.55.35:421][432]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-02.55.37:281][498]LogSlate: Took 0.006837 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Italic.ttf' (157K)
[2025.08.26-02.55.40:953][713]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.55.40:953][713]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.55.40:953][713]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-02.55.41:053][718]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.55.41:053][718]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.55.41:053][718]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_crowd_control_system", "params": {"system_name": "crowd_control_system", "hard_cc_types": {"stun": {"name": "Atordoamento", "enabled": true, "description": "Impede movimento, ataques e habilidades", "affected_by_tenacity": true, "can_be_cleansed": true, "prevents": ["movement", "attacks", "abilities", "summoner_spells"]}, "root": {"name": "Enraizamento", "enabled": true, "description": "Impede movimento mas permite ataques e habilidades", "affected_by_tenacity": true, "can_be_cleansed": true, "prevents": ["movement"], "allows": ["attacks", "abilities", "summoner_spells"]}, "suppress": {"name": "Supress\u00e3o", "enabled": true, "description": "CC mais forte que n\u00e3o pode ser removido", "affected_by_tenacity": false, "can_be_cleansed": false, "prevents": ["movement", "attacks", "abilities", "summoner_spells"], "special": "Cannot be reduced or removed"}, "knockup": {"name": "Levantamento", "enabled": true, "description": "Lan\u00e7a o alvo no ar", "affected_by_tenacity": false, "can_be_cleansed": false, "prevents": ["movement", "attacks", "abilities"], "special": "Airborne state"}, "fear": {"name": "Medo", "enabled": true, "description": "For\u00e7a movimento aleat\u00f3rio", "affected_by_tenacity": true, "can_be_cleansed": true, "prevents": ["controlled_movement", "attacks", "abilities"], "forces": ["random_movement"]}, "charm": {"name": "Encanto", "enabled": true, "description": "For\u00e7a movimento em dire\u00e7\u00e3o ao conjurador", "affected_by_tenacity": true, "can_be_cleansed": true, "prevents": ["controlled_movement", "attacks", "abilities"], "forces": ["movement_toward_caster"]}, "taunt": {"name": "Provoca\u00e7\u00e3o", "enabled": true, "description": "For\u00e7a ataques contra o conjurador", "affected_by_tenacity": true, "can_be_cleansed": true, "prevents": ["ability_usage", "movement_away"], "forces": ["attacks_on_caster"]}}, "soft_cc_types": {"slow": {"name": "Lentid\u00e3o", "enabled": true, "description": "Reduz velocidade de movimento", "affected_by_tenacity": true, "can_be_cleansed": true, "effect": "movement_speed_reduction", "stacks": "multiplicatively"}, "blind": {"name": "Cegueira", "enabled": true, "description": "Faz ataques errarem", "affected_by_tenacity": true, "can_be_cleansed": true, "effect": "auto_attacks_miss"}, "silence": {"name": "Sil\u00eancio", "enabled": true, "description": "Impede uso de habilidades", "affected_by_tenacity": true, "can_be_cleansed": true, "prevents": ["abilities"], "allows": ["movement", "attacks"]}, "disarm": {"name": "Desarmar", "enabled": true, "description": "Impede ataques b\u00e1sicos", "affected_by_tenacity": true, "can_be_cleansed": true, "prevents": ["attacks"], "allows": ["movement", "abilities"]}, "cripple": {"name": "Aleijamento", "enabled": true, "description": "Reduz velocidade de ataque", "affected_by_tenacity": true, "can_be_cleansed": true, "effect": "attack_speed_reduction"}}, "enable_hard_cc": true, "enable_soft_cc": true, "enable_tenacity": true}}
[2025.08.26-02.55.41:053][718]LogTemp: Display: UnrealMCPBridge: Executing command: create_crowd_control_system
[2025.08.26-02.55.41:060][718]LogTemp: Criando sistema de Crowd Control
[2025.08.26-02.55.41:060][718]LogTemp: Sistema de Crowd Control criado com sucesso
[2025.08.26-02.55.41:060][718]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"status": "success",
		"message": "Sistema de Crowd Control criado com sucesso",
		"data":
		{
			"system_config":
			{
				"system_name": "crowd_control_system",
				"cc_types":
				{
					"hard_cc":
					{
						"name": "Hard Crowd Control",
						"enabled": true,
						"description": "Impede completamente ações do alvo",
						"cc_types": [
							"Stun - Impede movimento e ações",
							"Root - Impede movimento, permite ações",
							"Suppress - Impede tudo, incluindo Summoner Spells",
							"Knockup - Deslocamento forçado no ar",
							"Fear - Movimento aleatório incontrolável",
							"Charm - Força movimento em direção ao caster",
							"Taunt - Força ataques contra o caster"
						]
					},
					"soft_cc":
					{
						"name": "Soft Crowd Control",
						"enabled": true,
						"description": "Reduz efetividade sem impedir completamente",
						"cc_types": [
							"Slow - Reduz velocidade de movimento",
							"Blind - Impede acerto de auto-ataques",
							"Silence - Impede uso de habilidades",
							"Disarm - Impede auto-ataques",
							"Cripple - Reduz velocidade de ataque"
						]
					}
				}
			},
			"system_description": "Sistema de Crowd Control configurado"
		},
		"timestamp": "2025.08.25-23.55.41"
	}
}
[2025.08.26-02.55.41:060][719]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 1347
[2025.08.26-02.55.41:060][719]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-02.55.45:987][897]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.55.45:987][897]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.55.45:987][897]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-02.55.46:089][897]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.55.46:089][897]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.55.46:089][897]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_healing_system", "params": {"system_name": "healing_sustain_system", "regeneration_types": {"health_regeneration": {"enabled": true, "base_regen": 1.5, "per_level": 0.11, "calculation": "base_regen + (level * per_level) + bonus_regen", "affected_by": ["grievous_wounds", "healing_amplification"], "out_of_combat_bonus": 0.0, "in_combat_reduction": 0.0}, "mana_regeneration": {"enabled": true, "base_regen": 2.0, "per_level": 0.13, "calculation": "base_regen + (level * per_level) + bonus_regen", "affected_by": ["mana_regen_items", "abilities"], "out_of_combat_bonus": 1.0, "meditation_bonus": 0.5}}, "lifesteal_mechanics": {"physical_lifesteal": {"enabled": true, "description": "Heals based on physical damage dealt", "calculation": "physical_damage * lifesteal_percent", "applies_to": ["auto_attacks", "physical_abilities"], "reduced_by": ["grievous_wounds"], "aoe_effectiveness": 0.33}, "spell_vamp": {"enabled": true, "description": "Heals based on magic damage dealt", "calculation": "magic_damage * spell_vamp_percent", "applies_to": ["abilities", "magic_damage"], "reduced_by": ["grievous_wounds"], "aoe_effectiveness": 0.33}, "omnivamp": {"enabled": true, "description": "Heals based on all damage dealt", "calculation": "total_damage * omnivamp_percent", "applies_to": ["all_damage_types"], "reduced_by": ["grievous_wounds"], "aoe_effectiveness": 0.33}}, "healing_reduction": {"grievous_wounds": {"name": "Ferimentos Graves", "enabled": true, "reduction_percent": 0.4, "enhanced_reduction_percent": 0.6, "applies_to": ["all_healing", "regeneration", "lifesteal"], "sources": ["items", "abilities", "runes"], "duration_typical": 3.0}, "healing_amplification": {"name": "Amplifica\u00e7\u00e3o de Cura", "enabled": true, "amplification_sources": ["spirit_visage", "revitalize_rune", "healing_abilities"], "typical_amplification": 0.25}}, "special_healing_mechanics": {"shields": {"description": "Temporary health that absorbs damage", "types": ["physical_shield", "magic_shield", "all_damage_shield"], "decay": "some_shields_decay_over_time", "stacking": "shields_stack_additively"}, "true_healing": {"description": "Healing that cannot be reduced", "sources": ["specific_abilities", "fountain"], "ignores": ["grievous_wounds", "healing_reduction"]}, "percentage_healing": {"description": "Healing based on max health percentage", "calculation": "max_health * percentage", "common_percentages": [0.02, 0.05, 0.1, 0.15]}}, "enable_health_regeneration": true, "enable_mana_regeneration": true, "enable_lifesteal": true, "enable_spell_vamp": true}}
[2025.08.26-02.55.46:089][897]LogTemp: Display: UnrealMCPBridge: Executing command: create_healing_system
[2025.08.26-02.55.46:112][897]LogTemp: Criando sistema de Cura
[2025.08.26-02.55.46:112][897]LogTemp: Sistema de Cura criado com sucesso
[2025.08.26-02.55.46:112][897]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"status": "success",
		"message": "Sistema de Cura criado com sucesso",
		"data":
		{
			"system_config":
			{
				"system_name": "healing_system",
				"healing_types":
				{
					"regeneration":
					{
						"name": "Regeneração",
						"description": "Cura passiva ao longo do tempo"
					},
					"lifesteal":
					{
						"name": "Lifesteal",
						"description": "Cura baseada em dano físico causado"
					},
					"spell_vamp":
					{
						"name": "Spell Vamp",
						"description": "Cura baseada em dano mágico causado"
					},
					"omnivamp":
					{
						"name": "Omnivamp",
						"description": "Cura baseada em qualquer dano causado"
					}
				}
			},
			"system_description": "Sistema de Cura configurado"
		},
		"timestamp": "2025.08.25-23.55.46"
	}
}
[2025.08.26-02.55.46:112][897]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 849
[2025.08.26-02.55.46:112][897]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-02.55.51:048][912]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.55.51:048][912]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.55.51:048][912]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-02.55.51:150][913]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.55.51:150][913]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.55.51:151][913]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_tenacity_system", "params": {"system_name": "tenacity_system", "base_tenacity": 0, "max_tenacity": 0.95, "enable_stacking": true, "tenacity_mechanics": {"calculation": "1 - ((1 - tenacity1) * (1 - tenacity2) * ...)", "description": "Reduces duration of crowd control effects", "applies_to": ["stun", "root", "fear", "charm", "taunt", "slow", "blind", "silence", "disarm", "cripple"], "does_not_apply_to": ["suppress", "knockup", "displacement"]}, "tenacity_sources": {"items": {"mercury_treads": 0.3, "legend_tenacity": 0.3, "steraks_gage": 0.3, "silvermere_dawn": 0.5}, "runes": {"legend_tenacity": 0.3, "unflinching": 0.15}, "abilities": {"champion_specific": "varies_by_champion", "temporary_tenacity": "some_abilities_grant_temporary"}, "masteries": {"tenacity_mastery": 0.15}}, "special_interactions": {"diminishing_returns": {"enabled": false, "description": "Tenacity stacks multiplicatively, not additively"}, "cc_immunity": {"description": "Some abilities grant temporary CC immunity", "examples": ["olaf_ultimate", "malzahar_passive", "morgana_black_shield"]}, "unstoppable": {"description": "Unstoppable effects ignore all CC", "examples": ["sion_ultimate", "malphite_ultimate"]}}}}
[2025.08.26-02.55.51:151][913]LogTemp: Display: UnrealMCPBridge: Executing command: create_tenacity_system
[2025.08.26-02.55.51:447][913]LogTemp: Criando sistema de Tenacidade
[2025.08.26-02.55.51:447][913]LogTemp: Sistema de Tenacidade criado com sucesso
[2025.08.26-02.55.51:447][913]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"status": "success",
		"message": "Sistema de Tenacidade criado com sucesso",
		"data":
		{
			"system_config":
			{
				"system_name": "tenacity_system",
				"tenacity":
				{
					"name": "Tenacidade",
					"description": "Reduz duração de efeitos de crowd control",
					"max_reduction_percent": 51,
					"affects_knockups": false,
					"affects_suppression": false,
					"stacking_mechanics": true,
					"affected_cc": [
						"Stun",
						"Root",
						"Fear",
						"Charm",
						"Taunt",
						"Slow",
						"Blind",
						"Silence"
					]
				}
			},
			"system_description": "Sistema de Tenacidade configurado"
		},
		"timestamp": "2025.08.25-23.55.51"
	}
}
[2025.08.26-02.55.51:447][913]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 746
[2025.08.26-02.55.51:447][913]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-02.56.03:448][949]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.56.03:448][949]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.56.03:448][949]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-02.56.03:550][950]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.56.03:550][950]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.56.03:550][950]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_early_game_phase", "params": {"phase_name": "early_game", "duration_minutes": 15, "start_time": 0, "end_time": 15, "primary_objectives": ["Estabelecer vantagem de CS", "Controlar posicionamento de waves", "Evitar ganks enquanto pressiona", "Estabelecer controle de vis\u00e3o", "Primeiro clear da jungle", "Primeiros recalls estrat\u00e9gicos"], "key_mechanics": {"laning_phase": {"enabled": true, "focus": "individual_skill_expression", "mechanics": ["last_hitting_minions", "trading_stance", "wave_management", "back_timing", "mana_management"]}, "jungle_establishment": {"enabled": true, "focus": "clear_routes_and_ganks", "mechanics": ["optimal_clear_paths", "gank_timing", "objective_control", "counter_jungling"]}, "vision_control": {"enabled": true, "focus": "basic_warding", "mechanics": ["trinket_ward_placement", "river_control", "jungle_entrances", "lane_bushes"]}}, "milestone_events": {"first_blood": {"typical_time": "2-5 minutes", "impact": "psychological_advantage"}, "first_tower": {"typical_time": "8-12 minutes", "impact": "map_control_shift"}, "first_dragon": {"typical_time": "6-10 minutes", "impact": "team_buff_advantage"}, "jungle_item_completion": {"typical_time": "6-8 minutes", "impact": "jungle_power_spike"}}, "transition_conditions": {"to_mid_game": ["multiple_towers_destroyed", "team_grouping_begins", "objective_contests_increase", "roaming_frequency_increases"]}, "enable_laning_focus": true, "enable_jungle_establishment": true}}
[2025.08.26-02.56.03:550][950]LogTemp: Display: UnrealMCPBridge: Executing command: create_early_game_phase
[2025.08.26-02.56.03:783][950]LogTemp: Criando fase Early Game
[2025.08.26-02.56.03:783][950]LogTemp: Fase Early Game criada com sucesso
[2025.08.26-02.56.03:783][950]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"status": "success",
		"message": "Fase Early Game criada com sucesso",
		"data":
		{
			"phase_config":
			{
				"phase_name": "early_game",
				"duration_minutes": 15,
				"start_time": 0,
				"end_time": 15,
				"primary_objectives": [
					"Estabelecer vantagem de CS",
					"Controlar posicionamento de waves",
					"Evitar ganks enquanto pressiona",
					"Estabelecer controle de visão",
					"Primeiro clear da jungle",
					"Primeiros recalls estratégicos"
				],
				"key_mechanics":
				{
					"laning_phase":
					{
						"enabled": true,
						"focus": "individual_skill_expression",
						"mechanics": [
							"last_hitting_minions",
							"trading_stance",
							"wave_management",
							"back_timing",
							"mana_management"
						]
					},
					"jungle_establishment":
					{
						"enabled": true,
						"focus": "clear_routes_and_ganks",
						"mechanics": [
							"optimal_clear_paths",
							"gank_timing",
							"objective_control",
							"counter_jungling"
						]
					}
				},
				"milestone_events":
				{
					"first_blood":
					{
						"typical_time": "2-5 minutes",
						"impact": "psychological_advantage"
					},
					"first_tower":
					{
						"typical_time": "8-12 minutes",
						"impact": "map_control_shift"
					},
					"first_dragon":
					{
						"typical_time": "6-10 minutes",
						"impact": "team_buff_advantage"
					}
				},
				"transition_conditions": [
					"multiple_towers_destroyed",
					"team_grouping_begins",
					"objective_contests_increase",
					"roaming_frequency_increases"
				]
			},
			"phase_state":
			{
				"status": "created",
				"creation_time": "2025.08.25-23.56.03",
				"active": true
			},
			"phase_description": "Fase inicial com foco em laning e estabelecimento"
		},
		"timestamp": "2025.08.25-23.56.03"
	}
}
[2025.08.26-02.56.03:783][950]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 1924
[2025.08.26-02.56.03:783][950]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-02.56.08:010][963]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.56.08:010][963]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.56.08:010][963]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-02.56.08:111][963]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.56.08:111][963]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.56.08:111][963]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_mid_game_phase", "params": {"phase_name": "mid_game", "duration_minutes": 15, "start_time": 15, "end_time": 30, "primary_objectives": ["Transi\u00e7\u00e3o para teamfights", "Controle de objetivos neutros", "Roaming e press\u00e3o no mapa", "Estabelecer vantagens de itens", "Controle de territ\u00f3rio", "Prepara\u00e7\u00e3o para late game"], "key_mechanics": {"teamfight_focus": {"enabled": true, "focus": "coordinated_team_play", "mechanics": ["team_positioning", "engage_timing", "target_prioritization", "ability_coordination", "peel_mechanics"]}, "objective_control": {"enabled": true, "focus": "neutral_objectives", "mechanics": ["dragon_control", "herald_utilization", "tower_sieging", "jungle_control", "vision_warfare"]}, "map_pressure": {"enabled": true, "focus": "territorial_control", "mechanics": ["split_pushing", "roam_timing", "wave_manipulation", "cross_map_plays", "vertical_navigation_usage"]}}, "milestone_events": {"first_teamfight": {"typical_time": "15-18 minutes", "impact": "team_coordination_test"}, "multiple_dragons": {"typical_time": "18-25 minutes", "impact": "significant_team_buffs"}, "baron_spawn": {"typical_time": "20 minutes", "impact": "major_objective_available"}, "core_items_completed": {"typical_time": "20-25 minutes", "impact": "champion_power_spikes"}}, "transition_conditions": {"to_late_game": ["multiple_inhibitors_threatened", "baron_contests_frequent", "full_item_builds_approaching", "death_timers_significant"]}, "enable_teamfight_focus": true, "enable_objective_control": true}}
[2025.08.26-02.56.08:111][963]LogTemp: Display: UnrealMCPBridge: Executing command: create_mid_game_phase
[2025.08.26-02.56.08:117][963]LogTemp: Criando fase Mid Game
[2025.08.26-02.56.08:117][963]LogTemp: Fase Mid Game criada com sucesso
[2025.08.26-02.56.08:117][963]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"status": "success",
		"message": "Fase Mid Game criada com sucesso",
		"data":
		{
			"phase_config":
			{
				"phase_name": "mid_game",
				"duration_minutes": 15,
				"start_time": 15,
				"end_time": 30,
				"primary_objectives": [
					"Transição para teamfights",
					"Controle de objetivos neutros",
					"Roaming e pressão no mapa",
					"Estabelecer vantagens de itens",
					"Controle de território",
					"Preparação para late game"
				]
			},
			"phase_description": "Fase intermediária com teamfights e objetivos"
		},
		"timestamp": "2025.08.25-23.56.08"
	}
}
[2025.08.26-02.56.08:117][963]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 650
[2025.08.26-02.56.08:117][963]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-02.56.12:344][976]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.56.12:344][976]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.56.12:344][976]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-02.56.12:446][976]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.56.12:446][976]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.56.12:446][976]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_late_game_phase", "params": {"phase_name": "late_game", "start_time": 30, "end_time": "game_end", "primary_objectives": ["Posicionamento em teamfights", "Controle de objetivos \u00e9picos", "Press\u00e3o de split push", "Execu\u00e7\u00e3o de end game", "Controle total do mapa vertical", "Finaliza\u00e7\u00e3o da partida"], "key_mechanics": {"high_stakes_teamfights": {"enabled": true, "focus": "decisive_engagements", "mechanics": ["perfect_positioning_required", "instant_death_potential", "long_death_timers", "game_ending_fights", "ultimate_coordination"]}, "epic_objectives": {"enabled": true, "focus": "baron_and_elder_dragon", "mechanics": ["baron_power_plays", "elder_dragon_executes", "objective_steals", "vision_control_critical", "team_coordination_essential"]}, "end_game_execution": {"enabled": true, "focus": "closing_out_games", "mechanics": ["inhibitor_control", "super_minion_waves", "nexus_rushes", "backdoor_attempts", "base_races"]}}, "milestone_events": {"elder_dragon_spawn": {"typical_time": "35+ minutes", "impact": "execute_threshold_damage"}, "full_item_builds": {"typical_time": "35-40 minutes", "impact": "maximum_champion_power"}, "multiple_inhibitors_down": {"typical_time": "35+ minutes", "impact": "super_minion_pressure"}, "death_timers_60_plus": {"typical_time": "40+ minutes", "impact": "single_fight_game_enders"}}, "win_conditions": {"nexus_destruction": "primary_win_condition", "enemy_surrender": "alternative_win_condition", "typical_methods": ["teamfight_victory_push", "baron_empowered_siege", "split_push_pressure", "backdoor_attempt", "elder_dragon_execute_fight"]}, "enable_high_stakes": true, "enable_elder_dragon": true}}
[2025.08.26-02.56.12:446][976]LogTemp: Display: UnrealMCPBridge: Executing command: create_late_game_phase
[2025.08.26-02.56.12:452][976]LogTemp: Criando fase Late Game
[2025.08.26-02.56.12:452][976]LogTemp: Fase Late Game criada com sucesso
[2025.08.26-02.56.12:452][976]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"status": "success",
		"message": "Fase Late Game criada com sucesso",
		"data":
		{
			"phase_config":
			{
				"phase_name": "late_game",
				"start_time": 30,
				"end_time": "game_end",
				"primary_objectives": [
					"Posicionamento em teamfights",
					"Controle de objetivos épicos",
					"Pressão de split push",
					"Execução de end game",
					"Controle total do mapa vertical",
					"Finalização da partida"
				]
			},
			"phase_description": "Fase final com decisões críticas"
		},
		"timestamp": "2025.08.25-23.56.12"
	}
}
[2025.08.26-02.56.12:452][976]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 613
[2025.08.26-02.56.12:452][976]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-02.56.17:891][993]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.56.17:891][993]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.56.17:891][993]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-02.56.17:992][993]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.56.17:992][993]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.56.17:992][993]LogTemp: Display: MCPServerRunnable: Received: {"type": "setup_complete_game_phases_system", "params": {"system_name": "complete_game_phases_system", "enable_all_phases": true, "enable_dynamic_transitions": true, "phase_transition_system": {"enabled": true, "transition_triggers": ["time_based", "objective_based", "item_completion_based", "team_behavior_based"], "transition_announcements": true, "phase_specific_mechanics": true}}}
[2025.08.26-02.56.17:992][993]LogTemp: Display: UnrealMCPBridge: Executing command: setup_complete_game_phases_system
[2025.08.26-02.56.18:121][993]LogTemp: Configurando sistema completo de fases da partida
[2025.08.26-02.56.18:121][993]LogTemp: Sistema completo de fases da partida configurado com sucesso
[2025.08.26-02.56.18:121][993]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"status": "success",
		"message": "Sistema completo de fases da partida configurado com sucesso",
		"data":
		{
			"system_config":
			{
				"system_name": "complete_game_phases_system",
				"enable_all_phases": true,
				"enable_dynamic_transitions": true
			},
			"system_description": "Sistema completo de fases da partida configurado"
		},
		"timestamp": "2025.08.25-23.56.18"
	}
}
[2025.08.26-02.56.18:121][993]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 444
[2025.08.26-02.56.18:121][993]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-02.56.30:299][ 30]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.56.30:299][ 30]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.56.30:299][ 30]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-02.56.30:400][ 30]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.56.30:400][ 30]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.56.30:400][ 30]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_world_partition_level", "params": {"level_name": "AuracronMainLevel", "layer_type": "Firmamento", "grid_size": 25600, "cell_size": 1600}}
[2025.08.26-02.56.30:400][ 30]LogTemp: Display: UnrealMCPBridge: Executing command: create_world_partition_level
[2025.08.26-02.56.30:459][ 30]LogStreaming: Display: FlushAsyncLoading(480): 1 QueuedPackages, 0 AsyncPackages
[2025.08.26-02.56.30:466][ 30]LogStreaming: Display: Flushing package /Engine/Maps/Templates/HLODs/HLODLayer_Merged (state: WaitingForIo) recursively from another package /Engine/Maps/Templates/HLODs/HLODLayer_Instanced (state: PreloadLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.26-02.56.30:472][ 30]LogStreaming: Display: Package /Engine/Maps/Templates/HLODs/HLODLayer_Instanced is adding a dynamic import to package /Engine/Maps/Templates/HLODs/HLODLayer_Merged because of a recursive sync load
[2025.08.26-02.56.30:472][ 30]LogStreaming: Display: Flushing package /Engine/Maps/Templates/HLODs/HLODLayer_Merged (state: DeferredPostLoad) recursively from another package /Engine/Maps/Templates/HLODs/HLODLayer_Instanced (state: PreloadLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.26-02.56.30:476][ 30]LogChaosDD: Creating Chaos Debug Draw Scene for world AuracronMainLevel
[2025.08.26-02.56.30:479][ 30]LogWorldPartition: ULevel::OnLevelLoaded(AuracronMainLevel)(bIsOwningWorldGameWorld=0, bIsOwningWorldPartitioned=1, InitializeForMainWorld=1, InitializeForEditor=1, InitializeForGame=0)
[2025.08.26-02.56.30:479][ 30]LogWorldPartition: Display: WorldPartition initialize started...
[2025.08.26-02.56.30:479][ 30]LogWorldPartition: UWorldPartition::Initialize : World = /Game/Levels/FIR_AuracronMainLevel.AuracronMainLevel, World Type = Editor, IsMainWorldPartition = 1, Location = V(0), Rotation = R(0), IsEditor = 1, IsGame = 0, IsPIEWorldTravel = 0, IsCooking = 0
[2025.08.26-02.56.30:479][ 30]LogWorldPartition: Display: WorldPartition initialize took 235 us (total: 223.887 ms)
[2025.08.26-02.56.30:480][ 30]LogTemp: WorldPartition successfully created for level: AuracronMainLevel
[2025.08.26-02.56.30:482][ 30]LogTemp: World settings actor label set for World Partition level: AuracronMainLevel
[2025.08.26-02.56.30:490][ 30]LogSavePackage: Moving output files for package: /Game/Levels/FIR_AuracronMainLevel
[2025.08.26-02.56.30:490][ 30]LogSavePackage: Moving '../../../../../../Game/Auracron/Saved/FIR_AuracronMainLevelEB66DF0B43D0AFA9340B2FB6766E0643.tmp' to '../../../../../../Game/Auracron/Content/Levels/FIR_AuracronMainLevel.umap'
[2025.08.26-02.56.30:492][ 30]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"success": true,
		"level_name": "AuracronMainLevel",
		"layer_type": "Firmamento",
		"package_path": "/Game/Levels/FIR_AuracronMainLevel",
		"grid_size": 76800,
		"cell_size": 2000,
		"message": "World Partition level created successfully"
	}
}
[2025.08.26-02.56.30:492][ 30]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 298
[2025.08.26-02.56.30:492][ 30]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-02.56.30:849][ 31]LogContentValidation: Display: Starting to validate 1 assets
[2025.08.26-02.56.30:849][ 31]LogContentValidation: Enabled validators:
[2025.08.26-02.56.30:849][ 31]LogContentValidation: 	/Script/DataValidation.EditorValidator_Material
[2025.08.26-02.56.30:849][ 31]LogContentValidation: 	/Script/DataValidation.DirtyFilesChangelistValidator
[2025.08.26-02.56.30:849][ 31]LogContentValidation: 	/Script/DataValidation.EditorValidator_Localization
[2025.08.26-02.56.30:849][ 31]LogContentValidation: 	/Script/DataValidation.PackageFileValidator
[2025.08.26-02.56.30:849][ 31]LogContentValidation: 	/Script/DataValidation.WorldPartitionChangelistValidator
[2025.08.26-02.56.30:849][ 31]LogContentValidation: 	/Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
[2025.08.26-02.56.30:849][ 31]AssetCheck: /Game/Levels/FIR_AuracronMainLevel Validando ativo
[2025.08.26-02.56.36:025][ 47]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.56.36:025][ 47]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.56.36:025][ 47]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-02.56.36:125][ 48]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.56.36:125][ 48]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.56.36:125][ 48]LogTemp: Display: MCPServerRunnable: Received: {"type": "configure_streaming_cell", "params": {"level_name": "AuracronMainLevel", "cell_x": 0, "cell_y": 0, "layer_type": "Firmamento", "streaming_distance": 5000.0, "priority": 1}}
[2025.08.26-02.56.36:125][ 48]LogTemp: Display: UnrealMCPBridge: Executing command: configure_streaming_cell
[2025.08.26-02.56.36:457][ 48]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "error",
	"error": "World Partition not found in current world"
}
[2025.08.26-02.56.36:457][ 48]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 82
[2025.08.26-02.56.36:457][ 48]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-02.56.41:493][ 64]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.56.41:493][ 64]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.56.41:493][ 64]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-02.56.41:594][ 64]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.56.41:594][ 64]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.56.41:594][ 64]LogTemp: Display: MCPServerRunnable: Received: {"type": "stream_in_layer", "params": {"layer_type": "Firmamento", "streaming_radius": 4000.0, "priority": 1, "force_load": false}}
[2025.08.26-02.56.41:594][ 64]LogTemp: Display: UnrealMCPBridge: Executing command: stream_in_layer
[2025.08.26-02.56.41:790][ 64]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "error",
	"error": "Unknown command: stream_in_layer"
}
[2025.08.26-02.56.41:790][ 64]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 72
[2025.08.26-02.56.41:790][ 64]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-02.56.50:145][ 90]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.56.50:145][ 90]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.56.50:145][ 90]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-02.56.50:246][ 90]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.56.50:246][ 90]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.56.50:246][ 90]LogTemp: Display: MCPServerRunnable: Received: {"type": "stream_in_layer", "params": {"layer_type": "Firmamento", "streaming_radius": 4000, "priority": 1, "force_load": false}}
[2025.08.26-02.56.50:246][ 90]LogTemp: Display: UnrealMCPBridge: Executing command: stream_in_layer
[2025.08.26-02.56.50:459][ 90]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "error",
	"error": "Unknown command: stream_in_layer"
}
[2025.08.26-02.56.50:459][ 90]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 72
[2025.08.26-02.56.50:459][ 90]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-02.56.56:606][109]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.56.56:606][109]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.56.56:606][109]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-02.56.56:706][109]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.56.56:706][109]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.56.56:706][109]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_performance_optimization_system", "params": {"system_id": "auracron_performance", "config": {"enable_multilayer_optimization": true, "target_fps": 60, "memory_budget_mb": 4096, "enable_lod_system": true, "enable_culling": true}}}
[2025.08.26-02.56.56:706][109]LogTemp: Display: UnrealMCPBridge: Executing command: create_performance_optimization_system
[2025.08.26-02.56.56:794][109]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"result": "{\r\n\t\"status\": \"error\",\r\n\t\"message\": \"Invalid performance system configuration\",\r\n\t\"error_code\": \"PERFORMANCE_ERROR\",\r\n\t\"timestamp\": 17271910.607894499\r\n}"
	}
}
[2025.08.26-02.56.56:794][109]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 245
[2025.08.26-02.56.56:794][109]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-02.57.09:578][148]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.57.09:578][148]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.57.09:578][148]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-02.57.09:679][148]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.57.09:679][148]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.57.09:679][148]LogTemp: Display: MCPServerRunnable: Received: {"type": "configure_dynamic_lod", "params": {"system_id": "auracron_performance", "target_objects": ["StaticMesh", "SkeletalMesh"], "distance_thresholds": [500.0, 1000.0, 2000.0, 4000.0], "lod_levels": ["4"], "auto_calculate": true}}
[2025.08.26-02.57.09:679][148]LogTemp: Display: UnrealMCPBridge: Executing command: configure_dynamic_lod
[2025.08.26-02.57.09:797][148]LogJson: Warning: Field lod_config was not found.
[2025.08.26-02.57.09:797][148]LogJson: Warning: Json Value of type 'Null' used as a 'Object'.
[2025.08.26-02.57.09:798][148]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"result": "{\r\n\t\"status\": \"error\",\r\n\t\"message\": \"Invalid LOD configuration\",\r\n\t\"error_code\": \"PERFORMANCE_ERROR\",\r\n\t\"timestamp\": 17271923.611675002\r\n}"
	}
}
[2025.08.26-02.57.09:798][148]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 230
[2025.08.26-02.57.09:798][148]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-02.57.15:439][176]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.57.15:439][176]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.57.15:439][176]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-02.57.15:541][176]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.57.15:541][176]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.57.15:541][176]LogTemp: Display: MCPServerRunnable: Received: {"type": "setup_culling_systems", "params": {"system_id": "auracron_performance", "culling_types": ["distance", "frustum", "occlusion"], "frustum_enabled": true, "occlusion_enabled": true, "distance_enabled": true, "max_distance": 10000.0}}
[2025.08.26-02.57.15:541][176]LogTemp: Display: UnrealMCPBridge: Executing command: setup_culling_systems
[2025.08.26-02.57.15:542][176]LogJson: Warning: Field culling_config was not found.
[2025.08.26-02.57.15:542][176]LogJson: Warning: Json Value of type 'Null' used as a 'Object'.
[2025.08.26-02.57.15:542][176]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"result": "{\r\n\t\"status\": \"error\",\r\n\t\"message\": \"Invalid culling configuration\",\r\n\t\"error_code\": \"PERFORMANCE_ERROR\",\r\n\t\"timestamp\": 17271929.3558934\r\n}"
	}
}
[2025.08.26-02.57.15:542][176]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 232
[2025.08.26-02.57.15:542][176]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-02.57.20:774][305]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.57.20:774][305]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.57.20:774][305]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-02.57.20:874][307]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.57.20:874][307]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.57.20:875][307]LogTemp: Display: MCPServerRunnable: Received: {"type": "setup_auracron_collision_channels", "params": {}}
[2025.08.26-02.57.20:875][307]LogTemp: Display: UnrealMCPBridge: Executing command: setup_auracron_collision_channels
[2025.08.26-02.57.20:875][307]LogTemp: Display: UnrealMCPCollisionCommands: Handling command: setup_auracron_collision_channels
[2025.08.26-02.57.20:875][307]LogTemp: Display: UnrealMCPCollisionCommands: Setting up Auracron collision channels
[2025.08.26-02.57.20:875][307]LogTemp: Display: Created Auracron collision channel: LayerRadiante
[2025.08.26-02.57.20:875][307]LogTemp: Display: Created Auracron collision channel: LayerZephyr
[2025.08.26-02.57.20:875][307]LogTemp: Display: Created Auracron collision channel: LayerUmbral
[2025.08.26-02.57.20:875][307]LogTemp: Display: Created Auracron collision channel: VerticalConnector
[2025.08.26-02.57.20:875][307]LogTemp: Display: Created Auracron collision channel: CrossLayer
[2025.08.26-02.57.20:875][307]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"success": true,
		"channels": [
			{
				"name": "LayerRadiante",
				"default_response": "ECR_Block",
				"trace_type": false
			},
			{
				"name": "LayerZephyr",
				"default_response": "ECR_Block",
				"trace_type": false
			},
			{
				"name": "LayerUmbral",
				"default_response": "ECR_Block",
				"trace_type": false
			},
			{
				"name": "VerticalConnector",
				"default_response": "ECR_Block",
				"trace_type": false
			},
			{
				"name": "CrossLayer",
				"default_response": "ECR_Block",
				"trace_type": false
			}
		],
		"architecture": "Auracron",
		"message": "Auracron collision channels configured successfully"
	}
}
[2025.08.26-02.57.20:875][307]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 716
[2025.08.26-02.57.20:875][307]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-02.57.28:422][518]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.57.28:422][518]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.57.28:422][518]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-02.57.28:523][520]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.57.28:523][520]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.57.28:523][520]LogTemp: Display: MCPServerRunnable: Received: {"type": "setup_layer_collision_interactions", "params": {"enable_cross_layer_collision": true, "vertical_connector_collision": true}}
[2025.08.26-02.57.28:523][520]LogTemp: Display: UnrealMCPBridge: Executing command: setup_layer_collision_interactions
[2025.08.26-02.57.28:524][520]LogTemp: Display: UnrealMCPCollisionCommands: Handling command: setup_layer_collision_interactions
[2025.08.26-02.57.28:524][520]LogTemp: Display: UnrealMCPCollisionCommands: Setting up layer collision interactions
[2025.08.26-02.57.28:524][520]LogTemp: Display: Configured layer collision interactions - CrossLayer: Enabled, VerticalConnector: Enabled
[2025.08.26-02.57.28:524][520]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"success": true,
		"interaction_matrix":
		{
			"LayerRadiante":
			{
				"LayerRadiante": "ECR_Block",
				"LayerZephyr": "ECR_Overlap",
				"LayerUmbral": "ECR_Overlap",
				"VerticalConnector": "ECR_Block",
				"CrossLayer": "ECR_Block"
			},
			"LayerZephyr":
			{
				"LayerRadiante": "ECR_Overlap",
				"LayerZephyr": "ECR_Block",
				"LayerUmbral": "ECR_Overlap",
				"VerticalConnector": "ECR_Block",
				"CrossLayer": "ECR_Block"
			},
			"LayerUmbral":
			{
				"LayerRadiante": "ECR_Overlap",
				"LayerZephyr": "ECR_Overlap",
				"LayerUmbral": "ECR_Block",
				"VerticalConnector": "ECR_Block",
				"CrossLayer": "ECR_Block"
			}
		},
		"cross_layer_collision": true,
		"vertical_connector_collision": true,
		"message": "Layer collision interactions configured successfully"
	}
}
[2025.08.26-02.57.28:524][520]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 870
[2025.08.26-02.57.28:524][520]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-02.57.34:361][618]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.57.34:361][618]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.57.34:361][618]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-02.57.34:462][619]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.57.34:462][619]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.57.34:462][619]LogTemp: Display: MCPServerRunnable: Received: {"type": "configure_lumen_global_illumination", "params": {"enable_lumen": true, "gi_quality": 3, "reflection_quality": 2, "enable_hardware_raytracing": false, "surface_cache_resolution": 256}}
[2025.08.26-02.57.34:462][619]LogTemp: Display: UnrealMCPBridge: Executing command: configure_lumen_global_illumination
[2025.08.26-02.57.34:694][619]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"success": true,
		"message": "Lumen Global Illumination configured successfully",
		"data":
		{
			"enable_lumen": "true",
			"gi_quality": "3",
			"reflection_quality": "2",
			"hardware_raytracing": "false"
		}
	}
}
[2025.08.26-02.57.34:694][619]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 273
[2025.08.26-02.57.34:694][619]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-02.57.40:838][638]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.57.40:838][638]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.57.40:838][638]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-02.57.40:939][638]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.57.40:939][638]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.57.40:939][638]LogTemp: Display: MCPServerRunnable: Received: {"type": "configure_nanite_virtualized_geometry", "params": {"enable_nanite": true, "cluster_per_page": 8, "max_candidate_clusters": 8192, "max_nodes": 1048576, "enable_tessellation": true}}
[2025.08.26-02.57.40:939][638]LogTemp: Display: UnrealMCPBridge: Executing command: configure_nanite_virtualized_geometry
[2025.08.26-02.57.41:030][638]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"success": true,
		"message": "Nanite Virtualized Geometry configured successfully",
		"data":
		{
			"enable_nanite": "true",
			"cluster_per_page": "8",
			"max_candidate_clusters": "8192",
			"max_nodes": "1048576"
		}
	}
}
[2025.08.26-02.57.41:030][638]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 281
[2025.08.26-02.57.41:030][638]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-02.57.52:507][673]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.57.52:507][673]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.57.52:507][673]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-02.57.52:608][673]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.57.52:608][673]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.57.52:608][673]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_multiplayer_session", "params": {"session_name": "AuracronSession", "max_players": 10, "is_lan": false, "is_dedicated": true, "allow_join_in_progress": true, "use_presence": true}}
[2025.08.26-02.57.52:608][673]LogTemp: Display: UnrealMCPBridge: Executing command: create_multiplayer_session
[2025.08.26-02.57.52:699][673]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"success": true,
		"message": "Multiplayer session created successfully",
		"data":
		{
			"session_name": "AuracronSession",
			"max_players": "10",
			"is_lan": "false",
			"is_dedicated": "true"
		}
	}
}
[2025.08.26-02.57.52:699][673]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 261
[2025.08.26-02.57.52:699][673]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-02.57.59:647][694]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.57.59:647][694]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.57.59:647][694]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-02.57.59:747][695]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.57.59:747][695]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.57.59:747][695]LogTemp: Display: MCPServerRunnable: Received: {"type": "setup_actor_replication", "params": {"actor_class_path": "/Game/Blueprints/Characters/AuracronCharacter", "replicate_movement": true, "replicate_properties": ["Health", "Mana", "Level", "Position"], "net_cull_distance": 15000.0, "net_update_frequency": 100.0}}
[2025.08.26-02.57.59:747][695]LogTemp: Display: UnrealMCPBridge: Executing command: setup_actor_replication
[2025.08.26-02.58.00:030][695]LogStreaming: Display: FlushAsyncLoading(483): 1 QueuedPackages, 0 AsyncPackages
[2025.08.26-02.58.00:030][695]LogStreaming: Warning: LoadPackage: SkipPackage: /Game/Blueprints/Characters/AuracronCharacter (0x2B467940BC88DF1B) - The package to load does not exist on disk or in the loader
[2025.08.26-02.58.00:031][695]LogUObjectGlobals: Warning: Falha ao encontrar o objeto 'Class Nenhum./Game/Blueprints/Characters/AuracronCharacter'
[2025.08.26-02.58.00:031][695]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "error",
	"error": ""
}
[2025.08.26-02.58.00:031][695]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 40
[2025.08.26-02.58.00:031][695]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-02.58.06:678][715]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.58.06:678][715]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.58.06:678][715]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-02.58.06:780][716]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.58.06:780][716]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.58.06:780][716]LogTemp: Display: MCPServerRunnable: Received: {"type": "configure_network_settings", "params": {"tick_rate": 60, "max_internet_client_rate": 25000, "max_client_rate": 25000, "enable_network_profiler": false, "network_emulation_settings": {"enable_lag_simulation": false, "packet_loss_percentage": 0}}}
[2025.08.26-02.58.06:780][716]LogTemp: Display: UnrealMCPBridge: Executing command: configure_network_settings
[2025.08.26-02.58.07:031][716]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "error",
	"error": ""
}
[2025.08.26-02.58.07:032][716]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 40
[2025.08.26-02.58.07:032][716]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-02.58.12:265][732]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.58.12:265][732]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.58.12:265][732]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-02.58.12:366][732]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.58.12:366][732]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.58.12:366][732]LogTemp: Display: MCPServerRunnable: Received: {"type": "configure_replication_graph", "params": {"enable_replication_graph": true, "spatial_bias": 1.0, "connection_max_actors_per_frame": 100, "enable_iris_replication": false, "custom_node_classes": ["AuracronReplicationNode"]}}
[2025.08.26-02.58.12:366][732]LogTemp: Display: UnrealMCPBridge: Executing command: configure_replication_graph
[2025.08.26-02.58.12:370][732]LogJson: Warning: Field max_replicated_actors was not found.
[2025.08.26-02.58.12:370][732]LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
[2025.08.26-02.58.12:370][732]LogJson: Warning: Field cull_distance_squared was not found.
[2025.08.26-02.58.12:370][732]LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
[2025.08.26-02.58.12:370][732]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"success": true,
		"message": "Replication graph configured successfully"
	}
}
[2025.08.26-02.58.12:370][733]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 126
[2025.08.26-02.58.12:370][733]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-02.58.19:112][753]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.58.19:112][753]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.58.19:112][753]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-02.58.19:212][753]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.58.19:212][753]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.58.19:212][753]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_multilayer_network_system", "params": {"layer_configs": [{"layer_name": "planicie_radiante", "max_actors": 200, "update_frequency": 30}, {"layer_name": "firmamento_zephyr", "max_actors": 150, "update_frequency": 25}, {"layer_name": "abismo_umbral", "max_actors": 100, "update_frequency": 20}], "global_settings": {"enable_cross_layer_sync": true, "max_total_actors": 450, "compression_enabled": true}}}
[2025.08.26-02.58.19:212][753]LogTemp: Display: UnrealMCPBridge: Executing command: create_multilayer_network_system
[2025.08.26-02.58.19:366][753]LogJson: Warning: Field replication_mode was not found.
[2025.08.26-02.58.19:366][753]LogJson: Warning: Json Value of type 'Null' used as a 'String'.
[2025.08.26-02.58.19:366][753]LogJson: Warning: Field max_players was not found.
[2025.08.26-02.58.19:366][753]LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
[2025.08.26-02.58.19:366][753]LogJson: Warning: Field tick_rate was not found.
[2025.08.26-02.58.19:366][753]LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
[2025.08.26-02.58.19:366][753]LogJson: Warning: Field bandwidth_limit_kbps was not found.
[2025.08.26-02.58.19:366][753]LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
[2025.08.26-02.58.19:366][753]LogJson: Warning: Field compression_enabled was not found.
[2025.08.26-02.58.19:366][753]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.08.26-02.58.19:367][753]LogJson: Warning: Field encryption_enabled was not found.
[2025.08.26-02.58.19:367][753]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.08.26-02.58.19:367][753]LogJson: Warning: Field priority was not found.
[2025.08.26-02.58.19:367][753]LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
[2025.08.26-02.58.19:367][753]LogJson: Warning: Field replication_mode was not found.
[2025.08.26-02.58.19:367][753]LogJson: Warning: Json Value of type 'Null' used as a 'String'.
[2025.08.26-02.58.19:367][753]LogJson: Warning: Field max_players was not found.
[2025.08.26-02.58.19:367][753]LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
[2025.08.26-02.58.19:367][753]LogJson: Warning: Field tick_rate was not found.
[2025.08.26-02.58.19:367][753]LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
[2025.08.26-02.58.19:367][753]LogJson: Warning: Field bandwidth_limit_kbps was not found.
[2025.08.26-02.58.19:367][753]LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
[2025.08.26-02.58.19:367][753]LogJson: Warning: Field compression_enabled was not found.
[2025.08.26-02.58.19:367][753]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.08.26-02.58.19:367][753]LogJson: Warning: Field encryption_enabled was not found.
[2025.08.26-02.58.19:367][753]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.08.26-02.58.19:367][753]LogJson: Warning: Field priority was not found.
[2025.08.26-02.58.19:367][753]LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
[2025.08.26-02.58.19:367][753]LogJson: Warning: Field replication_mode was not found.
[2025.08.26-02.58.19:367][753]LogJson: Warning: Json Value of type 'Null' used as a 'String'.
[2025.08.26-02.58.19:367][753]LogJson: Warning: Field max_players was not found.
[2025.08.26-02.58.19:367][753]LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
[2025.08.26-02.58.19:367][753]LogJson: Warning: Field tick_rate was not found.
[2025.08.26-02.58.19:367][753]LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
[2025.08.26-02.58.19:367][753]LogJson: Warning: Field bandwidth_limit_kbps was not found.
[2025.08.26-02.58.19:367][753]LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
[2025.08.26-02.58.19:367][753]LogJson: Warning: Field compression_enabled was not found.
[2025.08.26-02.58.19:367][753]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.08.26-02.58.19:367][753]LogJson: Warning: Field encryption_enabled was not found.
[2025.08.26-02.58.19:367][753]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.08.26-02.58.19:367][753]LogJson: Warning: Field priority was not found.
[2025.08.26-02.58.19:367][753]LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
[2025.08.26-02.58.19:367][753]LogScript: Warning: Script Msg: No world was found for object (/Engine/Transient.UnrealEdEngine_0) passed in to UEngine::GetWorldFromContextObject().
[2025.08.26-02.58.19:367][753]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"status": "success",
		"message": "Sistema de rede multicamada criado com sucesso",
		"data":
		{
			"system_id": "network_system_1756166299",
			"creation_time": "2025.08.25-23.58.19",
			"layer_count": 3,
			"layers": [
				{
					"layer_name": "planicie_radiante",
					"replication_mode": "",
					"max_players": 0,
					"tick_rate": 0,
					"bandwidth_limit_kbps": 0,
					"compression_enabled": false,
					"encryption_enabled": false,
					"priority": 0
				},
				{
					"layer_name": "firmamento_zephyr",
					"replication_mode": "",
					"max_players": 0,
					"tick_rate": 0,
					"bandwidth_limit_kbps": 0,
					"compression_enabled": false,
					"encryption_enabled": false,
					"priority": 0
				},
				{
					"layer_name": "abismo_umbral",
					"replication_mode": "",
					"max_players": 0,
					"tick_rate": 0,
					"bandwidth_limit_kbps": 0,
					"compression_enabled": false,
					"encryption_enabled": false,
					"priority": 0
				}
			],
			"global_settings":
			{
				"enable_cross_layer_sync": true,
				"max_total_actors": 450,
				"compression_enabled": true
			},
			"total_bandwidth_kbps": 0,
			"real_performance_metrics":
			{
				"success": false,
				"error": "No valid world context",
				"max_concurrent_players": 0,
				"configured_throughput_mbps": 0
			}
		}
	}
}
[2025.08.26-02.58.19:368][753]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 1401
[2025.08.26-02.58.19:368][753]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-02.58.25:304][771]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.58.25:304][771]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.58.25:304][771]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-02.58.25:404][772]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.58.25:404][772]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.58.25:404][772]LogTemp: Display: MCPServerRunnable: Received: {"type": "configure_network_synchronization", "params": {"layer_name": "planicie_radiante", "sync_configs": [{"sync_type": "position", "frequency": 30, "compression": true}, {"sync_type": "health", "frequency": 10, "compression": false}]}}
[2025.08.26-02.58.25:404][772]LogTemp: Display: UnrealMCPBridge: Executing command: configure_network_synchronization
[2025.08.26-02.58.25:702][772]LogJson: Warning: Field authority_role was not found.
[2025.08.26-02.58.25:702][772]LogJson: Warning: Json Value of type 'Null' used as a 'String'.
[2025.08.26-02.58.25:702][772]LogJson: Warning: Field conflict_resolution was not found.
[2025.08.26-02.58.25:702][772]LogJson: Warning: Json Value of type 'Null' used as a 'String'.
[2025.08.26-02.58.25:702][772]LogJson: Warning: Field lag_compensation_enabled was not found.
[2025.08.26-02.58.25:702][772]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.08.26-02.58.25:702][772]LogJson: Warning: Field time_dilation_enabled was not found.
[2025.08.26-02.58.25:702][772]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.08.26-02.58.25:702][772]LogJson: Warning: Field max_desync_tolerance_ms was not found.
[2025.08.26-02.58.25:702][772]LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
[2025.08.26-02.58.25:702][772]LogJson: Warning: Field authority_role was not found.
[2025.08.26-02.58.25:702][772]LogJson: Warning: Json Value of type 'Null' used as a 'String'.
[2025.08.26-02.58.25:702][772]LogJson: Warning: Field conflict_resolution was not found.
[2025.08.26-02.58.25:702][772]LogJson: Warning: Json Value of type 'Null' used as a 'String'.
[2025.08.26-02.58.25:702][772]LogJson: Warning: Field lag_compensation_enabled was not found.
[2025.08.26-02.58.25:702][772]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.08.26-02.58.25:702][772]LogJson: Warning: Field time_dilation_enabled was not found.
[2025.08.26-02.58.25:702][772]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.08.26-02.58.25:702][772]LogJson: Warning: Field max_desync_tolerance_ms was not found.
[2025.08.26-02.58.25:702][772]LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
[2025.08.26-02.58.25:702][772]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"status": "success",
		"message": "Configuração de sincronização aplicada com sucesso",
		"data":
		{
			"layer_name": "planicie_radiante",
			"configuration_time": "2025.08.25-23.58.25",
			"sync_configs": [
				{
					"sync_type": "position",
					"authority_role": "",
					"conflict_resolution": "",
					"lag_compensation_enabled": false,
					"time_dilation_enabled": false,
					"max_desync_tolerance_ms": 0
				},
				{
					"sync_type": "health",
					"authority_role": "",
					"conflict_resolution": "",
					"lag_compensation_enabled": false,
					"time_dilation_enabled": false,
					"max_desync_tolerance_ms": 0
				}
			],
			"total_configs": 2,
			"synchronization_metrics":
			{
				"sync_accuracy_percent": 92.326148986816406,
				"average_sync_time_ms": 35,
				"conflict_resolution_rate": 0.049175694584846497,
				"desync_events_per_minute": 0.38040709495544434
			}
		}
	}
}
[2025.08.26-02.58.25:702][772]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 979
[2025.08.26-02.58.25:702][772]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-02.58.47:136][837]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.58.47:136][837]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.58.47:136][837]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-02.58.47:237][837]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.58.47:237][837]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.58.47:237][837]LogTemp: Display: MCPServerRunnable: Received: {"type": "get_actors_in_level", "params": {}}
[2025.08.26-02.58.47:237][837]LogTemp: Display: UnrealMCPBridge: Executing command: get_actors_in_level
[2025.08.26-02.58.47:420][837]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"actors": [
			{
				"name": "WorldSettings",
				"class": "WorldSettings",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "Brush_0",
				"class": "Brush",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "MassVisualizer_0",
				"class": "MassVisualizer",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "DefaultPhysicsVolume_0",
				"class": "DefaultPhysicsVolume",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "GameplayDebuggerPlayerManager_0",
				"class": "GameplayDebuggerPlayerManager",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "ChaosDebugDrawActor",
				"class": "Actor",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "SmartObjectSubsystemRenderingActor_0",
				"class": "SmartObjectSubsystemRenderingActor",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "AbstractNavData-Default",
				"class": "AbstractNavData",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			}
		],
		"total_actors": 8,
		"returned_actors": 8,
		"limited": false
	}
}
[2025.08.26-02.58.47:420][837]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 1509
[2025.08.26-02.58.47:420][837]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-02.58.52:853][860]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.58.52:853][860]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.58.52:853][860]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-02.58.52:955][862]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.58.52:955][862]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.58.52:955][862]LogTemp: Display: MCPServerRunnable: Received: {"type": "get_collision_system_status", "params": {}}
[2025.08.26-02.58.52:955][862]LogTemp: Display: UnrealMCPBridge: Executing command: get_collision_system_status
[2025.08.26-02.58.52:956][862]LogTemp: Display: UnrealMCPCollisionCommands: Handling command: get_collision_system_status
[2025.08.26-02.58.52:956][862]LogTemp: Display: Retrieved collision system status
[2025.08.26-02.58.52:956][862]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"success": true,
		"system_status": "active",
		"active_collision_channels": 8,
		"custom_profiles": 0,
		"active_layers": 0,
		"optimization_enabled": true
	}
}
[2025.08.26-02.58.52:956][862]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 213
[2025.08.26-02.58.52:956][862]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-02.58.57:485][968]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.58.57:485][968]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.58.57:485][968]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-02.58.57:585][970]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.58.57:585][970]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.58.57:585][970]LogTemp: Display: MCPServerRunnable: Received: {"type": "get_pathfinding_system_status", "params": {"include_performance_metrics": true}}
[2025.08.26-02.58.57:585][970]LogTemp: Display: UnrealMCPBridge: Executing command: get_pathfinding_system_status
[2025.08.26-02.58.57:587][970]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"status": "error",
		"message": "Mundo não disponível para obter status do sistema"
	}
}
[2025.08.26-02.58.57:587][970]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 138
[2025.08.26-02.58.57:587][970]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-02.59.02:011][ 57]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.59.02:012][ 57]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.59.02:012][ 57]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-02.59.02:112][ 59]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.59.02:112][ 59]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.59.02:112][ 59]LogTemp: Display: MCPServerRunnable: Received: {"type": "get_vision_system_status", "params": {"include_performance_metrics": false}}
[2025.08.26-02.59.02:112][ 59]LogTemp: Display: UnrealMCPBridge: Executing command: get_vision_system_status
[2025.08.26-02.59.02:118][ 59]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"status": "success",
		"message": "Vision system status retrieved successfully",
		"data":
		{
			"fog_layers_count": 0,
			"vision_sensors_count": 0,
			"blocking_volumes_count": 0,
			"debug_enabled": false,
			"system_status": "operational"
		}
	}
}
[2025.08.26-02.59.02:119][ 59]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 308
[2025.08.26-02.59.02:119][ 59]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-02.59.07:955][177]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.59.07:956][177]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.59.07:956][177]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-02.59.08:057][179]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.59.08:057][179]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.59.08:057][179]LogTemp: Display: MCPServerRunnable: Received: {"type": "take_screenshot", "params": {"filepath": "Screenshots/AuracronImplementationComplete"}}
[2025.08.26-02.59.08:057][179]LogTemp: Display: UnrealMCPBridge: Executing command: take_screenshot
[2025.08.26-02.59.08:058][179]LogRendererCore: Warning: FlushRenderingCommands called recursively! 2 calls on the stack.
[2025.08.26-02.59.08:352][179]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"filepath": "Screenshots/AuracronImplementationComplete.png"
	}
}
[2025.08.26-02.59.08:353][180]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 112
[2025.08.26-02.59.08:353][180]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-02.59.14:589][306]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.59.14:589][306]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.59.14:589][306]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-02.59.14:690][307]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.59.14:690][307]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.59.14:690][307]LogTemp: Display: MCPServerRunnable: Received: {"type": "spawn_actor", "params": {"name": "TestActor", "type": "STATICMESHACTOR", "location": [0.0, 0.0, 100.0], "rotation": [0.0, 0.0, 0.0]}}
[2025.08.26-02.59.14:690][307]LogTemp: Display: UnrealMCPBridge: Executing command: spawn_actor
[2025.08.26-02.59.14:691][307]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"name": "TestActor",
		"class": "StaticMeshActor",
		"location": [ 0, 0, 100 ],
		"rotation": [ 0, 0, 0 ],
		"scale": [ 1, 1, 1 ]
	}
}
[2025.08.26-02.59.14:691][307]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 185
[2025.08.26-02.59.14:691][307]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-02.59.19:919][410]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.59.19:919][411]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.59.19:919][411]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-02.59.20:021][412]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-02.59.20:022][412]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-02.59.20:022][412]LogTemp: Display: MCPServerRunnable: Received: {"type": "delete_actor", "params": {"name": "TestActor"}}
[2025.08.26-02.59.20:022][412]LogTemp: Display: UnrealMCPBridge: Executing command: delete_actor
[2025.08.26-02.59.20:023][412]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"deleted_actor":
		{
			"name": "TestActor",
			"class": "StaticMeshActor",
			"location": [ 0, 0, 100 ],
			"rotation": [ 0, 0, 0 ],
			"scale": [ 1, 1, 1 ]
		}
	}
}
[2025.08.26-02.59.20:023][412]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 220
[2025.08.26-02.59.20:023][412]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-03.00.31:722][117]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-03.00.31:722][117]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-03.00.31:722][117]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-03.00.31:823][117]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-03.00.31:823][117]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-03.00.31:823][117]LogTemp: Display: MCPServerRunnable: Received: {"type": "spawn_actor", "params": {"name": "PlanicieRadianteFloor", "type": "STATICMESHACTOR", "location": [0.0, 0.0, 0.0], "rotation": [0.0, 0.0, 0.0]}}
[2025.08.26-03.00.31:823][117]LogTemp: Display: UnrealMCPBridge: Executing command: spawn_actor
[2025.08.26-03.00.31:846][117]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"name": "PlanicieRadianteFloor",
		"class": "StaticMeshActor",
		"location": [ 0, 0, 0 ],
		"rotation": [ 0, 0, 0 ],
		"scale": [ 1, 1, 1 ]
	}
}
[2025.08.26-03.00.31:846][117]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 195
[2025.08.26-03.00.31:847][117]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-03.00.40:502][158]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-03.00.40:502][158]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-03.00.40:502][158]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-03.00.40:603][160]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-03.00.40:603][160]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-03.00.40:603][160]LogTemp: Display: MCPServerRunnable: Received: {"type": "spawn_actor", "params": {"name": "FirmamentoZephyrPlatform", "type": "STATICMESHACTOR", "location": [0.0, 0.0, 3000.0], "rotation": [0.0, 0.0, 0.0]}}
[2025.08.26-03.00.40:603][160]LogTemp: Display: UnrealMCPBridge: Executing command: spawn_actor
[2025.08.26-03.00.40:604][160]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"name": "FirmamentoZephyrPlatform",
		"class": "StaticMeshActor",
		"location": [ 0, 0, 3000 ],
		"rotation": [ 0, 0, 0 ],
		"scale": [ 1, 1, 1 ]
	}
}
[2025.08.26-03.00.40:604][160]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 201
[2025.08.26-03.00.40:604][160]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-03.00.46:634][305]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-03.00.46:634][305]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-03.00.46:634][305]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-03.00.46:736][308]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-03.00.46:736][308]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-03.00.46:736][308]LogTemp: Display: MCPServerRunnable: Received: {"type": "spawn_actor", "params": {"name": "AbismoUmbralCeiling", "type": "STATICMESHACTOR", "location": [0.0, 0.0, 5000.0], "rotation": [0.0, 0.0, 0.0]}}
[2025.08.26-03.00.46:736][308]LogTemp: Display: UnrealMCPBridge: Executing command: spawn_actor
[2025.08.26-03.00.46:736][308]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"name": "AbismoUmbralCeiling",
		"class": "StaticMeshActor",
		"location": [ 0, 0, 5000 ],
		"rotation": [ 0, 0, 0 ],
		"scale": [ 1, 1, 1 ]
	}
}
[2025.08.26-03.00.46:736][308]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 196
[2025.08.26-03.00.46:736][308]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-03.00.54:377][349]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-03.00.54:377][349]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-03.00.54:377][349]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-03.00.54:479][349]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-03.00.54:479][349]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-03.00.54:479][349]LogTemp: Display: MCPServerRunnable: Received: {"type": "spawn_actor", "params": {"name": "MainDirectionalLight", "type": "DIRECTIONALLIGHT", "location": [0.0, 0.0, 1000.0], "rotation": [-45.0, 0.0, 0.0]}}
[2025.08.26-03.00.54:479][349]LogTemp: Display: UnrealMCPBridge: Executing command: spawn_actor
[2025.08.26-03.00.54:676][349]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"name": "MainDirectionalLight",
		"class": "DirectionalLight",
		"location": [ 0, 0, 1000 ],
		"rotation": [ -88.999999999997925, 180, 180 ],
		"scale": [ 1, 1, 1 ]
	}
}
[2025.08.26-03.00.54:676][349]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 220
[2025.08.26-03.00.54:677][349]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-03.01.00:107][408]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-03.01.00:107][408]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-03.01.00:107][408]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-03.01.00:208][411]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-03.01.00:208][411]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-03.01.00:208][411]LogTemp: Display: MCPServerRunnable: Received: {"type": "spawn_actor", "params": {"name": "SkyLight", "type": "SKYLIGHT", "location": [0.0, 0.0, 2000.0], "rotation": [0.0, 0.0, 0.0]}}
[2025.08.26-03.01.00:208][411]LogTemp: Display: UnrealMCPBridge: Executing command: spawn_actor
[2025.08.26-03.01.00:209][412]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "error",
	"error": "Unknown actor type: SKYLIGHT"
}
[2025.08.26-03.01.00:209][412]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 68
[2025.08.26-03.01.00:209][412]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-03.01.08:558][548]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-03.01.08:558][548]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-03.01.08:558][548]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-03.01.08:659][548]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-03.01.08:659][548]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-03.01.08:659][548]LogTemp: Display: MCPServerRunnable: Received: {"type": "spawn_actor", "params": {"name": "AmbientSkyLight", "type": "SKYLIGHTACTOR", "location": [0.0, 0.0, 2000.0], "rotation": [0.0, 0.0, 0.0]}}
[2025.08.26-03.01.08:659][548]LogTemp: Display: UnrealMCPBridge: Executing command: spawn_actor
[2025.08.26-03.01.08:666][548]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "error",
	"error": "Unknown actor type: SKYLIGHTACTOR"
}
[2025.08.26-03.01.08:666][548]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 73
[2025.08.26-03.01.08:666][548]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-03.01.14:099][565]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-03.01.14:099][565]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-03.01.14:099][565]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-03.01.14:199][565]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-03.01.14:199][565]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-03.01.14:199][565]LogTemp: Display: MCPServerRunnable: Received: {"type": "spawn_actor", "params": {"name": "PlanicieLight", "type": "POINTLIGHT", "location": [500.0, 500.0, 500.0], "rotation": [0.0, 0.0, 0.0]}}
[2025.08.26-03.01.14:199][565]LogTemp: Display: UnrealMCPBridge: Executing command: spawn_actor
[2025.08.26-03.01.14:333][565]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"name": "PlanicieLight",
		"class": "PointLight",
		"location": [ 500, 500, 500 ],
		"rotation": [ 0, 0, 0 ],
		"scale": [ 1, 1, 1 ]
	}
}
[2025.08.26-03.01.14:333][565]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 188
[2025.08.26-03.01.14:333][565]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-03.01.19:765][661]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-03.01.19:766][661]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-03.01.19:766][661]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-03.01.19:867][663]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-03.01.19:867][663]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-03.01.19:867][663]LogTemp: Display: MCPServerRunnable: Received: {"type": "spawn_actor", "params": {"name": "FirmamentoLight", "type": "POINTLIGHT", "location": [500.0, 500.0, 3500.0], "rotation": [0.0, 0.0, 0.0]}}
[2025.08.26-03.01.19:867][663]LogTemp: Display: UnrealMCPBridge: Executing command: spawn_actor
[2025.08.26-03.01.19:869][663]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"name": "FirmamentoLight",
		"class": "PointLight",
		"location": [ 500, 500, 3500 ],
		"rotation": [ 0, 0, 0 ],
		"scale": [ 1, 1, 1 ]
	}
}
[2025.08.26-03.01.19:869][663]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 191
[2025.08.26-03.01.19:869][663]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-03.01.25:606][742]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-03.01.25:606][742]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-03.01.25:606][742]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-03.01.25:707][743]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-03.01.25:707][743]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-03.01.25:707][743]LogTemp: Display: MCPServerRunnable: Received: {"type": "spawn_actor", "params": {"name": "AbismoLight", "type": "POINTLIGHT", "location": [500.0, 500.0, 5500.0], "rotation": [0.0, 0.0, 0.0]}}
[2025.08.26-03.01.25:707][743]LogTemp: Display: UnrealMCPBridge: Executing command: spawn_actor
[2025.08.26-03.01.25:982][743]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"name": "AbismoLight",
		"class": "PointLight",
		"location": [ 500, 500, 5500 ],
		"rotation": [ 0, 0, 0 ],
		"scale": [ 1, 1, 1 ]
	}
}
[2025.08.26-03.01.25:982][743]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 187
[2025.08.26-03.01.25:982][743]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-03.01.32:018][808]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-03.01.32:018][808]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-03.01.32:019][808]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-03.01.32:119][810]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-03.01.32:119][810]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-03.01.32:119][810]LogTemp: Display: MCPServerRunnable: Received: {"type": "focus_viewport", "params": {"location": [1000.0, 1000.0, 2500.0], "distance": 2000.0, "orientation": [-30.0, -45.0, 0.0]}}
[2025.08.26-03.01.32:119][810]LogTemp: Display: UnrealMCPBridge: Executing command: focus_viewport
[2025.08.26-03.01.32:120][810]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"success": true
	}
}
[2025.08.26-03.01.32:120][810]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 67
[2025.08.26-03.01.32:120][810]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-03.01.38:967][948]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-03.01.38:967][948]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-03.01.38:967][948]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-03.01.39:067][950]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-03.01.39:068][950]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-03.01.39:068][950]LogTemp: Display: MCPServerRunnable: Received: {"type": "spawn_actor", "params": {"name": "PlanicieMarker", "type": "STATICMESHACTOR", "location": [0.0, 0.0, 100.0], "rotation": [0.0, 0.0, 0.0]}}
[2025.08.26-03.01.39:068][950]LogTemp: Display: UnrealMCPBridge: Executing command: spawn_actor
[2025.08.26-03.01.39:074][950]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"name": "PlanicieMarker",
		"class": "StaticMeshActor",
		"location": [ 0, 0, 100 ],
		"rotation": [ 0, 0, 0 ],
		"scale": [ 1, 1, 1 ]
	}
}
[2025.08.26-03.01.39:074][950]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 190
[2025.08.26-03.01.39:074][950]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-03.01.46:519][ 96]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-03.01.46:519][ 96]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-03.01.46:519][ 96]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-03.01.46:620][ 97]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-03.01.46:620][ 97]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-03.01.46:621][ 97]LogTemp: Display: MCPServerRunnable: Received: {"type": "get_actor_properties", "params": {"name": "PlanicieMarker"}}
[2025.08.26-03.01.46:621][ 97]LogTemp: Display: UnrealMCPBridge: Executing command: get_actor_properties
[2025.08.26-03.01.46:621][ 97]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"name": "PlanicieMarker",
		"class": "StaticMeshActor",
		"location": [ 0, 0, 100 ],
		"rotation": [ 0, 0, 0 ],
		"scale": [ 1, 1, 1 ]
	}
}
[2025.08.26-03.01.46:621][ 97]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 190
[2025.08.26-03.01.46:621][ 97]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-03.01.52:863][130]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-03.01.52:863][130]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-03.01.52:863][130]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-03.01.52:963][131]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-03.01.52:963][131]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-03.01.52:963][131]LogTemp: Display: MCPServerRunnable: Received: {"type": "set_actor_property", "params": {"name": "PlanicieMarker", "property_name": "StaticMesh", "property_value": "/Engine/BasicShapes/Cube"}}
[2025.08.26-03.01.52:963][131]LogTemp: Display: UnrealMCPBridge: Executing command: set_actor_property
[2025.08.26-03.01.53:192][131]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "error",
	"error": "Property not found: StaticMesh"
}
[2025.08.26-03.01.53:192][131]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 70
[2025.08.26-03.01.53:192][131]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-03.01.58:832][148]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-03.01.58:832][148]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-03.01.58:832][148]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-03.01.58:933][149]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-03.01.58:933][149]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-03.01.58:933][149]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_blueprint", "params": {"name": "BP_LayerMarker", "parent_class": "Actor"}}
[2025.08.26-03.01.58:933][149]LogTemp: Display: UnrealMCPBridge: Executing command: create_blueprint
[2025.08.26-03.01.59:194][149]LogTemp: Successfully set parent class to 'Actor'
[2025.08.26-03.01.59:199][149]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"name": "BP_LayerMarker",
		"path": "/Game/Blueprints/BP_LayerMarker"
	}
}
[2025.08.26-03.01.59:199][149]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 122
[2025.08.26-03.01.59:199][149]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-03.03.36:507][986]LogUObjectHash: Compacting FUObjectHashTables data took   0.60ms
[2025.08.26-03.03.36:512][986]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/AURACRON" FILE="../../../../../../Game/Auracron/Saved/Autosaves/Game/AURACRON_Auto2.umap" SILENT=true AUTOSAVING=true KEEPDIRTY=false
[2025.08.26-03.03.36:522][986]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/AURACRON_Auto2
[2025.08.26-03.03.36:522][986]LogSavePackage: Moving '../../../../../../Game/Auracron/Saved/AURACRON_Auto2FA694AE14EFBC24F6657A4B35ACB02C4.tmp' to '../../../../../../Game/Auracron/Saved/Autosaves/Game/AURACRON_Auto2.umap'
[2025.08.26-03.03.36:523][986]LogFileHelpers: Editor autosave (incl. external actors) for '/Game/AURACRON' took 0.050
[2025.08.26-03.03.36:523][986]LogFileHelpers: Editor autosave (incl. sublevels & external actors) for all levels took 0.050
[2025.08.26-03.03.36:523][986]OBJ SavePackage: Generating thumbnails for [0] asset(s) in package [/Game/Blueprints/BP_LayerMarker] ([2] browsable assets)...
[2025.08.26-03.03.36:523][986]OBJ SavePackage: Finished generating thumbnails for package [/Game/Blueprints/BP_LayerMarker]
[2025.08.26-03.03.36:523][986]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Blueprints/BP_LayerMarker" FILE="../../../../../../Game/Auracron/Saved/Autosaves/Game/Blueprints/BP_LayerMarker_Auto2.uasset" SILENT=false AUTOSAVING=true
[2025.08.26-03.03.36:531][986]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/Blueprints/BP_LayerMarker_Auto2
[2025.08.26-03.03.36:531][986]LogSavePackage: Moving '../../../../../../Game/Auracron/Saved/BP_LayerMarker_Auto205A90CB846B350D2855E4899838760C5.tmp' to '../../../../../../Game/Auracron/Saved/Autosaves/Game/Blueprints/BP_LayerMarker_Auto2.uasset'
[2025.08.26-03.03.36:532][986]LogFileHelpers: Auto-saving content packages took 0.010
[2025.08.26-03.03.45:975][ 15]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-03.03.45:975][ 15]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-03.03.45:975][ 15]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-03.03.46:076][ 15]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-03.03.46:076][ 15]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-03.03.46:076][ 15]LogTemp: Display: MCPServerRunnable: Received: {"type": "add_component_to_blueprint", "params": {"blueprint_name": "BP_LayerMarker", "component_type": "StaticMeshComponent", "component_name": "MeshComponent", "location": [0.0, 0.0, 0.0], "rotation": [0.0, 0.0, 0.0], "scale": [2.0, 2.0, 2.0]}}
[2025.08.26-03.03.46:076][ 15]LogTemp: Display: UnrealMCPBridge: Executing command: add_component_to_blueprint
[2025.08.26-03.03.46:077][ 15]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "error",
	"error": "Unknown component type: StaticMeshComponent"
}
[2025.08.26-03.03.46:077][ 15]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 83
[2025.08.26-03.03.46:077][ 15]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-03.03.52:319][ 34]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-03.03.52:319][ 34]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-03.03.52:319][ 34]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-03.03.52:420][ 35]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-03.03.52:420][ 35]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-03.03.52:420][ 35]LogTemp: Display: MCPServerRunnable: Received: {"type": "add_component_to_blueprint", "params": {"blueprint_name": "BP_LayerMarker", "component_type": "UStaticMeshComponent", "component_name": "MeshComponent", "location": [0.0, 0.0, 0.0], "rotation": [0.0, 0.0, 0.0], "scale": [2.0, 2.0, 2.0]}}
[2025.08.26-03.03.52:420][ 35]LogTemp: Display: UnrealMCPBridge: Executing command: add_component_to_blueprint
[2025.08.26-03.03.52:746][ 35]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "error",
	"error": "Unknown component type: UStaticMeshComponent"
}
[2025.08.26-03.03.52:747][ 35]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 84
[2025.08.26-03.03.52:747][ 35]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-03.03.59:292][ 55]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-03.03.59:292][ 55]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-03.03.59:292][ 55]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-03.03.59:393][ 55]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-03.03.59:393][ 55]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-03.03.59:393][ 55]LogTemp: Display: MCPServerRunnable: Received: {"type": "spawn_actor", "params": {"name": "BrightLight1", "type": "POINTLIGHT", "location": [0.0, 0.0, 1000.0], "rotation": [0.0, 0.0, 0.0]}}
[2025.08.26-03.03.59:393][ 55]LogTemp: Display: UnrealMCPBridge: Executing command: spawn_actor
[2025.08.26-03.03.59:413][ 55]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"name": "BrightLight1",
		"class": "PointLight",
		"location": [ 0, 0, 1000 ],
		"rotation": [ 0, 0, 0 ],
		"scale": [ 1, 1, 1 ]
	}
}
[2025.08.26-03.03.59:413][ 55]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 184
[2025.08.26-03.03.59:414][ 55]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-03.04.06:462][ 77]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-03.04.06:462][ 77]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-03.04.06:462][ 77]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-03.04.06:563][ 77]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-03.04.06:564][ 77]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-03.04.06:564][ 77]LogTemp: Display: MCPServerRunnable: Received: {"type": "set_actor_property", "params": {"name": "BrightLight1", "property_name": "Intensity", "property_value": "10000"}}
[2025.08.26-03.04.06:564][ 77]LogTemp: Display: UnrealMCPBridge: Executing command: set_actor_property
[2025.08.26-03.04.06:748][ 77]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "error",
	"error": "Property not found: Intensity"
}
[2025.08.26-03.04.06:748][ 77]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 69
[2025.08.26-03.04.06:748][ 77]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-03.04.12:284][ 94]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-03.04.12:284][ 94]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-03.04.12:284][ 94]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-03.04.12:384][ 94]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-03.04.12:384][ 94]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-03.04.12:384][ 94]LogTemp: Display: MCPServerRunnable: Received: {"type": "get_actors_in_level", "params": {}}
[2025.08.26-03.04.12:384][ 94]LogTemp: Display: UnrealMCPBridge: Executing command: get_actors_in_level
[2025.08.26-03.04.12:416][ 94]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"actors": [
			{
				"name": "WorldSettings",
				"class": "WorldSettings",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "Brush_0",
				"class": "Brush",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "MassVisualizer_0",
				"class": "MassVisualizer",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "DefaultPhysicsVolume_0",
				"class": "DefaultPhysicsVolume",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "GameplayDebuggerPlayerManager_0",
				"class": "GameplayDebuggerPlayerManager",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "ChaosDebugDrawActor",
				"class": "Actor",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "SmartObjectSubsystemRenderingActor_0",
				"class": "SmartObjectSubsystemRenderingActor",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "AbstractNavData-Default",
				"class": "AbstractNavData",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "PlanicieRadianteFloor",
				"class": "StaticMeshActor",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "FirmamentoZephyrPlatform",
				"class": "StaticMeshActor",
				"location": [ 0, 0, 3000 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "AbismoUmbralCeiling",
				"class": "StaticMeshActor",
				"location": [ 0, 0, 5000 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "MainDirectionalLight",
				"class": "DirectionalLight",
				"location": [ 0, 0, 1000 ],
				"rotation": [ -89, 180, 180 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "PlanicieLight",
				"class": "PointLight",
				"location": [ 500, 500, 500 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "FirmamentoLight",
				"class": "PointLight",
				"location": [ 500, 500, 3500 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "AbismoLight",
				"class": "PointLight",
				"location": [ 500, 500, 5500 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "PlanicieMarker",
				"class": "StaticMeshActor",
				"location": [ 0, 0, 100 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "BrightLight1",
				"class": "PointLight",
				"location": [ 0, 0, 1000 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			}
		],
		"total_actors": 17,
		"returned_actors": 17,
		"limited": false
	}
}
[2025.08.26-03.04.12:416][ 94]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 3022
[2025.08.26-03.04.12:416][ 94]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-03.05.50:383][460]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-03.05.50:383][460]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-03.05.50:383][460]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-03.05.50:484][461]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-03.05.50:484][461]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-03.05.50:484][461]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_blueprint", "params": {"name": "BP_AuracronGameMode", "parent_class": "GameModeBase"}}
[2025.08.26-03.05.50:484][461]LogTemp: Display: UnrealMCPBridge: Executing command: create_blueprint
[2025.08.26-03.05.50:804][461]LogUObjectGlobals: Warning: Falha ao encontrar o objeto 'Class /Script/Engine.AGameModeBase'
[2025.08.26-03.05.50:804][461]LogUObjectGlobals: Warning: Falha ao encontrar o objeto 'Class /Script/Game.AGameModeBase'
[2025.08.26-03.05.50:804][461]LogTemp: Warning: Could not find specified parent class 'AGameModeBase' at paths: /Script/Engine.AGameModeBase or /Script/Game.AGameModeBase, defaulting to AActor
[2025.08.26-03.05.50:809][461]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"name": "BP_AuracronGameMode",
		"path": "/Game/Blueprints/BP_AuracronGameMode"
	}
}
[2025.08.26-03.05.50:809][461]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 132
[2025.08.26-03.05.50:809][461]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-03.05.56:045][477]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-03.05.56:045][477]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-03.05.56:045][477]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-03.05.56:145][478]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-03.05.56:145][478]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-03.05.56:145][478]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_blueprint", "params": {"name": "BP_MultilayerManager", "parent_class": "Actor"}}
[2025.08.26-03.05.56:145][478]LogTemp: Display: UnrealMCPBridge: Executing command: create_blueprint
[2025.08.26-03.05.56:472][478]LogTemp: Successfully set parent class to 'Actor'
[2025.08.26-03.05.56:475][478]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"name": "BP_MultilayerManager",
		"path": "/Game/Blueprints/BP_MultilayerManager"
	}
}
[2025.08.26-03.05.56:475][478]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 134
[2025.08.26-03.05.56:475][478]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-03.06.02:714][497]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-03.06.02:714][497]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-03.06.02:714][497]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-03.06.02:815][498]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-03.06.02:815][498]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-03.06.02:815][498]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_blueprint", "params": {"name": "BP_PlanicieRadianteLayer", "parent_class": "Actor"}}
[2025.08.26-03.06.02:815][498]LogTemp: Display: UnrealMCPBridge: Executing command: create_blueprint
[2025.08.26-03.06.03:138][498]LogTemp: Successfully set parent class to 'Actor'
[2025.08.26-03.06.03:141][498]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"name": "BP_PlanicieRadianteLayer",
		"path": "/Game/Blueprints/BP_PlanicieRadianteLayer"
	}
}
[2025.08.26-03.06.03:141][498]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 142
[2025.08.26-03.06.03:141][498]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-03.06.08:875][516]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-03.06.08:875][516]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-03.06.08:875][516]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-03.06.08:976][516]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-03.06.08:976][516]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-03.06.08:976][516]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_blueprint", "params": {"name": "BP_FirmamentoZephyrLayer", "parent_class": "Actor"}}
[2025.08.26-03.06.08:976][516]LogTemp: Display: UnrealMCPBridge: Executing command: create_blueprint
[2025.08.26-03.06.09:138][516]LogTemp: Successfully set parent class to 'Actor'
[2025.08.26-03.06.09:142][516]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"name": "BP_FirmamentoZephyrLayer",
		"path": "/Game/Blueprints/BP_FirmamentoZephyrLayer"
	}
}
[2025.08.26-03.06.09:143][516]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 142
[2025.08.26-03.06.09:143][516]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-03.06.14:278][532]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-03.06.14:278][532]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-03.06.14:278][532]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-03.06.14:380][532]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-03.06.14:380][532]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-03.06.14:380][532]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_blueprint", "params": {"name": "BP_AbismoUmbralLayer", "parent_class": "Actor"}}
[2025.08.26-03.06.14:380][532]LogTemp: Display: UnrealMCPBridge: Executing command: create_blueprint
[2025.08.26-03.06.14:473][532]LogTemp: Successfully set parent class to 'Actor'
[2025.08.26-03.06.14:477][532]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"name": "BP_AbismoUmbralLayer",
		"path": "/Game/Blueprints/BP_AbismoUmbralLayer"
	}
}
[2025.08.26-03.06.14:477][532]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 134
[2025.08.26-03.06.14:477][532]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-03.06.21:222][553]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-03.06.21:222][553]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-03.06.21:222][553]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-03.06.21:323][553]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-03.06.21:323][553]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-03.06.21:323][553]LogTemp: Display: MCPServerRunnable: Received: {"type": "add_component_to_blueprint", "params": {"blueprint_name": "BP_PlanicieRadianteLayer", "component_type": "StaticMesh", "component_name": "FloorMesh", "location": [0.0, 0.0, 0.0], "rotation": [0.0, 0.0, 0.0], "scale": [50.0, 50.0, 1.0]}}
[2025.08.26-03.06.21:323][553]LogTemp: Display: UnrealMCPBridge: Executing command: add_component_to_blueprint
[2025.08.26-03.06.21:473][553]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "error",
	"error": "Unknown component type: StaticMesh"
}
[2025.08.26-03.06.21:473][553]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 74
[2025.08.26-03.06.21:473][553]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-03.06.40:690][692]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-03.06.40:690][692]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-03.06.40:690][692]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-03.06.40:791][692]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-03.06.40:791][692]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-03.06.40:791][692]LogTemp: Display: MCPServerRunnable: Received: {"type": "add_component_to_blueprint", "params": {"blueprint_name": "BP_PlanicieRadianteLayer", "component_type": "StaticMeshComponent", "component_name": "FloorMesh", "location": [0.0, 0.0, 0.0], "rotation": [0.0, 0.0, 0.0], "scale": [50.0, 50.0, 1.0]}}
[2025.08.26-03.06.40:791][692]LogTemp: Display: UnrealMCPBridge: Executing command: add_component_to_blueprint
[2025.08.26-03.06.40:878][692]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "error",
	"error": "Unknown component type: StaticMeshComponent"
}
[2025.08.26-03.06.40:878][692]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 83
[2025.08.26-03.06.40:878][692]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-03.06.53:556][731]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-03.06.53:556][731]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-03.06.53:556][731]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-03.06.53:657][731]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-03.06.53:657][731]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-03.06.53:657][731]LogTemp: Display: MCPServerRunnable: Received: {"type": "add_component_to_blueprint", "params": {"blueprint_name": "BP_PlanicieRadianteLayer", "component_type": "SceneComponent", "component_name": "RootComponent", "location": [0.0, 0.0, 0.0], "rotation": [0.0, 0.0, 0.0], "scale": [1.0, 1.0, 1.0]}}
[2025.08.26-03.06.53:657][731]LogTemp: Display: UnrealMCPBridge: Executing command: add_component_to_blueprint
[2025.08.26-03.06.53:880][731]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "error",
	"error": "Unknown component type: SceneComponent"
}
[2025.08.26-03.06.53:880][731]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 78
[2025.08.26-03.06.53:880][731]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-03.07.48:891][896]LogDirectoryWatcher: Display: Refresh of ReadDirectoryChangesW failed. GetLastError code [5] Handle [0000000000001080], Path [C:/Game/Auracron/Binaries/Win64]. Aborting watch request...
[2025.08.26-03.07.55:580][917]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-03.07.55:580][917]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-03.07.55:580][917]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-03.07.55:681][917]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-03.07.55:681][917]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-03.07.55:681][917]LogTemp: Display: MCPServerRunnable: Received: {"type": "spawn_blueprint_actor", "params": {"blueprint_name": "BP_AuracronGameMode", "actor_name": "AuracronGameModeInstance", "location": [0.0, 0.0, 0.0], "rotation": [0.0, 0.0, 0.0]}}
[2025.08.26-03.07.55:681][917]LogTemp: Display: UnrealMCPBridge: Executing command: spawn_blueprint_actor
[2025.08.26-03.07.55:891][917]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "error",
	"error": "Blueprint 'BP_AuracronGameMode' not found – it must reside under /Game/Blueprints"
}
[2025.08.26-03.07.55:891][917]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 123
[2025.08.26-03.07.55:891][917]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-03.07.57:162][921]LogUObjectHash: Compacting FUObjectHashTables data took   0.57ms
[2025.08.26-03.08.01:575][921]LogSlate: Window 'Salvar conteúdo' being destroyed
[2025.08.26-03.08.01:595][921]LogFileHelpers: InternalPromptForCheckoutAndSave started...
[2025.08.26-03.08.01:644][921]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/AURACRON" FILE="../../../../../../Game/Auracron/Content/AURACRON.umap" SILENT=true AUTOSAVING=false KEEPDIRTY=false
[2025.08.26-03.08.01:679][921]LogUObjectHash: Compacting FUObjectHashTables data took   0.25ms
[2025.08.26-03.08.01:690][921]LogSavePackage: Moving output files for package: /Game/AURACRON
[2025.08.26-03.08.01:690][921]LogSavePackage: Moving '../../../../../../Game/Auracron/Saved/AURACRON60FE37FD4AD9A240B62E28A1F8D98314.tmp' to '../../../../../../Game/Auracron/Content/AURACRON.umap'
[2025.08.26-03.08.01:697][921]LogFileHelpers: Saving map 'AURACRON' took 0.054
[2025.08.26-03.08.01:698][921]OBJ SavePackage: Generating thumbnails for [0] asset(s) in package [/Game/Blueprints/BP_AbismoUmbralLayer] ([2] browsable assets)...
[2025.08.26-03.08.01:698][921]OBJ SavePackage: Finished generating thumbnails for package [/Game/Blueprints/BP_AbismoUmbralLayer]
[2025.08.26-03.08.01:698][921]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Blueprints/BP_AbismoUmbralLayer" FILE="../../../../../../Game/Auracron/Content/Blueprints/BP_AbismoUmbralLayer.uasset" SILENT=true
[2025.08.26-03.08.01:706][921]LogSavePackage: Moving output files for package: /Game/Blueprints/BP_AbismoUmbralLayer
[2025.08.26-03.08.01:706][921]LogSavePackage: Moving '../../../../../../Game/Auracron/Saved/BP_AbismoUmbralLayer3ADA20574EEDB49A8905E4A8089EA9B7.tmp' to '../../../../../../Game/Auracron/Content/Blueprints/BP_AbismoUmbralLayer.uasset'
[2025.08.26-03.08.01:710][921]OBJ SavePackage: Generating thumbnails for [0] asset(s) in package [/Game/Blueprints/BP_AuracronGameMode] ([2] browsable assets)...
[2025.08.26-03.08.01:710][921]OBJ SavePackage: Finished generating thumbnails for package [/Game/Blueprints/BP_AuracronGameMode]
[2025.08.26-03.08.01:710][921]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Blueprints/BP_AuracronGameMode" FILE="../../../../../../Game/Auracron/Content/Blueprints/BP_AuracronGameMode.uasset" SILENT=true
[2025.08.26-03.08.01:717][921]LogSavePackage: Moving output files for package: /Game/Blueprints/BP_AuracronGameMode
[2025.08.26-03.08.01:717][921]LogSavePackage: Moving '../../../../../../Game/Auracron/Saved/BP_AuracronGameMode8631ED664253C0B338D6D398AF587D06.tmp' to '../../../../../../Game/Auracron/Content/Blueprints/BP_AuracronGameMode.uasset'
[2025.08.26-03.08.01:720][921]OBJ SavePackage: Generating thumbnails for [0] asset(s) in package [/Game/Blueprints/BP_FirmamentoZephyrLayer] ([2] browsable assets)...
[2025.08.26-03.08.01:720][921]OBJ SavePackage: Finished generating thumbnails for package [/Game/Blueprints/BP_FirmamentoZephyrLayer]
[2025.08.26-03.08.01:720][921]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Blueprints/BP_FirmamentoZephyrLayer" FILE="../../../../../../Game/Auracron/Content/Blueprints/BP_FirmamentoZephyrLayer.uasset" SILENT=true
[2025.08.26-03.08.01:729][921]LogSavePackage: Moving output files for package: /Game/Blueprints/BP_FirmamentoZephyrLayer
[2025.08.26-03.08.01:729][921]LogSavePackage: Moving '../../../../../../Game/Auracron/Saved/BP_FirmamentoZephyrLayer9855C3DE4487771FD2AEFAA3B745A3A3.tmp' to '../../../../../../Game/Auracron/Content/Blueprints/BP_FirmamentoZephyrLayer.uasset'
[2025.08.26-03.08.01:732][921]OBJ SavePackage: Generating thumbnails for [0] asset(s) in package [/Game/Blueprints/BP_LayerMarker] ([2] browsable assets)...
[2025.08.26-03.08.01:732][921]OBJ SavePackage: Finished generating thumbnails for package [/Game/Blueprints/BP_LayerMarker]
[2025.08.26-03.08.01:732][921]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Blueprints/BP_LayerMarker" FILE="../../../../../../Game/Auracron/Content/Blueprints/BP_LayerMarker.uasset" SILENT=true
[2025.08.26-03.08.01:740][921]LogSavePackage: Moving output files for package: /Game/Blueprints/BP_LayerMarker
[2025.08.26-03.08.01:740][921]LogSavePackage: Moving '../../../../../../Game/Auracron/Saved/BP_LayerMarker25EB540E403075B1BDEF49A7F1F13E20.tmp' to '../../../../../../Game/Auracron/Content/Blueprints/BP_LayerMarker.uasset'
[2025.08.26-03.08.01:743][921]OBJ SavePackage: Generating thumbnails for [0] asset(s) in package [/Game/Blueprints/BP_MultilayerManager] ([2] browsable assets)...
[2025.08.26-03.08.01:743][921]OBJ SavePackage: Finished generating thumbnails for package [/Game/Blueprints/BP_MultilayerManager]
[2025.08.26-03.08.01:743][921]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Blueprints/BP_MultilayerManager" FILE="../../../../../../Game/Auracron/Content/Blueprints/BP_MultilayerManager.uasset" SILENT=true
[2025.08.26-03.08.01:750][921]LogSavePackage: Moving output files for package: /Game/Blueprints/BP_MultilayerManager
[2025.08.26-03.08.01:750][921]LogSavePackage: Moving '../../../../../../Game/Auracron/Saved/BP_MultilayerManager0256AF5540AC5EFDC62FEAA886722081.tmp' to '../../../../../../Game/Auracron/Content/Blueprints/BP_MultilayerManager.uasset'
[2025.08.26-03.08.01:753][921]OBJ SavePackage: Generating thumbnails for [0] asset(s) in package [/Game/Blueprints/BP_PlanicieRadianteLayer] ([2] browsable assets)...
[2025.08.26-03.08.01:753][921]OBJ SavePackage: Finished generating thumbnails for package [/Game/Blueprints/BP_PlanicieRadianteLayer]
[2025.08.26-03.08.01:753][921]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Blueprints/BP_PlanicieRadianteLayer" FILE="../../../../../../Game/Auracron/Content/Blueprints/BP_PlanicieRadianteLayer.uasset" SILENT=true
[2025.08.26-03.08.01:761][921]LogSavePackage: Moving output files for package: /Game/Blueprints/BP_PlanicieRadianteLayer
[2025.08.26-03.08.01:761][921]LogSavePackage: Moving '../../../../../../Game/Auracron/Saved/BP_PlanicieRadianteLayer60820F8148BE7751B589C1A17A38B47F.tmp' to '../../../../../../Game/Auracron/Content/Blueprints/BP_PlanicieRadianteLayer.uasset'
[2025.08.26-03.08.01:782][921]LogFileHelpers: InternalPromptForCheckoutAndSave took 186.375 ms (total: 413.549 ms)
[2025.08.26-03.08.01:838][921]LogStall: Shutdown...
[2025.08.26-03.08.01:838][921]LogStall: Shutdown complete.
[2025.08.26-03.08.01:869][921]LogSlate: Window 'Auracron — Unreal Editor' being destroyed
[2025.08.26-03.08.01:876][921]LogWindowsTextInputMethodSystem: Activated input method: Português (Brasil) - (Keyboard).
[2025.08.26-03.08.01:940][921]LogContentValidation: Display: Starting to validate 7 assets
[2025.08.26-03.08.01:940][921]LogContentValidation: Enabled validators:
[2025.08.26-03.08.01:940][921]LogContentValidation: 	/Script/DataValidation.EditorValidator_Material
[2025.08.26-03.08.01:940][921]LogContentValidation: 	/Script/DataValidation.DirtyFilesChangelistValidator
[2025.08.26-03.08.01:940][921]LogContentValidation: 	/Script/DataValidation.EditorValidator_Localization
[2025.08.26-03.08.01:940][921]LogContentValidation: 	/Script/DataValidation.PackageFileValidator
[2025.08.26-03.08.01:940][921]LogContentValidation: 	/Script/DataValidation.WorldPartitionChangelistValidator
[2025.08.26-03.08.01:940][921]LogContentValidation: 	/Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
[2025.08.26-03.08.01:940][921]AssetCheck: /Game/AURACRON Validando ativo
[2025.08.26-03.08.01:940][921]AssetCheck: /Game/Blueprints/BP_AbismoUmbralLayer Validando ativo
[2025.08.26-03.08.01:940][921]AssetCheck: /Game/Blueprints/BP_AuracronGameMode Validando ativo
[2025.08.26-03.08.01:940][921]AssetCheck: /Game/Blueprints/BP_FirmamentoZephyrLayer Validando ativo
[2025.08.26-03.08.01:941][921]AssetCheck: /Game/Blueprints/BP_LayerMarker Validando ativo
[2025.08.26-03.08.01:941][921]AssetCheck: /Game/Blueprints/BP_MultilayerManager Validando ativo
[2025.08.26-03.08.01:941][921]AssetCheck: /Game/Blueprints/BP_PlanicieRadianteLayer Validando ativo
[2025.08.26-03.08.01:944][921]Cmd: QUIT_EDITOR
[2025.08.26-03.08.01:944][921]LogCore: Engine exit requested (reason: UUnrealEdEngine::CloseEditor())
[2025.08.26-03.08.01:945][921]LogSourceControl: Display: Uncontrolled Changelist persistency file saved ../../../../../../Game/Auracron/Saved/SourceControl/UncontrolledChangelists.json
[2025.08.26-03.08.01:948][922]LogCore: Engine exit requested (reason: EngineExit() was called; note: exit was already requested)
[2025.08.26-03.08.01:948][922]LogStaticMesh: Abandoning remaining async distance field tasks for shutdown
[2025.08.26-03.08.01:949][922]LogStaticMesh: Abandoning remaining async card representation tasks for shutdown
[2025.08.26-03.08.01:949][922]LogWorld: UWorld::CleanupWorld for AURACRON, bSessionEnded=true, bCleanupResources=true
[2025.08.26-03.08.01:949][922]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.08.26-03.08.01:958][922]LogStylusInput: Shutting down StylusInput subsystem.
[2025.08.26-03.08.01:958][922]LogTedsSettings: UTedsSettingsEditorSubsystem::Deinitialize
[2025.08.26-03.08.01:958][922]LogTemp: Display: UnrealMCPBridge: Shutting down
[2025.08.26-03.08.02:025][922]LogTemp: Display: MCPServerRunnable: Server thread stopping
[2025.08.26-03.08.02:027][922]LogTemp: Display: UnrealMCPBridge: Server stopped
[2025.08.26-03.08.02:027][922]LogLevelSequenceEditor: LevelSequenceEditor subsystem deinitialized.
[2025.08.26-03.08.02:041][922]LogRuntimeTelemetry: Recording EnginePreExit events
[2025.08.26-03.08.02:041][922]LogStudioTelemetry: Ended StudioTelemetry Session
[2025.08.26-03.08.02:046][922]LogAnalytics: Display: [UEEditor.Rocket.Release] AnalyticsET::EndSession
[2025.08.26-03.08.02:047][922]LogAudio: Display: Beginning Audio Device Manager Shutdown (Module: AudioMixerXAudio2)...
[2025.08.26-03.08.02:048][922]LogAudio: Display: Destroying 1 Remaining Audio Device(s)...
[2025.08.26-03.08.02:048][922]LogAudio: Display: Audio Device unregistered from world 'AURACRON'.
[2025.08.26-03.08.02:048][922]LogAudio: Display: Shutting down audio device while 1 references to it are still alive. For more information, compile with INSTRUMENT_AUDIODEVICE_HANDLES.
[2025.08.26-03.08.02:048][922]LogAudioMixer: Display: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=1, StreamState=4
[2025.08.26-03.08.02:050][922]LogAudioMixer: Display: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=1, StreamState=2
[2025.08.26-03.08.02:058][922]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID -1
[2025.08.26-03.08.02:058][922]LogAudio: Display: Audio Device Manager Shutdown
[2025.08.26-03.08.02:063][922]LogSlate: Slate User Destroyed.  User Index 0, Is Virtual User: 0
[2025.08.26-03.08.02:063][922]LogExit: Preparing to exit.
[2025.08.26-03.08.02:087][922]LogTemp: Display: UnrealMCPCollisionCommands: Destroyed
[2025.08.26-03.08.02:087][922]LogTemp: Vision system state saved
[2025.08.26-03.08.02:087][922]LogTemp: UnrealMCP AI Commands destroyed
[2025.08.26-03.08.02:087][922]LogTemp: FUnrealMCPRealmCommands: Sistema de Transição de Realms finalizado
[2025.08.26-03.08.02:087][922]LogTemp: FUnrealMCPMultilayerMapCommands: Sistema de Mapa Multicamada finalizado
[2025.08.26-03.08.02:087][922]LogTemp: FUnrealMCPLaneMechanicsCommands: Sistema de Mecânicas de Lane finalizado
[2025.08.26-03.08.02:087][922]LogTemp: FUnrealMCPJungleSystemCommands: Sistema de Jungle finalizado
[2025.08.26-03.08.02:087][922]LogTemp: FUnrealMCPVerticalNavigationCommands: Sistema de Navegação Vertical finalizado
[2025.08.26-03.08.02:087][922]LogTemp: FUnrealMCPGamePhasesCommands: Sistema de Fases da Partida finalizado
[2025.08.26-03.08.02:087][922]LogTemp: FUnrealMCPObjectivesStructuresCommands: Sistema de Objetivos e Estruturas finalizado
[2025.08.26-03.08.02:087][922]LogTemp: FUnrealMCPCombatMechanicsCommands: Sistema de Mecânicas de Combate finalizado
[2025.08.26-03.08.02:095][922]LogUObjectHash: Compacting FUObjectHashTables data took   0.64ms
[2025.08.26-03.08.02:502][922]LogEditorDataStorage: Deinitializing
[2025.08.26-03.08.02:569][922]LogOutputDevice: Warning: 

Script Stack (0 frames) :

[2025.08.26-03.08.02:597][922]LogStats: FPlatformStackWalk::StackWalkAndDump -  0.028 s
[2025.08.26-03.08.02:597][922]LogOutputDevice: Error: === Handled ensure: ===
[2025.08.26-03.08.02:597][922]LogOutputDevice: Error: 
[2025.08.26-03.08.02:597][922]LogOutputDevice: Error: Ensure condition failed: !bInitialized  [File:D:\build\++UE5\Sync\Engine\Source\Runtime\Engine\Private\Subsystems\WorldSubsystem.cpp] [Line: 115] 
[2025.08.26-03.08.02:597][922]LogOutputDevice: Error: Tickable subsystem MassSignalSubsystem /Game/Levels/FIR_AuracronMainLevel.AuracronMainLevel:None was destroyed while still initialized! Check for missing Super call
[2025.08.26-03.08.02:597][922]LogOutputDevice: Error: Stack: 
[2025.08.26-03.08.02:597][922]LogOutputDevice: Error: [Callstack] 0x00007ffb8ac0370e UnrealEditor-Engine.dll!UnknownFunction []
[2025.08.26-03.08.02:597][922]LogOutputDevice: Error: [Callstack] 0x00007ffb8a073144 UnrealEditor-Engine.dll!UnknownFunction []
[2025.08.26-03.08.02:597][922]LogOutputDevice: Error: [Callstack] 0x00007ffbd23a3aa2 UnrealEditor-CoreUObject.dll!UnknownFunction []
[2025.08.26-03.08.02:597][922]LogOutputDevice: Error: [Callstack] 0x00007ffbd22c821c UnrealEditor-CoreUObject.dll!UnknownFunction []
[2025.08.26-03.08.02:597][922]LogOutputDevice: Error: [Callstack] 0x00007ffbd2248c70 UnrealEditor-CoreUObject.dll!UnknownFunction []
[2025.08.26-03.08.02:597][922]LogOutputDevice: Error: [Callstack] 0x00007ffbd2272a45 UnrealEditor-CoreUObject.dll!UnknownFunction []
[2025.08.26-03.08.02:597][922]LogOutputDevice: Error: [Callstack] 0x00007ffbd23dd484 UnrealEditor-CoreUObject.dll!UnknownFunction []
[2025.08.26-03.08.02:597][922]LogOutputDevice: Error: [Callstack] 0x00007ffbd20459d7 UnrealEditor-CoreUObject.dll!UnknownFunction []
[2025.08.26-03.08.02:597][922]LogOutputDevice: Error: [Callstack] 0x00007ff6a1713ff1 UnrealEditor.exe!UnknownFunction []
[2025.08.26-03.08.02:597][922]LogOutputDevice: Error: [Callstack] 0x00007ff6a1713651 UnrealEditor.exe!UnknownFunction []
[2025.08.26-03.08.02:597][922]LogOutputDevice: Error: [Callstack] 0x00007ff6a171c2a6 UnrealEditor.exe!UnknownFunction []
[2025.08.26-03.08.02:597][922]LogOutputDevice: Error: [Callstack] 0x00007ff6a171e655 UnrealEditor.exe!UnknownFunction []
[2025.08.26-03.08.02:597][922]LogOutputDevice: Error: [Callstack] 0x00007ff6a171e6ba UnrealEditor.exe!UnknownFunction []
[2025.08.26-03.08.02:597][922]LogOutputDevice: Error: [Callstack] 0x00007ff6a172209e UnrealEditor.exe!UnknownFunction []
[2025.08.26-03.08.02:597][922]LogOutputDevice: Error: [Callstack] 0x00007ff6a1734e44 UnrealEditor.exe!UnknownFunction []
[2025.08.26-03.08.02:597][922]LogOutputDevice: Error: [Callstack] 0x00007ff6a17380fa UnrealEditor.exe!UnknownFunction []
[2025.08.26-03.08.02:597][922]LogOutputDevice: Error: [Callstack] 0x00007ffc3856e8d7 KERNEL32.DLL!UnknownFunction []
[2025.08.26-03.08.02:597][922]LogOutputDevice: Error: [Callstack] 0x00007ffc3915c34c ntdll.dll!UnknownFunction []
[2025.08.26-03.08.02:597][922]LogOutputDevice: Error: 
[2025.08.26-03.08.02:607][922]LogStats:                SubmitErrorReport -  0.000 s
[2025.08.26-03.08.03:893][922]LogStats:                    SendNewReport -  1.286 s
[2025.08.26-03.08.03:893][922]LogStats:             FDebug::EnsureFailed -  1.325 s
[2025.08.26-03.08.03:893][922]LogOutputDevice: Warning: 

Script Stack (0 frames) :

[2025.08.26-03.08.03:903][922]LogWindows: Error: appError called: Assertion failed: InitState == EWorldPartitionInitState::Uninitialized [File:D:\build\++UE5\Sync\Engine\Source\Runtime\Engine\Private\WorldPartition\WorldPartition.cpp] [Line: 1728] 


[2025.08.26-03.08.03:903][922]LogWindows: Windows GetLastError: A operação foi concluída com êxito. (0)
