# Scripts PowerShell para o Projeto Auracron

Este diretório contém scripts PowerShell para facilitar o gerenciamento e abertura do projeto Auracron no Unreal Engine.

## Scripts Disponíveis

### 1. OpenProject.ps1

Script principal para abrir e gerenciar o projeto Auracron.

#### Funcionalidades:
- ✅ Abre o Unreal Engine Editor com o projeto
- ✅ Compila o projeto
- ✅ Limpa arquivos temporários
- ✅ Detecta automaticamente a versão do Unreal Engine instalada
- ✅ Verificações de segurança antes da execução

#### Uso:

```powershell
# Abrir o editor (comportamento padrão)
.\OpenProject.ps1

# Abrir o editor (explícito)
.\OpenProject.ps1 -Editor

# Compilar o projeto
.\OpenProject.ps1 -Build

# Limpar arquivos temporários
.\OpenProject.ps1 -Clean

# Limpar e compilar
.\OpenProject.ps1 -Clean -Build

# Limpar e abrir o editor
.\OpenProject.ps1 -Clean -Editor
```

### 2. OpenAuracron.ps1 (Atalho)

Script de atalho localizado na raiz do projeto para acesso rápido.

```powershell
# Executar da raiz do projeto
.\OpenAuracron.ps1
```

## Configuração Inicial

### Habilitar Execução de Scripts

Antes de usar os scripts, você pode precisar habilitar a execução de scripts PowerShell:

```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

### Verificar Instalação do Unreal Engine

O script detecta automaticamente as seguintes versões do Unreal Engine:
- UE 5.6 (padrão)
- UE 5.5
- UE 5.4
- UE 5.3

Caminhos verificados:
- `C:\Program Files\Epic Games\UE_X.X\Engine\Binaries\Win64\UnrealEditor.exe`

## Estrutura de Arquivos Limpos

Quando usar a opção `-Clean`, os seguintes diretórios serão removidos:
- `Binaries/`
- `Intermediate/`
- `Saved/Logs/`
- `Saved/Config/CrashReportClient/`

## Solução de Problemas

### Erro: "Execution of scripts is disabled"

```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser -Force
```

### Erro: "Unreal Engine não encontrado"

1. Verifique se o Unreal Engine está instalado
2. Verifique o caminho de instalação
3. Edite o script para apontar para o caminho correto

### Erro: "Arquivo do projeto não encontrado"

1. Verifique se você está executando o script do diretório correto
2. Verifique se o arquivo `Auracron.uproject` existe

## Personalização

Para personalizar os caminhos ou adicionar funcionalidades:

1. Edite o arquivo `OpenProject.ps1`
2. Modifique as variáveis no início do script:
   - `$ProjectPath`
   - `$ProjectFile`
   - `$UnrealEnginePath`

## Logs e Debugging

O script fornece saída colorida para facilitar o debugging:
- 🟢 Verde: Operações bem-sucedidas
- 🟡 Amarelo: Avisos e operações em andamento
- 🔴 Vermelho: Erros
- 🔵 Ciano: Informações
- 🟣 Magenta: Cabeçalhos de seção

## Contribuição

Para adicionar novas funcionalidades ou melhorar os scripts:

1. Mantenha a compatibilidade com versões anteriores
2. Adicione comentários explicativos
3. Teste em diferentes ambientes
4. Atualize esta documentação