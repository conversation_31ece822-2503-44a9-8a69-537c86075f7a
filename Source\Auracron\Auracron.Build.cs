// Copyright Epic Games, Inc. All Rights Reserved.

using UnrealBuildTool;

public class Auracron : ModuleRules
{
	public Auracron(ReadOnlyTargetRules Target) : base(Target)
	{
		PCHUsage = PCHUsageMode.UseExplicitOrSharedPCHs;

		PublicDependencyModuleNames.AddRange(new string[] {
			"Core",
			"CoreUObject",
			"Engine",
			"InputCore",
			"NavigationSystem",
			"AIModule",
			"GameplayTasks",
			"UMG",
			"Slate",
			"SlateCore"
		});

		PrivateDependencyModuleNames.AddRange(new string[] {
			"RenderCore",
			"RHI",
			"Niagara",
			"ChaosVehicles",
			"ChaosSolverEngine",
			"FieldSystemEngine",
			"GeometryCollectionEngine",
			"OnlineSubsystem",
			"OnlineSubsystemUtils",
			"ReplicationGraph"
		});
	}
}