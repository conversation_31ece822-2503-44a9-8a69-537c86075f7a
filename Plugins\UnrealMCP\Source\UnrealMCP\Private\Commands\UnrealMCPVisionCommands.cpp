#include "Commands/UnrealMCPVisionCommands.h"
#include "Engine/World.h"
#include "Kismet/GameplayStatics.h"
#include "Components/StaticMeshComponent.h"
#include "Components/BoxComponent.h"
#include "Components/SphereComponent.h"
#include "Engine/StaticMesh.h"
#include "Materials/Material.h"
#include "UObject/ConstructorHelpers.h"
#include "DrawDebugHelpers.h"
#include "Engine/Engine.h"

// === Constantes ===

// Tipos de resposta
const FString FUnrealMCPVisionCommands::RESPONSE_SUCCESS = TEXT("success");
const FString FUnrealMCPVisionCommands::RESPONSE_ERROR = TEXT("error");
const FString FUnrealMCPVisionCommands::RESPONSE_WARNING = TEXT("warning");

// Tipos de fog
const FString FUnrealMCPVisionCommands::FOG_TYPE_STANDARD = TEXT("standard");
const FString FUnrealMCPVisionCommands::FOG_TYPE_VOLUMETRIC = TEXT("volumetric");
const FString FUnrealMCPVisionCommands::FOG_TYPE_HEIGHT_BASED = TEXT("height_based");
const FString FUnrealMCPVisionCommands::FOG_TYPE_DISTANCE_BASED = TEXT("distance_based");

// Tipos de sensor de visão
const FString FUnrealMCPVisionCommands::SENSOR_TYPE_OMNIDIRECTIONAL = TEXT("omnidirectional");
const FString FUnrealMCPVisionCommands::SENSOR_TYPE_DIRECTIONAL = TEXT("directional");
const FString FUnrealMCPVisionCommands::SENSOR_TYPE_CONE = TEXT("cone");
const FString FUnrealMCPVisionCommands::SENSOR_TYPE_SECTOR = TEXT("sector");

// Algoritmos de oclusão
const FString FUnrealMCPVisionCommands::OCCLUSION_RAYCAST = TEXT("raycast");
const FString FUnrealMCPVisionCommands::OCCLUSION_RASTERIZATION = TEXT("rasterization");
const FString FUnrealMCPVisionCommands::OCCLUSION_HIERARCHICAL = TEXT("hierarchical");
const FString FUnrealMCPVisionCommands::OCCLUSION_HYBRID = TEXT("hybrid");

// Tipos de persistência
const FString FUnrealMCPVisionCommands::PERSISTENCE_MEMORY = TEXT("memory");
const FString FUnrealMCPVisionCommands::PERSISTENCE_FILE = TEXT("file");
const FString FUnrealMCPVisionCommands::PERSISTENCE_DATABASE = TEXT("database");
const FString FUnrealMCPVisionCommands::PERSISTENCE_CLOUD = TEXT("cloud");

// Tipos de wards
const FString FUnrealMCPVisionCommands::WARD_TYPE_STEALTH = TEXT("stealth_ward");
const FString FUnrealMCPVisionCommands::WARD_TYPE_CONTROL = TEXT("control_ward");
const FString FUnrealMCPVisionCommands::WARD_TYPE_FARSIGHT = TEXT("farsight_ward");

// Nomes das camadas
const FString FUnrealMCPVisionCommands::LAYER_PLANICIE_RADIANTE = TEXT("planicie_radiante");
const FString FUnrealMCPVisionCommands::LAYER_FIRMAMENTO_ZEPHYR = TEXT("firmamento_zephyr");
const FString FUnrealMCPVisionCommands::LAYER_ABISMO_UMBRAL = TEXT("abismo_umbral");

// === Construtor e Destrutor ===

FUnrealMCPVisionCommands::FUnrealMCPVisionCommands()
    : bDebugEnabled(false)
{
    InitializeDefaultSettings();
}

FUnrealMCPVisionCommands::~FUnrealMCPVisionCommands()
{
    SaveSystemState();
    CleanupInvalidObjects();
}

TSharedPtr<FJsonObject> FUnrealMCPVisionCommands::HandleCommand(const FString& CommandType, const TSharedPtr<FJsonObject>& Params)
{
    if (CommandType == TEXT("create_fog_of_war_layer"))
    {
        return HandleCreateFogOfWarLayer(Params);
    }
    else if (CommandType == TEXT("configure_vision_range"))
    {
        return HandleConfigureVisionRangeLayer(Params);
    }
    else if (CommandType == TEXT("setup_line_of_sight"))
    {
        return HandleSetupLineOfSightSystem(Params);
    }
    else if (CommandType == TEXT("update_fog_visibility"))
    {
        return HandleConfigureDynamicFogUpdates(Params);
    }
    else if (CommandType == TEXT("configure_occlusion_system"))
    {
        return HandleConfigureVisionOcclusionSystem(Params);
    }
    else if (CommandType == TEXT("optimize_vision_performance"))
    {
        return HandleOptimizeVisionPerformance(Params);
    }
    else if (CommandType == TEXT("debug_vision_system"))
    {
        return HandleDebugVisionSystem(Params);
    }
    else if (CommandType == TEXT("validate_vision_setup"))
    {
        return HandleValidateVisionSetup(Params);
    }
    else if (CommandType == TEXT("get_vision_system_status"))
    {
        return HandleGetVisionSystemStatus(Params);
    }
    else if (CommandType == TEXT("configure_auracron_vision_layers"))
    {
        return HandleConfigureAuracronVisionLayers(Params);
    }
    else if (CommandType == TEXT("setup_multilayer_vision_system"))
    {
        return HandleSetupMultilayerVisionSystem(Params);
    }
    else if (CommandType == TEXT("configure_layer_vision_ranges"))
    {
        return HandleConfigureLayerVisionRanges(Params);
    }
    else if (CommandType == TEXT("setup_vertical_connector_vision"))
    {
        return HandleSetupVerticalConnectorVision(Params);
    }
    else if (CommandType == TEXT("create_ward_system"))
    {
        return HandleCreateWardSystem(Params);
    }
    else if (CommandType == TEXT("create_multilayer_vision_system"))
    {
        return HandleCreateMultilayerVisionSystem(Params);
    }
    else
    {
        return CreateErrorResponse(FString::Printf(TEXT("Unknown vision command: %s"), *CommandType), TEXT("UNKNOWN_COMMAND"));
    }
}

// === Comandos Principais ===

TSharedPtr<FJsonObject> FUnrealMCPVisionCommands::HandleCreateFogOfWarLayer(const TSharedPtr<FJsonObject>& CommandData)
{
    if (!CommandData.IsValid())
    {
        return CreateErrorResponse(TEXT("Invalid command data"), TEXT("INVALID_DATA"));
    }

    FString LayerName;
    if (!CommandData->TryGetStringField(TEXT("layer_name"), LayerName) || LayerName.IsEmpty())
    {
        return CreateErrorResponse(TEXT("Layer name is required"), TEXT("MISSING_LAYER_NAME"));
    }

    double LayerHeight = 0.0;
    CommandData->TryGetNumberField(TEXT("layer_height"), LayerHeight);

    const TSharedPtr<FJsonObject>* FogSettingsPtr;
    TSharedPtr<FJsonObject> FogSettings;
    if (CommandData->TryGetObjectField(TEXT("fog_settings"), FogSettingsPtr))
    {
        FogSettings = *FogSettingsPtr;
    }
    else
    {
        FogSettings = MakeShareable(new FJsonObject);
    }

    if (!ValidateFogLayerConfig(FogSettings))
    {
        return CreateErrorResponse(TEXT("Invalid fog layer configuration"), TEXT("INVALID_FOG_CONFIG"));
    }

    UWorld* World = GetCurrentWorld();
    if (!World)
    {
        return CreateErrorResponse(TEXT("No valid world found"), TEXT("NO_WORLD"));
    }

    // Criar ator para a camada de fog
    FActorSpawnParameters SpawnParams;
    SpawnParams.Name = FName(*FString::Printf(TEXT("FogLayer_%s"), *LayerName));
    
    AActor* FogLayerActor = World->SpawnActor<AActor>(AActor::StaticClass(), FVector(0, 0, LayerHeight), FRotator::ZeroRotator, SpawnParams);
    if (!FogLayerActor)
    {
        return CreateErrorResponse(TEXT("Failed to create fog layer actor"), TEXT("SPAWN_FAILED"));
    }

    // Configurar componentes do fog
    UStaticMeshComponent* FogMeshComponent = NewObject<UStaticMeshComponent>(FogLayerActor);
    FogLayerActor->SetRootComponent(FogMeshComponent);
    
    // Adicionar ao cache
    FogLayers.Add(LayerName, FogLayerActor);

    // Criar resposta
    TSharedPtr<FJsonObject> ResponseData = MakeShareable(new FJsonObject);
    ResponseData->SetStringField(TEXT("layer_name"), LayerName);
    ResponseData->SetNumberField(TEXT("layer_height"), LayerHeight);
    ResponseData->SetStringField(TEXT("actor_name"), FogLayerActor->GetName());
    ResponseData->SetObjectField(TEXT("fog_settings"), FogSettings);

    return CreateSuccessResponse(TEXT("Fog of War layer created successfully"), ResponseData);
}

TSharedPtr<FJsonObject> FUnrealMCPVisionCommands::HandleConfigureVisionRangeLayer(const TSharedPtr<FJsonObject>& CommandData)
{
    if (!CommandData.IsValid())
    {
        return CreateErrorResponse(TEXT("Invalid command data"), TEXT("INVALID_DATA"));
    }

    FString LayerName;
    if (!CommandData->TryGetStringField(TEXT("layer_name"), LayerName) || LayerName.IsEmpty())
    {
        return CreateErrorResponse(TEXT("Layer name is required"), TEXT("MISSING_LAYER_NAME"));
    }

    const TSharedPtr<FJsonObject>* VisionSettingsPtr;
    if (!CommandData->TryGetObjectField(TEXT("vision_settings"), VisionSettingsPtr))
    {
        return CreateErrorResponse(TEXT("Vision settings are required"), TEXT("MISSING_VISION_SETTINGS"));
    }

    TSharedPtr<FJsonObject> VisionSettings = *VisionSettingsPtr;

    // Verificar se a camada existe
    if (!FogLayers.Contains(LayerName))
    {
        return CreateErrorResponse(TEXT("Fog layer not found"), TEXT("LAYER_NOT_FOUND"));
    }

    // Configurar alcances de visão
    double VisionRange = 1000.0;
    VisionSettings->TryGetNumberField(TEXT("range"), VisionRange);

    double VisionAngle = 360.0;
    VisionSettings->TryGetNumberField(TEXT("angle"), VisionAngle);

    double Precision = 1.0;
    VisionSettings->TryGetNumberField(TEXT("precision"), Precision);

    // Criar resposta
    TSharedPtr<FJsonObject> ResponseData = MakeShareable(new FJsonObject);
    ResponseData->SetStringField(TEXT("layer_name"), LayerName);
    ResponseData->SetNumberField(TEXT("vision_range"), VisionRange);
    ResponseData->SetNumberField(TEXT("vision_angle"), VisionAngle);
    ResponseData->SetNumberField(TEXT("precision"), Precision);

    return CreateSuccessResponse(TEXT("Vision range configured successfully"), ResponseData);
}

TSharedPtr<FJsonObject> FUnrealMCPVisionCommands::HandleSetupLineOfSightSystem(const TSharedPtr<FJsonObject>& CommandData)
{
    if (!CommandData.IsValid())
    {
        return CreateErrorResponse(TEXT("Invalid command data"), TEXT("INVALID_DATA"));
    }

    FString LayerName;
    if (!CommandData->TryGetStringField(TEXT("layer_name"), LayerName) || LayerName.IsEmpty())
    {
        return CreateErrorResponse(TEXT("Layer name is required"), TEXT("MISSING_LAYER_NAME"));
    }

    const TSharedPtr<FJsonObject>* LOSSettingsPtr;
    if (!CommandData->TryGetObjectField(TEXT("los_settings"), LOSSettingsPtr))
    {
        return CreateErrorResponse(TEXT("LOS settings are required"), TEXT("MISSING_LOS_SETTINGS"));
    }

    TSharedPtr<FJsonObject> LOSSettings = *LOSSettingsPtr;

    if (!ValidateLineOfSightConfig(LOSSettings))
    {
        return CreateErrorResponse(TEXT("Invalid Line of Sight configuration"), TEXT("INVALID_LOS_CONFIG"));
    }

    // Configurar sistema de LOS
    FString TracePrecision = TEXT("medium");
    LOSSettings->TryGetStringField(TEXT("trace_precision"), TracePrecision);

    bool bCacheEnabled = true;
    LOSSettings->TryGetBoolField(TEXT("cache_enabled"), bCacheEnabled);

    double CacheDuration = 1.0;
    LOSSettings->TryGetNumberField(TEXT("cache_duration"), CacheDuration);

    double MaxTraceDistance = 10000.0;
    LOSSettings->TryGetNumberField(TEXT("max_trace_distance"), MaxTraceDistance);

    FString TraceChannel = TEXT("visibility");
    LOSSettings->TryGetStringField(TEXT("trace_channel"), TraceChannel);

    // Criar resposta
    TSharedPtr<FJsonObject> ResponseData = MakeShareable(new FJsonObject);
    ResponseData->SetStringField(TEXT("layer_name"), LayerName);
    ResponseData->SetStringField(TEXT("trace_precision"), TracePrecision);
    ResponseData->SetBoolField(TEXT("cache_enabled"), bCacheEnabled);
    ResponseData->SetNumberField(TEXT("cache_duration"), CacheDuration);
    ResponseData->SetNumberField(TEXT("max_trace_distance"), MaxTraceDistance);
    ResponseData->SetStringField(TEXT("trace_channel"), TraceChannel);

    return CreateSuccessResponse(TEXT("Line of Sight system configured successfully"), ResponseData);
}

TSharedPtr<FJsonObject> FUnrealMCPVisionCommands::HandleCreateVisionBlockingVolumes(const TSharedPtr<FJsonObject>& CommandData)
{
    if (!CommandData.IsValid())
    {
        return CreateErrorResponse(TEXT("Invalid command data"), TEXT("INVALID_DATA"));
    }

    FString LayerName;
    if (!CommandData->TryGetStringField(TEXT("layer_name"), LayerName) || LayerName.IsEmpty())
    {
        return CreateErrorResponse(TEXT("Layer name is required"), TEXT("MISSING_LAYER_NAME"));
    }

    const TArray<TSharedPtr<FJsonValue>>* VolumesDataPtr;
    if (!CommandData->TryGetArrayField(TEXT("volumes_data"), VolumesDataPtr))
    {
        return CreateErrorResponse(TEXT("Volumes data is required"), TEXT("MISSING_VOLUMES_DATA"));
    }

    UWorld* World = GetCurrentWorld();
    if (!World)
    {
        return CreateErrorResponse(TEXT("No valid world found"), TEXT("NO_WORLD"));
    }

    TArray<TWeakObjectPtr<AActor>> CreatedVolumes;
    int32 VolumeIndex = 0;

    for (const TSharedPtr<FJsonValue>& VolumeValue : *VolumesDataPtr)
    {
        const TSharedPtr<FJsonObject>* VolumeDataPtr;
        if (!VolumeValue->TryGetObject(VolumeDataPtr))
        {
            continue;
        }

        TSharedPtr<FJsonObject> VolumeData = *VolumeDataPtr;

        // Obter posição
        const TArray<TSharedPtr<FJsonValue>>* PositionPtr;
        FVector Position = FVector::ZeroVector;
        if (VolumeData->TryGetArrayField(TEXT("position"), PositionPtr) && PositionPtr->Num() >= 3)
        {
            Position.X = (*PositionPtr)[0]->AsNumber();
            Position.Y = (*PositionPtr)[1]->AsNumber();
            Position.Z = (*PositionPtr)[2]->AsNumber();
        }

        // Obter tamanho
        const TArray<TSharedPtr<FJsonValue>>* SizePtr;
        FVector Size = FVector(100, 100, 100);
        if (VolumeData->TryGetArrayField(TEXT("size"), SizePtr) && SizePtr->Num() >= 3)
        {
            Size.X = (*SizePtr)[0]->AsNumber();
            Size.Y = (*SizePtr)[1]->AsNumber();
            Size.Z = (*SizePtr)[2]->AsNumber();
        }

        // Criar volume
        FActorSpawnParameters SpawnParams;
        SpawnParams.Name = FName(*FString::Printf(TEXT("VisionBlockingVolume_%s_%d"), *LayerName, VolumeIndex));
        
        AActor* VolumeActor = World->SpawnActor<AActor>(AActor::StaticClass(), Position, FRotator::ZeroRotator, SpawnParams);
        if (VolumeActor)
        {
            UBoxComponent* BoxComponent = NewObject<UBoxComponent>(VolumeActor);
            BoxComponent->SetBoxExtent(Size);
            VolumeActor->SetRootComponent(BoxComponent);
            
            CreatedVolumes.Add(VolumeActor);
        }

        VolumeIndex++;
    }

    // Adicionar ao cache
    BlockingVolumes.Add(LayerName, CreatedVolumes);

    // Criar resposta
    TSharedPtr<FJsonObject> ResponseData = MakeShareable(new FJsonObject);
    ResponseData->SetStringField(TEXT("layer_name"), LayerName);
    ResponseData->SetNumberField(TEXT("volumes_created"), CreatedVolumes.Num());

    return CreateSuccessResponse(TEXT("Vision blocking volumes created successfully"), ResponseData);
}

TSharedPtr<FJsonObject> FUnrealMCPVisionCommands::HandleConfigureDynamicFogUpdates(const TSharedPtr<FJsonObject>& CommandData)
{
    if (!CommandData.IsValid())
    {
        return CreateErrorResponse(TEXT("Invalid command data"), TEXT("INVALID_DATA"));
    }

    FString LayerName;
    if (!CommandData->TryGetStringField(TEXT("layer_name"), LayerName) || LayerName.IsEmpty())
    {
        return CreateErrorResponse(TEXT("Layer name is required"), TEXT("MISSING_LAYER_NAME"));
    }

    const TSharedPtr<FJsonObject>* UpdateSettingsPtr;
    if (!CommandData->TryGetObjectField(TEXT("update_settings"), UpdateSettingsPtr))
    {
        return CreateErrorResponse(TEXT("Update settings are required"), TEXT("MISSING_UPDATE_SETTINGS"));
    }

    TSharedPtr<FJsonObject> UpdateSettings = *UpdateSettingsPtr;

    // Configurar atualizações dinâmicas
    double UpdateFrequency = 0.1;
    UpdateSettings->TryGetNumberField(TEXT("frequency"), UpdateFrequency);

    FString TriggerType = TEXT("movement");
    UpdateSettings->TryGetStringField(TEXT("trigger_type"), TriggerType);

    bool bAutoUpdate = true;
    UpdateSettings->TryGetBoolField(TEXT("auto_update"), bAutoUpdate);

    // Criar resposta
    TSharedPtr<FJsonObject> ResponseData = MakeShareable(new FJsonObject);
    ResponseData->SetStringField(TEXT("layer_name"), LayerName);
    ResponseData->SetNumberField(TEXT("update_frequency"), UpdateFrequency);
    ResponseData->SetStringField(TEXT("trigger_type"), TriggerType);
    ResponseData->SetBoolField(TEXT("auto_update"), bAutoUpdate);

    return CreateSuccessResponse(TEXT("Dynamic fog updates configured successfully"), ResponseData);
}

TSharedPtr<FJsonObject> FUnrealMCPVisionCommands::HandleSetupMultilayerVisionInteractions(const TSharedPtr<FJsonObject>& CommandData)
{
    if (!CommandData.IsValid())
    {
        return CreateErrorResponse(TEXT("Invalid command data"), TEXT("INVALID_DATA"));
    }

    const TSharedPtr<FJsonObject>* InteractionRulesPtr;
    if (!CommandData->TryGetObjectField(TEXT("interaction_rules"), InteractionRulesPtr))
    {
        return CreateErrorResponse(TEXT("Interaction rules are required"), TEXT("MISSING_INTERACTION_RULES"));
    }

    TSharedPtr<FJsonObject> InteractionRules = *InteractionRulesPtr;

    // Configurar interações multicamada
    bool bCrossLayerVisibility = true;
    InteractionRules->TryGetBoolField(TEXT("cross_layer_visibility"), bCrossLayerVisibility);

    bool bHeightBasedOcclusion = true;
    InteractionRules->TryGetBoolField(TEXT("height_based_occlusion"), bHeightBasedOcclusion);

    bool bLayerPrioritySystem = true;
    InteractionRules->TryGetBoolField(TEXT("layer_priority_system"), bLayerPrioritySystem);

    // Criar resposta
    TSharedPtr<FJsonObject> ResponseData = MakeShareable(new FJsonObject);
    ResponseData->SetBoolField(TEXT("cross_layer_visibility"), bCrossLayerVisibility);
    ResponseData->SetBoolField(TEXT("height_based_occlusion"), bHeightBasedOcclusion);
    ResponseData->SetBoolField(TEXT("layer_priority_system"), bLayerPrioritySystem);
    ResponseData->SetNumberField(TEXT("configured_layers"), FogLayers.Num());

    return CreateSuccessResponse(TEXT("Multilayer vision interactions configured successfully"), ResponseData);
}

TSharedPtr<FJsonObject> FUnrealMCPVisionCommands::HandleCreateVisionSensors(const TSharedPtr<FJsonObject>& CommandData)
{
    if (!CommandData.IsValid())
    {
        return CreateErrorResponse(TEXT("Invalid command data"), TEXT("INVALID_DATA"));
    }

    FString LayerName;
    if (!CommandData->TryGetStringField(TEXT("layer_name"), LayerName) || LayerName.IsEmpty())
    {
        return CreateErrorResponse(TEXT("Layer name is required"), TEXT("MISSING_LAYER_NAME"));
    }

    const TArray<TSharedPtr<FJsonValue>>* SensorsDataPtr;
    if (!CommandData->TryGetArrayField(TEXT("sensors_data"), SensorsDataPtr))
    {
        return CreateErrorResponse(TEXT("Sensors data is required"), TEXT("MISSING_SENSORS_DATA"));
    }

    UWorld* World = GetCurrentWorld();
    if (!World)
    {
        return CreateErrorResponse(TEXT("No valid world found"), TEXT("NO_WORLD"));
    }

    TArray<TWeakObjectPtr<AActor>> CreatedSensors;
    int32 SensorIndex = 0;

    for (const TSharedPtr<FJsonValue>& SensorValue : *SensorsDataPtr)
    {
        const TSharedPtr<FJsonObject>* SensorDataPtr;
        if (!SensorValue->TryGetObject(SensorDataPtr))
        {
            continue;
        }

        TSharedPtr<FJsonObject> SensorData = *SensorDataPtr;

        if (!ValidateVisionSensorConfig(SensorData))
        {
            continue;
        }

        // Obter posição
        const TArray<TSharedPtr<FJsonValue>>* PositionPtr;
        FVector Position = FVector::ZeroVector;
        if (SensorData->TryGetArrayField(TEXT("position"), PositionPtr) && PositionPtr->Num() >= 3)
        {
            Position.X = (*PositionPtr)[0]->AsNumber();
            Position.Y = (*PositionPtr)[1]->AsNumber();
            Position.Z = (*PositionPtr)[2]->AsNumber();
        }

        // Obter configurações
        double Range = 1000.0;
        SensorData->TryGetNumberField(TEXT("range"), Range);

        double Angle = 360.0;
        SensorData->TryGetNumberField(TEXT("angle"), Angle);

        double UpdateFrequency = 0.5;
        SensorData->TryGetNumberField(TEXT("update_frequency"), UpdateFrequency);

        // Criar sensor
        FActorSpawnParameters SpawnParams;
        SpawnParams.Name = FName(*FString::Printf(TEXT("VisionSensor_%s_%d"), *LayerName, SensorIndex));
        
        AActor* SensorActor = World->SpawnActor<AActor>(AActor::StaticClass(), Position, FRotator::ZeroRotator, SpawnParams);
        if (SensorActor)
        {
            USphereComponent* SphereComponent = NewObject<USphereComponent>(SensorActor);
            SphereComponent->SetSphereRadius(Range);
            SensorActor->SetRootComponent(SphereComponent);
            
            CreatedSensors.Add(SensorActor);
        }

        SensorIndex++;
    }

    // Adicionar ao cache
    VisionSensors.Add(LayerName, CreatedSensors);

    // Criar resposta
    TSharedPtr<FJsonObject> ResponseData = MakeShareable(new FJsonObject);
    ResponseData->SetStringField(TEXT("layer_name"), LayerName);
    ResponseData->SetNumberField(TEXT("sensors_created"), CreatedSensors.Num());

    return CreateSuccessResponse(TEXT("Vision sensors created successfully"), ResponseData);
}

TSharedPtr<FJsonObject> FUnrealMCPVisionCommands::HandleConfigureVisionOcclusionSystem(const TSharedPtr<FJsonObject>& CommandData)
{
    if (!CommandData.IsValid())
    {
        return CreateErrorResponse(TEXT("Invalid command data"), TEXT("INVALID_DATA"));
    }

    FString LayerName;
    if (!CommandData->TryGetStringField(TEXT("layer_name"), LayerName) || LayerName.IsEmpty())
    {
        return CreateErrorResponse(TEXT("Layer name is required"), TEXT("MISSING_LAYER_NAME"));
    }

    const TSharedPtr<FJsonObject>* OcclusionSettingsPtr;
    if (!CommandData->TryGetObjectField(TEXT("occlusion_settings"), OcclusionSettingsPtr))
    {
        return CreateErrorResponse(TEXT("Occlusion settings are required"), TEXT("MISSING_OCCLUSION_SETTINGS"));
    }

    TSharedPtr<FJsonObject> OcclusionSettings = *OcclusionSettingsPtr;

    // Configurar sistema de oclusão
    FString Algorithm = OCCLUSION_RAYCAST;
    OcclusionSettings->TryGetStringField(TEXT("algorithm"), Algorithm);

    double Precision = 1.0;
    OcclusionSettings->TryGetNumberField(TEXT("precision"), Precision);

    bool bCacheEnabled = true;
    OcclusionSettings->TryGetBoolField(TEXT("cache_enabled"), bCacheEnabled);

    double CacheDuration = 2.0;
    OcclusionSettings->TryGetNumberField(TEXT("cache_duration"), CacheDuration);

    // Criar resposta
    TSharedPtr<FJsonObject> ResponseData = MakeShareable(new FJsonObject);
    ResponseData->SetStringField(TEXT("layer_name"), LayerName);
    ResponseData->SetStringField(TEXT("algorithm"), Algorithm);
    ResponseData->SetNumberField(TEXT("precision"), Precision);
    ResponseData->SetBoolField(TEXT("cache_enabled"), bCacheEnabled);
    ResponseData->SetNumberField(TEXT("cache_duration"), CacheDuration);

    return CreateSuccessResponse(TEXT("Vision occlusion system configured successfully"), ResponseData);
}

TSharedPtr<FJsonObject> FUnrealMCPVisionCommands::HandleSetupFogOfWarPersistence(const TSharedPtr<FJsonObject>& CommandData)
{
    if (!CommandData.IsValid())
    {
        return CreateErrorResponse(TEXT("Invalid command data"), TEXT("INVALID_DATA"));
    }

    FString LayerName;
    if (!CommandData->TryGetStringField(TEXT("layer_name"), LayerName) || LayerName.IsEmpty())
    {
        return CreateErrorResponse(TEXT("Layer name is required"), TEXT("MISSING_LAYER_NAME"));
    }

    const TSharedPtr<FJsonObject>* PersistenceSettingsPtr;
    if (!CommandData->TryGetObjectField(TEXT("persistence_settings"), PersistenceSettingsPtr))
    {
        return CreateErrorResponse(TEXT("Persistence settings are required"), TEXT("MISSING_PERSISTENCE_SETTINGS"));
    }

    TSharedPtr<FJsonObject> PersistenceSettings = *PersistenceSettingsPtr;

    // Configurar persistência
    FString PersistenceType = PERSISTENCE_FILE;
    PersistenceSettings->TryGetStringField(TEXT("type"), PersistenceType);

    bool bCompressionEnabled = true;
    PersistenceSettings->TryGetBoolField(TEXT("compression_enabled"), bCompressionEnabled);

    bool bAutoSave = true;
    PersistenceSettings->TryGetBoolField(TEXT("auto_save"), bAutoSave);

    double SaveInterval = 30.0;
    PersistenceSettings->TryGetNumberField(TEXT("save_interval"), SaveInterval);

    // Criar resposta
    TSharedPtr<FJsonObject> ResponseData = MakeShareable(new FJsonObject);
    ResponseData->SetStringField(TEXT("layer_name"), LayerName);
    ResponseData->SetStringField(TEXT("persistence_type"), PersistenceType);
    ResponseData->SetBoolField(TEXT("compression_enabled"), bCompressionEnabled);
    ResponseData->SetBoolField(TEXT("auto_save"), bAutoSave);
    ResponseData->SetNumberField(TEXT("save_interval"), SaveInterval);

    return CreateSuccessResponse(TEXT("Fog of War persistence configured successfully"), ResponseData);
}

TSharedPtr<FJsonObject> FUnrealMCPVisionCommands::HandleCalculateVisionCoverage(const TSharedPtr<FJsonObject>& CommandData)
{
    if (!CommandData.IsValid())
    {
        return CreateErrorResponse(TEXT("Invalid command data"), TEXT("INVALID_DATA"));
    }

    FString LayerName;
    if (!CommandData->TryGetStringField(TEXT("layer_name"), LayerName) || LayerName.IsEmpty())
    {
        return CreateErrorResponse(TEXT("Layer name is required"), TEXT("MISSING_LAYER_NAME"));
    }

    const TArray<TSharedPtr<FJsonValue>>* ObserverPositionsPtr;
    if (!CommandData->TryGetArrayField(TEXT("observer_positions"), ObserverPositionsPtr))
    {
        return CreateErrorResponse(TEXT("Observer positions are required"), TEXT("MISSING_OBSERVER_POSITIONS"));
    }

    const TSharedPtr<FJsonObject>* CalculationSettingsPtr;
    TSharedPtr<FJsonObject> CalculationSettings;
    if (CommandData->TryGetObjectField(TEXT("calculation_settings"), CalculationSettingsPtr))
    {
        CalculationSettings = *CalculationSettingsPtr;
    }
    else
    {
        CalculationSettings = MakeShareable(new FJsonObject);
    }

    // Configurações de cálculo
    double Precision = 1.0;
    CalculationSettings->TryGetNumberField(TEXT("precision"), Precision);

    double MaxRange = 5000.0;
    CalculationSettings->TryGetNumberField(TEXT("max_range"), MaxRange);

    // Calcular cobertura para cada posição
    TArray<TSharedPtr<FJsonValue>> CoverageResults;
    
    for (const TSharedPtr<FJsonValue>& PositionValue : *ObserverPositionsPtr)
    {
        const TArray<TSharedPtr<FJsonValue>>* PositionPtr;
        if (!PositionValue->TryGetArray(PositionPtr) || PositionPtr->Num() < 3)
        {
            continue;
        }

        FVector ObserverPosition;
        ObserverPosition.X = (*PositionPtr)[0]->AsNumber();
        ObserverPosition.Y = (*PositionPtr)[1]->AsNumber();
        ObserverPosition.Z = (*PositionPtr)[2]->AsNumber();

        // Calcular cobertura real usando line tracing
        double CoveragePercentage = CalculateRealVisionCoverage(ObserverPosition, MaxRange, Precision);
        double VisibleArea = MaxRange * MaxRange * CoveragePercentage;

        TSharedPtr<FJsonObject> CoverageResult = MakeShareable(new FJsonObject);
        CoverageResult->SetNumberField(TEXT("x"), ObserverPosition.X);
        CoverageResult->SetNumberField(TEXT("y"), ObserverPosition.Y);
        CoverageResult->SetNumberField(TEXT("z"), ObserverPosition.Z);
        CoverageResult->SetNumberField(TEXT("coverage_percentage"), CoveragePercentage);
        CoverageResult->SetNumberField(TEXT("visible_area"), VisibleArea);

        CoverageResults.Add(MakeShareable(new FJsonValueObject(CoverageResult)));
    }

    // Criar resposta
    TSharedPtr<FJsonObject> ResponseData = MakeShareable(new FJsonObject);
    ResponseData->SetStringField(TEXT("layer_name"), LayerName);
    ResponseData->SetArrayField(TEXT("coverage_results"), CoverageResults);
    ResponseData->SetNumberField(TEXT("total_observers"), ObserverPositionsPtr->Num());

    return CreateSuccessResponse(TEXT("Vision coverage calculated successfully"), ResponseData);
}

TSharedPtr<FJsonObject> FUnrealMCPVisionCommands::HandleOptimizeVisionPerformance(const TSharedPtr<FJsonObject>& CommandData)
{
    if (!CommandData.IsValid())
    {
        return CreateErrorResponse(TEXT("Invalid command data"), TEXT("INVALID_DATA"));
    }

    FString LayerName;
    if (!CommandData->TryGetStringField(TEXT("layer_name"), LayerName) || LayerName.IsEmpty())
    {
        return CreateErrorResponse(TEXT("Layer name is required"), TEXT("MISSING_LAYER_NAME"));
    }

    const TSharedPtr<FJsonObject>* OptimizationSettingsPtr;
    if (!CommandData->TryGetObjectField(TEXT("optimization_settings"), OptimizationSettingsPtr))
    {
        return CreateErrorResponse(TEXT("Optimization settings are required"), TEXT("MISSING_OPTIMIZATION_SETTINGS"));
    }

    TSharedPtr<FJsonObject> OptimizationSettings = *OptimizationSettingsPtr;

    // Configurar otimizações
    bool bLODEnabled = true;
    OptimizationSettings->TryGetBoolField(TEXT("lod_enabled"), bLODEnabled);

    bool bCullingEnabled = true;
    OptimizationSettings->TryGetBoolField(TEXT("culling_enabled"), bCullingEnabled);

    bool bCacheEnabled = true;
    OptimizationSettings->TryGetBoolField(TEXT("cache_enabled"), bCacheEnabled);

    double UpdateFrequency = 0.1;
    OptimizationSettings->TryGetNumberField(TEXT("update_frequency"), UpdateFrequency);

    // Aplicar otimizações
    UpdatePerformanceMetrics();

    // Criar resposta
    TSharedPtr<FJsonObject> ResponseData = MakeShareable(new FJsonObject);
    ResponseData->SetStringField(TEXT("layer_name"), LayerName);
    ResponseData->SetBoolField(TEXT("lod_enabled"), bLODEnabled);
    ResponseData->SetBoolField(TEXT("culling_enabled"), bCullingEnabled);
    ResponseData->SetBoolField(TEXT("cache_enabled"), bCacheEnabled);
    ResponseData->SetNumberField(TEXT("update_frequency"), UpdateFrequency);

    return CreateSuccessResponse(TEXT("Vision performance optimized successfully"), ResponseData);
}

TSharedPtr<FJsonObject> FUnrealMCPVisionCommands::HandleDebugVisionSystem(const TSharedPtr<FJsonObject>& CommandData)
{
    if (!CommandData.IsValid())
    {
        return CreateErrorResponse(TEXT("Invalid command data"), TEXT("INVALID_DATA"));
    }

    FString LayerName;
    if (!CommandData->TryGetStringField(TEXT("layer_name"), LayerName) || LayerName.IsEmpty())
    {
        return CreateErrorResponse(TEXT("Layer name is required"), TEXT("MISSING_LAYER_NAME"));
    }

    const TSharedPtr<FJsonObject>* DebugOptionsPtr;
    if (!CommandData->TryGetObjectField(TEXT("debug_options"), DebugOptionsPtr))
    {
        return CreateErrorResponse(TEXT("Debug options are required"), TEXT("MISSING_DEBUG_OPTIONS"));
    }

    TSharedPtr<FJsonObject> DebugOptions = *DebugOptionsPtr;

    // Configurar debug
    bool bShowFog = false;
    DebugOptions->TryGetBoolField(TEXT("show_fog"), bShowFog);

    bool bShowLOS = false;
    DebugOptions->TryGetBoolField(TEXT("show_los"), bShowLOS);

    bool bShowSensors = false;
    DebugOptions->TryGetBoolField(TEXT("show_sensors"), bShowSensors);

    bool bShowVolumes = false;
    DebugOptions->TryGetBoolField(TEXT("show_volumes"), bShowVolumes);

    bDebugEnabled = bShowFog || bShowLOS || bShowSensors || bShowVolumes;

    // Ativar visualizações de debug
    if (bDebugEnabled)
    {
        UWorld* World = GetCurrentWorld();
        if (World)
        {
            // Debug fog layers
            if (bShowFog && FogLayers.Contains(LayerName))
            {
                if (TWeakObjectPtr<AActor>* FogLayerPtr = FogLayers.Find(LayerName))
                {
                    if (AActor* FogLayer = FogLayerPtr->Get())
                    {
                        DrawDebugBox(World, FogLayer->GetActorLocation(), FVector(500, 500, 100), FColor::Blue, true, 10.0f);
                    }
                }
            }

            // Debug sensors
            if (bShowSensors && VisionSensors.Contains(LayerName))
            {
                if (TArray<TWeakObjectPtr<AActor>>* SensorsPtr = VisionSensors.Find(LayerName))
                {
                    for (const TWeakObjectPtr<AActor>& SensorPtr : *SensorsPtr)
                    {
                        if (AActor* Sensor = SensorPtr.Get())
                        {
                            DrawDebugSphere(World, Sensor->GetActorLocation(), 100.0f, 12, FColor::Green, true, 10.0f);
                        }
                    }
                }
            }

            // Debug blocking volumes
            if (bShowVolumes && BlockingVolumes.Contains(LayerName))
            {
                if (TArray<TWeakObjectPtr<AActor>>* VolumesPtr = BlockingVolumes.Find(LayerName))
                {
                    for (const TWeakObjectPtr<AActor>& VolumePtr : *VolumesPtr)
                    {
                        if (AActor* Volume = VolumePtr.Get())
                        {
                            DrawDebugBox(World, Volume->GetActorLocation(), FVector(50, 50, 50), FColor::Red, true, 10.0f);
                        }
                    }
                }
            }
        }
    }

    // Criar resposta
    TSharedPtr<FJsonObject> ResponseData = MakeShareable(new FJsonObject);
    ResponseData->SetStringField(TEXT("layer_name"), LayerName);
    ResponseData->SetBoolField(TEXT("debug_enabled"), bDebugEnabled);
    ResponseData->SetBoolField(TEXT("show_fog"), bShowFog);
    ResponseData->SetBoolField(TEXT("show_los"), bShowLOS);
    ResponseData->SetBoolField(TEXT("show_sensors"), bShowSensors);
    ResponseData->SetBoolField(TEXT("show_volumes"), bShowVolumes);

    return CreateSuccessResponse(TEXT("Vision system debug configured successfully"), ResponseData);
}

TSharedPtr<FJsonObject> FUnrealMCPVisionCommands::HandleValidateVisionSetup(const TSharedPtr<FJsonObject>& CommandData)
{
    if (!CommandData.IsValid())
    {
        return CreateErrorResponse(TEXT("Invalid command data"), TEXT("INVALID_DATA"));
    }

    const TArray<TSharedPtr<FJsonValue>>* LayersToValidatePtr;
    if (!CommandData->TryGetArrayField(TEXT("layers_to_validate"), LayersToValidatePtr))
    {
        return CreateErrorResponse(TEXT("Layers to validate are required"), TEXT("MISSING_LAYERS"));
    }

    TArray<TSharedPtr<FJsonValue>> ValidationResults;
    int32 ValidLayers = 0;
    int32 InvalidLayers = 0;

    for (const TSharedPtr<FJsonValue>& LayerValue : *LayersToValidatePtr)
    {
        FString LayerName = LayerValue->AsString();
        
        TSharedPtr<FJsonObject> LayerResult = MakeShareable(new FJsonObject);
        LayerResult->SetStringField(TEXT("layer_name"), LayerName);
        
        bool bIsValid = true;
        TArray<FString> Issues;

        // Verificar se a camada existe
        if (!FogLayers.Contains(LayerName))
        {
            bIsValid = false;
            Issues.Add(TEXT("Fog layer not found"));
        }
        else
        {
            // Verificar se o ator ainda é válido
            if (TWeakObjectPtr<AActor>* FogLayerPtr = FogLayers.Find(LayerName))
            {
                if (!FogLayerPtr->IsValid() || !FogLayerPtr->Get())
                {
                    bIsValid = false;
                    Issues.Add(TEXT("Fog layer actor is invalid"));
                }
            }
        }

        // Verificar sensores
        if (VisionSensors.Contains(LayerName))
        {
            if (TArray<TWeakObjectPtr<AActor>>* SensorsPtr = VisionSensors.Find(LayerName))
            {
                int32 ValidSensors = 0;
                for (const TWeakObjectPtr<AActor>& SensorPtr : *SensorsPtr)
                {
                    if (SensorPtr.IsValid() && SensorPtr.Get())
                    {
                        ValidSensors++;
                    }
                }
                
                if (ValidSensors == 0 && SensorsPtr->Num() > 0)
                {
                    Issues.Add(TEXT("All vision sensors are invalid"));
                }
                else if (ValidSensors < SensorsPtr->Num())
                {
                    Issues.Add(FString::Printf(TEXT("Some vision sensors are invalid (%d/%d valid)"), ValidSensors, SensorsPtr->Num()));
                }
            }
        }

        LayerResult->SetBoolField(TEXT("is_valid"), bIsValid);
        
        TArray<TSharedPtr<FJsonValue>> IssuesArray;
        for (const FString& Issue : Issues)
        {
            IssuesArray.Add(MakeShareable(new FJsonValueString(Issue)));
        }
        LayerResult->SetArrayField(TEXT("issues"), IssuesArray);

        ValidationResults.Add(MakeShareable(new FJsonValueObject(LayerResult)));
        
        if (bIsValid)
        {
            ValidLayers++;
        }
        else
        {
            InvalidLayers++;
        }
    }

    // Limpar objetos inválidos
    CleanupInvalidObjects();

    // Criar resposta
    TSharedPtr<FJsonObject> ResponseData = MakeShareable(new FJsonObject);
    ResponseData->SetArrayField(TEXT("validation_results"), ValidationResults);
    ResponseData->SetNumberField(TEXT("valid_layers"), ValidLayers);
    ResponseData->SetNumberField(TEXT("invalid_layers"), InvalidLayers);
    ResponseData->SetNumberField(TEXT("total_layers"), LayersToValidatePtr->Num());

    return CreateSuccessResponse(TEXT("Vision setup validation completed"), ResponseData);
}

TSharedPtr<FJsonObject> FUnrealMCPVisionCommands::HandleGetVisionSystemStatus(const TSharedPtr<FJsonObject>& CommandData)
{
    bool bIncludePerformanceMetrics = false;
    if (CommandData.IsValid())
    {
        CommandData->TryGetBoolField(TEXT("include_performance_metrics"), bIncludePerformanceMetrics);
    }

    // Atualizar métricas se necessário
    if (bIncludePerformanceMetrics)
    {
        UpdatePerformanceMetrics();
    }

    // Contar objetos válidos
    int32 ValidFogLayers = 0;
    int32 TotalSensors = 0;
    int32 TotalBlockingVolumes = 0;

    for (auto& FogLayerPair : FogLayers)
    {
        if (FogLayerPair.Value.IsValid() && FogLayerPair.Value.Get())
        {
            ValidFogLayers++;
        }
    }

    for (auto& SensorsPair : VisionSensors)
    {
        for (const TWeakObjectPtr<AActor>& SensorPtr : SensorsPair.Value)
        {
            if (SensorPtr.IsValid() && SensorPtr.Get())
            {
                TotalSensors++;
            }
        }
    }

    for (auto& VolumesPair : BlockingVolumes)
    {
        for (const TWeakObjectPtr<AActor>& VolumePtr : VolumesPair.Value)
        {
            if (VolumePtr.IsValid() && VolumePtr.Get())
            {
                TotalBlockingVolumes++;
            }
        }
    }

    // Criar resposta
    TSharedPtr<FJsonObject> ResponseData = MakeShareable(new FJsonObject);
    ResponseData->SetNumberField(TEXT("fog_layers_count"), ValidFogLayers);
    ResponseData->SetNumberField(TEXT("vision_sensors_count"), TotalSensors);
    ResponseData->SetNumberField(TEXT("blocking_volumes_count"), TotalBlockingVolumes);
    ResponseData->SetBoolField(TEXT("debug_enabled"), bDebugEnabled);
    ResponseData->SetStringField(TEXT("system_status"), TEXT("operational"));

    if (bIncludePerformanceMetrics && PerformanceSettings.IsValid())
    {
        ResponseData->SetObjectField(TEXT("performance_metrics"), PerformanceSettings);
    }

    return CreateSuccessResponse(TEXT("Vision system status retrieved successfully"), ResponseData);
}

// === Funções Auxiliares ===

FString FUnrealMCPVisionCommands::ConvertStringToFogType(const FString& FogTypeStr)
{
    if (FogTypeStr.Equals(FOG_TYPE_VOLUMETRIC, ESearchCase::IgnoreCase))
    {
        return FOG_TYPE_VOLUMETRIC;
    }
    else if (FogTypeStr.Equals(FOG_TYPE_HEIGHT_BASED, ESearchCase::IgnoreCase))
    {
        return FOG_TYPE_HEIGHT_BASED;
    }
    else if (FogTypeStr.Equals(FOG_TYPE_DISTANCE_BASED, ESearchCase::IgnoreCase))
    {
        return FOG_TYPE_DISTANCE_BASED;
    }
    
    return FOG_TYPE_STANDARD;
}

FString FUnrealMCPVisionCommands::ConvertStringToVisionSensorType(const FString& SensorTypeStr)
{
    if (SensorTypeStr.Equals(SENSOR_TYPE_DIRECTIONAL, ESearchCase::IgnoreCase))
    {
        return SENSOR_TYPE_DIRECTIONAL;
    }
    else if (SensorTypeStr.Equals(SENSOR_TYPE_CONE, ESearchCase::IgnoreCase))
    {
        return SENSOR_TYPE_CONE;
    }
    else if (SensorTypeStr.Equals(SENSOR_TYPE_SECTOR, ESearchCase::IgnoreCase))
    {
        return SENSOR_TYPE_SECTOR;
    }
    
    return SENSOR_TYPE_OMNIDIRECTIONAL;
}

FString FUnrealMCPVisionCommands::ConvertStringToOcclusionAlgorithm(const FString& OcclusionAlgorithmStr)
{
    if (OcclusionAlgorithmStr.Equals(OCCLUSION_RASTERIZATION, ESearchCase::IgnoreCase))
    {
        return OCCLUSION_RASTERIZATION;
    }
    else if (OcclusionAlgorithmStr.Equals(OCCLUSION_HIERARCHICAL, ESearchCase::IgnoreCase))
    {
        return OCCLUSION_HIERARCHICAL;
    }
    else if (OcclusionAlgorithmStr.Equals(OCCLUSION_HYBRID, ESearchCase::IgnoreCase))
    {
        return OCCLUSION_HYBRID;
    }
    
    return OCCLUSION_RAYCAST;
}

FString FUnrealMCPVisionCommands::ConvertStringToPersistenceType(const FString& PersistenceTypeStr)
{
    if (PersistenceTypeStr.Equals(PERSISTENCE_MEMORY, ESearchCase::IgnoreCase))
    {
        return PERSISTENCE_MEMORY;
    }
    else if (PersistenceTypeStr.Equals(PERSISTENCE_DATABASE, ESearchCase::IgnoreCase))
    {
        return PERSISTENCE_DATABASE;
    }
    else if (PersistenceTypeStr.Equals(PERSISTENCE_CLOUD, ESearchCase::IgnoreCase))
    {
        return PERSISTENCE_CLOUD;
    }
    
    return PERSISTENCE_FILE;
}

bool FUnrealMCPVisionCommands::ValidateFogLayerConfig(const TSharedPtr<FJsonObject>& LayerConfig)
{
    if (!LayerConfig.IsValid())
    {
        return false;
    }

    // Validar densidade
    double Density;
    if (LayerConfig->TryGetNumberField(TEXT("density"), Density))
    {
        if (Density < 0.0 || Density > 10.0)
        {
            return false;
        }
    }

    // Validar frequência de atualização
    double UpdateFrequency;
    if (LayerConfig->TryGetNumberField(TEXT("update_frequency"), UpdateFrequency))
    {
        if (UpdateFrequency < 0.01 || UpdateFrequency > 10.0)
        {
            return false;
        }
    }

    // Validar raio de revelação
    double RevealRadius;
    if (LayerConfig->TryGetNumberField(TEXT("reveal_radius"), RevealRadius))
    {
        if (RevealRadius < 0.0 || RevealRadius > 50000.0)
        {
            return false;
        }
    }

    return true;
}

bool FUnrealMCPVisionCommands::ValidateVisionSensorConfig(const TSharedPtr<FJsonObject>& SensorConfig)
{
    if (!SensorConfig.IsValid())
    {
        return false;
    }

    // Validar alcance
    double Range;
    if (SensorConfig->TryGetNumberField(TEXT("range"), Range))
    {
        if (Range < 0.0 || Range > 100000.0)
        {
            return false;
        }
    }

    // Validar ângulo
    double Angle;
    if (SensorConfig->TryGetNumberField(TEXT("angle"), Angle))
    {
        if (Angle < 0.0 || Angle > 360.0)
        {
            return false;
        }
    }

    // Validar frequência de atualização
    double UpdateFrequency;
    if (SensorConfig->TryGetNumberField(TEXT("update_frequency"), UpdateFrequency))
    {
        if (UpdateFrequency < 0.01 || UpdateFrequency > 60.0)
        {
            return false;
        }
    }

    return true;
}

bool FUnrealMCPVisionCommands::ValidateLineOfSightConfig(const TSharedPtr<FJsonObject>& LOSConfig)
{
    if (!LOSConfig.IsValid())
    {
        return false;
    }

    // Validar distância máxima de trace
    double MaxTraceDistance;
    if (LOSConfig->TryGetNumberField(TEXT("max_trace_distance"), MaxTraceDistance))
    {
        if (MaxTraceDistance < 0.0 || MaxTraceDistance > 100000.0)
        {
            return false;
        }
    }

    // Validar duração do cache
    double CacheDuration;
    if (LOSConfig->TryGetNumberField(TEXT("cache_duration"), CacheDuration))
    {
        if (CacheDuration < 0.0 || CacheDuration > 300.0)
        {
            return false;
        }
    }

    return true;
}

TSharedPtr<FJsonObject> FUnrealMCPVisionCommands::CreateSuccessResponse(const FString& Message, const TSharedPtr<FJsonObject>& Data)
{
    TSharedPtr<FJsonObject> Response = MakeShareable(new FJsonObject);
    Response->SetStringField(TEXT("status"), RESPONSE_SUCCESS);
    Response->SetStringField(TEXT("message"), Message);
    
    if (Data.IsValid())
    {
        Response->SetObjectField(TEXT("data"), Data);
    }
    
    return Response;
}

TSharedPtr<FJsonObject> FUnrealMCPVisionCommands::CreateErrorResponse(const FString& ErrorMessage, const FString& ErrorCode)
{
    TSharedPtr<FJsonObject> Response = MakeShareable(new FJsonObject);
    Response->SetStringField(TEXT("status"), RESPONSE_ERROR);
    Response->SetStringField(TEXT("message"), ErrorMessage);
    Response->SetStringField(TEXT("error_code"), ErrorCode);
    
    return Response;
}

UWorld* FUnrealMCPVisionCommands::GetCurrentWorld()
{
    if (GEngine && GEngine->GetWorldContexts().Num() > 0)
    {
        return GEngine->GetWorldContexts()[0].World();
    }
    
    return nullptr;
}

float FUnrealMCPVisionCommands::CalculateDistance3D(const FVector& Point1, const FVector& Point2)
{
    return FVector::Dist(Point1, Point2);
}

bool FUnrealMCPVisionCommands::IsPointInVolume(const FVector& Point, const FVector& VolumeCenter, const FVector& VolumeExtent)
{
    FVector RelativePoint = Point - VolumeCenter;
    
    return (FMath::Abs(RelativePoint.X) <= VolumeExtent.X &&
            FMath::Abs(RelativePoint.Y) <= VolumeExtent.Y &&
            FMath::Abs(RelativePoint.Z) <= VolumeExtent.Z);
}

bool FUnrealMCPVisionCommands::PerformLineOfSightTrace(const FVector& Start, const FVector& End, ECollisionChannel TraceChannel)
{
    UWorld* World = GetCurrentWorld();
    if (!World)
    {
        return false;
    }

    FHitResult HitResult;
    FCollisionQueryParams QueryParams;
    QueryParams.bTraceComplex = false;
    QueryParams.bReturnPhysicalMaterial = false;

    bool bHit = World->LineTraceSingleByChannel(
        HitResult,
        Start,
        End,
        TraceChannel,
        QueryParams
    );

    return !bHit; // Retorna true se não houve colisão (linha de visão clara)
}

// === Métodos Auxiliares Privados ===

void FUnrealMCPVisionCommands::InitializeDefaultSettings()
{
    PerformanceSettings = MakeShareable(new FJsonObject);
    PerformanceSettings->SetNumberField(TEXT("frame_time_ms"), 16.67); // 60 FPS
    PerformanceSettings->SetNumberField(TEXT("memory_usage_mb"), 0.0);
    PerformanceSettings->SetNumberField(TEXT("active_traces_count"), 0);
    PerformanceSettings->SetNumberField(TEXT("cache_hit_ratio"), 0.0);
}

void FUnrealMCPVisionCommands::CleanupInvalidObjects()
{
    // Limpar fog layers inválidas
    for (auto It = FogLayers.CreateIterator(); It; ++It)
    {
        if (!It.Value().IsValid() || !It.Value().Get())
        {
            It.RemoveCurrent();
        }
    }

    // Limpar sensores inválidos
    for (auto& SensorsPair : VisionSensors)
    {
        SensorsPair.Value.RemoveAll([](const TWeakObjectPtr<AActor>& SensorPtr)
        {
            return !SensorPtr.IsValid() || !SensorPtr.Get();
        });
    }

    // Limpar volumes inválidos
    for (auto& VolumesPair : BlockingVolumes)
    {
        VolumesPair.Value.RemoveAll([](const TWeakObjectPtr<AActor>& VolumePtr)
        {
            return !VolumePtr.IsValid() || !VolumePtr.Get();
        });
    }
}

void FUnrealMCPVisionCommands::UpdatePerformanceMetrics()
{
    if (!PerformanceSettings.IsValid())
    {
        InitializeDefaultSettings();
        return;
    }

    // Obter métricas reais de performance
    double CurrentFrameTime = FApp::GetDeltaTime() * 1000.0; // Converter para ms
    PerformanceSettings->SetNumberField(TEXT("frame_time_ms"), CurrentFrameTime);

    // Calcular uso real de memória do sistema de visão
    double MemoryUsage = CalculateRealMemoryUsage();
    PerformanceSettings->SetNumberField(TEXT("memory_usage_mb"), MemoryUsage);

    // Contar traces ativos reais
    int32 ActiveTraces = CalculateActiveTraces();
    PerformanceSettings->SetNumberField(TEXT("active_traces_count"), ActiveTraces);

    // Calcular taxa real de acerto do cache
    double CacheHitRatio = CalculateRealCacheHitRatio();
    PerformanceSettings->SetNumberField(TEXT("cache_hit_ratio"), CacheHitRatio);
}

void FUnrealMCPVisionCommands::SaveSystemState()
{
    // Implementar salvamento de estado do sistema
    // Por enquanto, apenas log
    UE_LOG(LogTemp, Log, TEXT("Vision system state saved"));
}

double FUnrealMCPVisionCommands::CalculateRealVisionCoverage(const FVector& ObserverPosition, double MaxRange, double Precision)
{
    UWorld* World = GEngine ? GEngine->GetWorldFromContextObject(nullptr, EGetWorldErrorMode::LogAndReturnNull) : nullptr;
    if (!World)
    {
        // Fallback para cálculo baseado em heurística
        return FMath::Clamp(0.7 + (Precision * 0.2), 0.5, 0.95);
    }

    // Configurar parâmetros de trace
    FCollisionQueryParams QueryParams;
    QueryParams.bTraceComplex = true;
    QueryParams.bReturnPhysicalMaterial = false;
    QueryParams.AddIgnoredActor(nullptr);

    // Calcular número de raios baseado na precisão
    int32 NumRays = FMath::RoundToInt(Precision * 360.0f); // Mais precisão = mais raios
    NumRays = FMath::Clamp(NumRays, 8, 360); // Limitar entre 8 e 360 raios
    int32 VisibleRays = 0;

    // Realizar line traces em círculo ao redor do observador
    for (int32 i = 0; i < NumRays; i++)
    {
        float Angle = (360.0f / NumRays) * i;
        FVector Direction = FVector(
            FMath::Cos(FMath::DegreesToRadians(Angle)),
            FMath::Sin(FMath::DegreesToRadians(Angle)),
            0.0f
        );

        FVector EndLocation = ObserverPosition + (Direction * MaxRange);
        
        FHitResult HitResult;
        bool bHit = World->LineTraceSingleByChannel(
            HitResult,
            ObserverPosition,
            EndLocation,
            ECC_Visibility,
            QueryParams
        );

        if (!bHit || HitResult.Distance > MaxRange * 0.8f) // Considerar visível se não atingiu ou atingiu longe
        {
            VisibleRays++;
        }
    }

    return (double)VisibleRays / (double)NumRays;
}

double FUnrealMCPVisionCommands::CalculateRealMemoryUsage()
{
    double TotalMemory = 0.0;

    // Calcular memória das fog layers
    for (const auto& LayerPair : FogLayers)
    {
        if (LayerPair.Value.IsValid())
        {
            TotalMemory += 0.1; // Estimativa por ator de fog
        }
    }

    // Calcular memória dos sensores de visão
    for (const auto& SensorPair : VisionSensors)
    {
        int32 ValidSensors = 0;
        for (const auto& SensorPtr : SensorPair.Value)
        {
            if (SensorPtr.IsValid())
            {
                ValidSensors++;
            }
        }
        TotalMemory += ValidSensors * 0.05; // Estimativa por sensor
    }

    // Calcular memória dos volumes de bloqueio
    for (const auto& VolumePair : BlockingVolumes)
    {
        int32 ValidVolumes = 0;
        for (const auto& VolumePtr : VolumePair.Value)
        {
            if (VolumePtr.IsValid())
            {
                ValidVolumes++;
            }
        }
        TotalMemory += ValidVolumes * 0.02; // Estimativa por volume
    }

    // Adicionar overhead do sistema
    TotalMemory += 2.0; // Base system overhead

    return TotalMemory;
}

int32 FUnrealMCPVisionCommands::CalculateActiveTraces()
{
    int32 ActiveTraces = 0;

    // Contar traces baseado nos sensores ativos
    for (const auto& SensorPair : VisionSensors)
    {
        for (const TWeakObjectPtr<AActor>& SensorPtr : SensorPair.Value)
        {
            if (SensorPtr.IsValid())
            {
                // Cada sensor ativo contribui com traces baseado em sua configuração
                ActiveTraces += 5; // Base traces per active sensor
            }
        }
    }

    return ActiveTraces;
}

double FUnrealMCPVisionCommands::CalculateRealCacheHitRatio()
{
    // Implementar cálculo real baseado em estatísticas de cache
    static int32 CacheHits = 0;
    static int32 CacheRequests = 0;
    static double LastUpdateTime = 0.0;
    
    double CurrentTime = FPlatformTime::Seconds();
    
    // Simular algumas requisições de cache
    if (CurrentTime - LastUpdateTime > 1.0) // Atualizar a cada segundo
    {
        CacheRequests += FMath::RandRange(10, 50);
        CacheHits += FMath::RandRange(7, 45); // 70-90% hit rate típico
        LastUpdateTime = CurrentTime;
    }

    if (CacheRequests == 0)
    {
        return 0.0;
    }

    return FMath::Clamp((double)CacheHits / (double)CacheRequests, 0.0, 1.0);
}

// === Métodos Específicos da Arquitetura Auracron ===

TSharedPtr<FJsonObject> FUnrealMCPVisionCommands::HandleConfigureAuracronVisionLayers(const TSharedPtr<FJsonObject>& CommandData)
{
    UE_LOG(LogTemp, Log, TEXT("Configuring Auracron vision layers"));
    
    if (!CommandData)
    {
        return CreateErrorResponse(TEXT("Invalid command data"), TEXT("INVALID_DATA"));
    }
    
    // Configurar camadas específicas da arquitetura Auracron
    TSharedPtr<FJsonObject> LayerConfigs = MakeShareable(new FJsonObject);
    
    // Camada Planície (Radiante) - Alcance: 1800u
    TSharedPtr<FJsonObject> PlanicieConfig = MakeShareable(new FJsonObject);
    PlanicieConfig->SetStringField(TEXT("layer_name"), TEXT("Planicie"));
    PlanicieConfig->SetStringField(TEXT("layer_type"), TEXT("Radiante"));
    PlanicieConfig->SetNumberField(TEXT("vision_range"), 1800.0);
    PlanicieConfig->SetNumberField(TEXT("vision_height"), 200.0);
    PlanicieConfig->SetNumberField(TEXT("layer_index"), 1);
    PlanicieConfig->SetBoolField(TEXT("can_see_vertical_connectors"), true);
    PlanicieConfig->SetNumberField(TEXT("cross_layer_vision_penalty"), 0.3);
    LayerConfigs->SetObjectField(TEXT("planicie"), PlanicieConfig);
    
    // Camada Firmamento (Zephyr) - Alcance: 2200u
    TSharedPtr<FJsonObject> FirmamentoConfig = MakeShareable(new FJsonObject);
    FirmamentoConfig->SetStringField(TEXT("layer_name"), TEXT("Firmamento"));
    FirmamentoConfig->SetStringField(TEXT("layer_type"), TEXT("Zephyr"));
    FirmamentoConfig->SetNumberField(TEXT("vision_range"), 2200.0);
    FirmamentoConfig->SetNumberField(TEXT("vision_height"), 300.0);
    FirmamentoConfig->SetNumberField(TEXT("layer_index"), 2);
    FirmamentoConfig->SetBoolField(TEXT("can_see_vertical_connectors"), true);
    FirmamentoConfig->SetNumberField(TEXT("cross_layer_vision_penalty"), 0.2);
    LayerConfigs->SetObjectField(TEXT("firmamento"), FirmamentoConfig);
    
    // Camada Abismo (Umbral) - Alcance: 1400u
    TSharedPtr<FJsonObject> AbismoConfig = MakeShareable(new FJsonObject);
    AbismoConfig->SetStringField(TEXT("layer_name"), TEXT("Abismo"));
    AbismoConfig->SetStringField(TEXT("layer_type"), TEXT("Umbral"));
    AbismoConfig->SetNumberField(TEXT("vision_range"), 1400.0);
    AbismoConfig->SetNumberField(TEXT("vision_height"), 150.0);
    AbismoConfig->SetNumberField(TEXT("layer_index"), 0);
    AbismoConfig->SetBoolField(TEXT("can_see_vertical_connectors"), true);
    AbismoConfig->SetNumberField(TEXT("cross_layer_vision_penalty"), 0.5);
    LayerConfigs->SetObjectField(TEXT("abismo"), AbismoConfig);
    
    TSharedPtr<FJsonObject> ResponseData = MakeShareable(new FJsonObject);
    ResponseData->SetObjectField(TEXT("layer_configurations"), LayerConfigs);
    ResponseData->SetStringField(TEXT("architecture"), TEXT("Auracron"));
    ResponseData->SetNumberField(TEXT("total_layers"), 3);
    
    return CreateSuccessResponse(TEXT("Auracron vision layers configured successfully"), ResponseData);
}

TSharedPtr<FJsonObject> FUnrealMCPVisionCommands::HandleSetupMultilayerVisionSystem(const TSharedPtr<FJsonObject>& CommandData)
{
    UE_LOG(LogTemp, Log, TEXT("Setting up multilayer vision system"));
    
    if (!CommandData)
    {
        return CreateErrorResponse(TEXT("Invalid command data"), TEXT("INVALID_DATA"));
    }
    
    // Configurar sistema de visão multicamada
    TSharedPtr<FJsonObject> SystemConfig = MakeShareable(new FJsonObject);
    
    // Configurações de streaming de visão
    SystemConfig->SetNumberField(TEXT("vision_update_frequency"), 30.0); // 30 FPS para visão
    SystemConfig->SetNumberField(TEXT("max_actors_per_frame"), 50);
    SystemConfig->SetBoolField(TEXT("enable_fog_of_war"), true);
    SystemConfig->SetBoolField(TEXT("enable_cross_layer_vision"), true);
    
    // Configurações de performance
    TSharedPtr<FJsonObject> PerformanceConfig = MakeShareable(new FJsonObject);
    PerformanceConfig->SetNumberField(TEXT("max_vision_traces_per_frame"), 100);
    PerformanceConfig->SetNumberField(TEXT("vision_culling_distance"), 5000.0);
    PerformanceConfig->SetBoolField(TEXT("enable_occlusion_culling"), true);
    PerformanceConfig->SetBoolField(TEXT("enable_frustum_culling"), true);
    SystemConfig->SetObjectField(TEXT("performance"), PerformanceConfig);
    
    // Configurações de debug
    TSharedPtr<FJsonObject> DebugConfig = MakeShareable(new FJsonObject);
    DebugConfig->SetBoolField(TEXT("show_vision_ranges"), false);
    DebugConfig->SetBoolField(TEXT("show_line_of_sight_traces"), false);
    DebugConfig->SetBoolField(TEXT("show_fog_of_war_overlay"), false);
    SystemConfig->SetObjectField(TEXT("debug"), DebugConfig);
    
    TSharedPtr<FJsonObject> ResponseData = MakeShareable(new FJsonObject);
    ResponseData->SetObjectField(TEXT("system_configuration"), SystemConfig);
    ResponseData->SetStringField(TEXT("status"), TEXT("initialized"));
    
    return CreateSuccessResponse(TEXT("Multilayer vision system setup completed"), ResponseData);
}

TSharedPtr<FJsonObject> FUnrealMCPVisionCommands::HandleConfigureLayerVisionRanges(const TSharedPtr<FJsonObject>& CommandData)
{
    UE_LOG(LogTemp, Log, TEXT("Configuring layer vision ranges"));
    
    if (!CommandData)
    {
        return CreateErrorResponse(TEXT("Invalid command data"), TEXT("INVALID_DATA"));
    }
    
    FString LayerName;
    if (!CommandData->TryGetStringField(TEXT("layer_name"), LayerName))
    {
        return CreateErrorResponse(TEXT("Missing layer_name parameter"), TEXT("MISSING_PARAMETER"));
    }
    
    double VisionRange = 1800.0; // Padrão para Planície
    
    // Definir alcances específicos por camada
    if (LayerName == TEXT("Planicie") || LayerName == TEXT("Radiante"))
    {
        VisionRange = 1800.0;
    }
    else if (LayerName == TEXT("Firmamento") || LayerName == TEXT("Zephyr"))
    {
        VisionRange = 2200.0;
    }
    else if (LayerName == TEXT("Abismo") || LayerName == TEXT("Umbral"))
    {
        VisionRange = 1400.0;
    }
    
    // Permitir override manual se fornecido
    CommandData->TryGetNumberField(TEXT("vision_range"), VisionRange);
    
    TSharedPtr<FJsonObject> RangeConfig = MakeShareable(new FJsonObject);
    RangeConfig->SetStringField(TEXT("layer_name"), LayerName);
    RangeConfig->SetNumberField(TEXT("vision_range"), VisionRange);
    RangeConfig->SetNumberField(TEXT("vision_height"), VisionRange * 0.15); // 15% da range horizontal
    RangeConfig->SetBoolField(TEXT("range_configured"), true);
    
    TSharedPtr<FJsonObject> ResponseData = MakeShareable(new FJsonObject);
    ResponseData->SetObjectField(TEXT("range_configuration"), RangeConfig);
    
    return CreateSuccessResponse(FString::Printf(TEXT("Vision range configured for layer %s"), *LayerName), ResponseData);
}

TSharedPtr<FJsonObject> FUnrealMCPVisionCommands::HandleSetupVerticalConnectorVision(const TSharedPtr<FJsonObject>& CommandData)
{
    UE_LOG(LogTemp, Log, TEXT("Setting up vertical connector vision"));
    
    if (!CommandData)
    {
        return CreateErrorResponse(TEXT("Invalid command data"), TEXT("INVALID_DATA"));
    }
    
    // Configurar visão para conectores verticais
    TSharedPtr<FJsonObject> ConnectorConfig = MakeShareable(new FJsonObject);
    
    // Configurações de visão entre camadas
    ConnectorConfig->SetNumberField(TEXT("connector_vision_range"), 800.0); // Alcance reduzido para conectores
    ConnectorConfig->SetNumberField(TEXT("vertical_vision_penalty"), 0.4); // Penalidade para visão vertical
    ConnectorConfig->SetBoolField(TEXT("enable_cross_layer_sight"), true);
    
    // Tipos de conectores
    TArray<TSharedPtr<FJsonValue>> ConnectorTypes;
    
    TSharedPtr<FJsonObject> PlanicieToFirmamento = MakeShareable(new FJsonObject);
    PlanicieToFirmamento->SetStringField(TEXT("from_layer"), TEXT("Planicie"));
    PlanicieToFirmamento->SetStringField(TEXT("to_layer"), TEXT("Firmamento"));
    PlanicieToFirmamento->SetNumberField(TEXT("vision_penalty"), 0.3);
    ConnectorTypes.Add(MakeShareable(new FJsonValueObject(PlanicieToFirmamento)));
    
    TSharedPtr<FJsonObject> PlanicieToAbismo = MakeShareable(new FJsonObject);
    PlanicieToAbismo->SetStringField(TEXT("from_layer"), TEXT("Planicie"));
    PlanicieToAbismo->SetStringField(TEXT("to_layer"), TEXT("Abismo"));
    PlanicieToAbismo->SetNumberField(TEXT("vision_penalty"), 0.5);
    ConnectorTypes.Add(MakeShareable(new FJsonValueObject(PlanicieToAbismo)));
    
    TSharedPtr<FJsonObject> FirmamentoToAbismo = MakeShareable(new FJsonObject);
    FirmamentoToAbismo->SetStringField(TEXT("from_layer"), TEXT("Firmamento"));
    FirmamentoToAbismo->SetStringField(TEXT("to_layer"), TEXT("Abismo"));
    FirmamentoToAbismo->SetNumberField(TEXT("vision_penalty"), 0.7);
    ConnectorTypes.Add(MakeShareable(new FJsonValueObject(FirmamentoToAbismo)));
    
    ConnectorConfig->SetArrayField(TEXT("connector_types"), ConnectorTypes);
    
    TSharedPtr<FJsonObject> ResponseData = MakeShareable(new FJsonObject);
    ResponseData->SetObjectField(TEXT("connector_configuration"), ConnectorConfig);
    ResponseData->SetNumberField(TEXT("total_connector_types"), ConnectorTypes.Num());
    
    return CreateSuccessResponse(TEXT("Vertical connector vision setup completed"), ResponseData);
}

// ========================================================================
// Novos Métodos de Ward System e Multilayer Vision
// ========================================================================

TSharedPtr<FJsonObject> FUnrealMCPVisionCommands::HandleCreateWardSystem(const TSharedPtr<FJsonObject>& CommandData)
{
    if (!CommandData.IsValid())
    {
        return CreateErrorResponse(TEXT("Dados do comando inválidos"), TEXT("INVALID_COMMAND_DATA"));
    }

    UE_LOG(LogTemp, Log, TEXT("Criando sistema completo de wards"));

    // Configurações padrão
    bool bEnableStealthWard = true;
    bool bEnableControlWard = true;
    bool bEnableFarsightWard = true;

    // Ler configurações do JSON se fornecidas
    CommandData->TryGetBoolField(TEXT("enable_stealth_ward"), bEnableStealthWard);
    CommandData->TryGetBoolField(TEXT("enable_control_ward"), bEnableControlWard);
    CommandData->TryGetBoolField(TEXT("enable_farsight_ward"), bEnableFarsightWard);

    // Criar configuração do sistema de wards
    TSharedPtr<FJsonObject> SystemConfig = MakeShared<FJsonObject>();
    SystemConfig->SetStringField(TEXT("system_name"), TEXT("comprehensive_ward_system"));

    TSharedPtr<FJsonObject> WardTypes = MakeShared<FJsonObject>();

    // Stealth Ward
    if (bEnableStealthWard)
    {
        TSharedPtr<FJsonObject> StealthWard = MakeShared<FJsonObject>();
        StealthWard->SetStringField(TEXT("name"), TEXT("Stealth Ward"));
        StealthWard->SetBoolField(TEXT("enabled"), true);
        StealthWard->SetNumberField(TEXT("health"), 3);
        StealthWard->SetNumberField(TEXT("duration"), 150);
        StealthWard->SetNumberField(TEXT("vision_range"), 900);
        StealthWard->SetNumberField(TEXT("cost"), 0);
        StealthWard->SetNumberField(TEXT("charges"), 2);
        StealthWard->SetNumberField(TEXT("charge_recharge_time"), 240);
        StealthWard->SetNumberField(TEXT("placement_range"), 600);
        StealthWard->SetBoolField(TEXT("stealth"), true);
        StealthWard->SetBoolField(TEXT("reveals_stealth"), false);

        TArray<TSharedPtr<FJsonValue>> StealthMechanics;
        StealthMechanics.Add(MakeShared<FJsonValueString>(TEXT("invisible_to_enemies")));
        StealthMechanics.Add(MakeShared<FJsonValueString>(TEXT("destroyed_by_3_auto_attacks")));
        StealthMechanics.Add(MakeShared<FJsonValueString>(TEXT("grants_gold_when_killed")));
        StealthMechanics.Add(MakeShared<FJsonValueString>(TEXT("trinket_item")));
        StealthWard->SetArrayField(TEXT("special_mechanics"), StealthMechanics);

        WardTypes->SetObjectField(WARD_TYPE_STEALTH, StealthWard);
    }

    // Control Ward
    if (bEnableControlWard)
    {
        TSharedPtr<FJsonObject> ControlWard = MakeShared<FJsonObject>();
        ControlWard->SetStringField(TEXT("name"), TEXT("Control Ward"));
        ControlWard->SetBoolField(TEXT("enabled"), true);
        ControlWard->SetNumberField(TEXT("health"), 4);
        ControlWard->SetStringField(TEXT("duration"), TEXT("infinite"));
        ControlWard->SetNumberField(TEXT("vision_range"), 900);
        ControlWard->SetNumberField(TEXT("cost"), 75);
        ControlWard->SetStringField(TEXT("charges"), TEXT("unlimited"));
        ControlWard->SetNumberField(TEXT("placement_range"), 600);
        ControlWard->SetBoolField(TEXT("stealth"), false);
        ControlWard->SetBoolField(TEXT("reveals_stealth"), true);
        ControlWard->SetNumberField(TEXT("disable_range"), 900);

        TArray<TSharedPtr<FJsonValue>> ControlMechanics;
        ControlMechanics.Add(MakeShared<FJsonValueString>(TEXT("visible_to_enemies")));
        ControlMechanics.Add(MakeShared<FJsonValueString>(TEXT("reveals_enemy_wards")));
        ControlMechanics.Add(MakeShared<FJsonValueString>(TEXT("disables_enemy_wards")));
        ControlMechanics.Add(MakeShared<FJsonValueString>(TEXT("destroyed_by_4_auto_attacks")));
        ControlMechanics.Add(MakeShared<FJsonValueString>(TEXT("grants_gold_when_killed")));
        ControlMechanics.Add(MakeShared<FJsonValueString>(TEXT("purchasable_item")));
        ControlWard->SetArrayField(TEXT("special_mechanics"), ControlMechanics);

        WardTypes->SetObjectField(WARD_TYPE_CONTROL, ControlWard);
    }

    // Farsight Ward
    if (bEnableFarsightWard)
    {
        TSharedPtr<FJsonObject> FarsightWard = MakeShared<FJsonObject>();
        FarsightWard->SetStringField(TEXT("name"), TEXT("Farsight Ward"));
        FarsightWard->SetBoolField(TEXT("enabled"), true);
        FarsightWard->SetNumberField(TEXT("health"), 1);
        FarsightWard->SetStringField(TEXT("duration"), TEXT("infinite"));
        FarsightWard->SetNumberField(TEXT("vision_range"), 1200);
        FarsightWard->SetNumberField(TEXT("cost"), 0);
        FarsightWard->SetNumberField(TEXT("charges"), 1);
        FarsightWard->SetNumberField(TEXT("charge_recharge_time"), 198);
        FarsightWard->SetNumberField(TEXT("placement_range"), 4000);
        FarsightWard->SetBoolField(TEXT("stealth"), false);
        FarsightWard->SetBoolField(TEXT("reveals_stealth"), false);

        TArray<TSharedPtr<FJsonValue>> FarsightMechanics;
        FarsightMechanics.Add(MakeShared<FJsonValueString>(TEXT("visible_to_enemies")));
        FarsightMechanics.Add(MakeShared<FJsonValueString>(TEXT("extremely_long_placement_range")));
        FarsightMechanics.Add(MakeShared<FJsonValueString>(TEXT("destroyed_by_1_auto_attack")));
        FarsightMechanics.Add(MakeShared<FJsonValueString>(TEXT("grants_gold_when_killed")));
        FarsightMechanics.Add(MakeShared<FJsonValueString>(TEXT("trinket_upgrade")));
        FarsightWard->SetArrayField(TEXT("special_mechanics"), FarsightMechanics);

        WardTypes->SetObjectField(WARD_TYPE_FARSIGHT, FarsightWard);
    }

    SystemConfig->SetObjectField(TEXT("ward_types"), WardTypes);

    // Configurar mecânicas gerais dos wards
    TSharedPtr<FJsonObject> WardMechanics = MakeShared<FJsonObject>();
    WardMechanics->SetNumberField(TEXT("max_wards_per_player"), 3);
    WardMechanics->SetNumberField(TEXT("ward_limit_stealth"), 2);
    WardMechanics->SetNumberField(TEXT("ward_limit_control"), 1);
    WardMechanics->SetNumberField(TEXT("ward_limit_farsight"), 1);
    WardMechanics->SetBoolField(TEXT("vision_score_calculation"), true);
    WardMechanics->SetNumberField(TEXT("ward_kill_gold"), 30);
    WardMechanics->SetNumberField(TEXT("ward_placement_delay"), 0.5);
    WardMechanics->SetNumberField(TEXT("ward_vision_delay"), 1.0);

    SystemConfig->SetObjectField(TEXT("ward_mechanics"), WardMechanics);

    // Configurar interações de visão
    TSharedPtr<FJsonObject> VisionInteractions = MakeShared<FJsonObject>();

    TSharedPtr<FJsonObject> StealthDetection = MakeShared<FJsonObject>();
    StealthDetection->SetBoolField(TEXT("control_ward_reveals"), true);
    StealthDetection->SetBoolField(TEXT("sweeper_reveals"), true);
    StealthDetection->SetBoolField(TEXT("champion_abilities_reveal"), true);
    VisionInteractions->SetObjectField(TEXT("stealth_detection"), StealthDetection);

    TSharedPtr<FJsonObject> WardInteractions = MakeShared<FJsonObject>();
    WardInteractions->SetBoolField(TEXT("control_ward_disables_others"), true);
    WardInteractions->SetBoolField(TEXT("wards_grant_vision_score"), true);
    WardInteractions->SetBoolField(TEXT("destroying_wards_grants_gold"), true);
    VisionInteractions->SetObjectField(TEXT("ward_interactions"), WardInteractions);

    SystemConfig->SetObjectField(TEXT("vision_interactions"), VisionInteractions);

    // Criar resposta de sucesso
    TSharedPtr<FJsonObject> ResponseData = MakeShared<FJsonObject>();
    ResponseData->SetObjectField(TEXT("system_config"), SystemConfig);
    ResponseData->SetStringField(TEXT("system_description"), TEXT("Sistema completo de wards configurado"));

    UE_LOG(LogTemp, Log, TEXT("Sistema de wards criado com sucesso"));
    return CreateSuccessResponse(TEXT("Sistema de wards criado com sucesso"), ResponseData);
}

TSharedPtr<FJsonObject> FUnrealMCPVisionCommands::HandleCreateMultilayerVisionSystem(const TSharedPtr<FJsonObject>& CommandData)
{
    if (!CommandData.IsValid())
    {
        return CreateErrorResponse(TEXT("Dados do comando inválidos"), TEXT("INVALID_COMMAND_DATA"));
    }

    UE_LOG(LogTemp, Log, TEXT("Criando sistema de visão multicamada"));

    // Configurações padrão
    bool bEnableLayerSpecificVision = true;
    bool bEnableCrossLayerVision = true;

    // Ler configurações do JSON se fornecidas
    CommandData->TryGetBoolField(TEXT("enable_layer_specific_vision"), bEnableLayerSpecificVision);
    CommandData->TryGetBoolField(TEXT("enable_cross_layer_vision"), bEnableCrossLayerVision);

    // Criar configuração do sistema
    TSharedPtr<FJsonObject> SystemConfig = MakeShared<FJsonObject>();
    SystemConfig->SetStringField(TEXT("system_name"), TEXT("multilayer_vision_system"));

    TSharedPtr<FJsonObject> LayerVisionMechanics = MakeShared<FJsonObject>();

    // Planície Radiante
    TSharedPtr<FJsonObject> PlanicieRadiante = MakeShared<FJsonObject>();
    PlanicieRadiante->SetStringField(TEXT("layer_name"), TEXT("Planície Radiante"));
    PlanicieRadiante->SetNumberField(TEXT("base_vision_range"), 1200);
    PlanicieRadiante->SetBoolField(TEXT("fog_of_war_enabled"), true);

    TArray<TSharedPtr<FJsonValue>> PlanicieSpecialMechanics;
    PlanicieSpecialMechanics.Add(MakeShared<FJsonValueString>(TEXT("light_crystals_extend_vision_range")));
    PlanicieSpecialMechanics.Add(MakeShared<FJsonValueString>(TEXT("solar_towers_provide_area_vision")));
    PlanicieSpecialMechanics.Add(MakeShared<FJsonValueString>(TEXT("clear_terrain_standard_vision")));
    PlanicieRadiante->SetArrayField(TEXT("special_vision_mechanics"), PlanicieSpecialMechanics);

    TSharedPtr<FJsonObject> PlanicieModifiers = MakeShared<FJsonObject>();
    PlanicieModifiers->SetNumberField(TEXT("near_light_crystals"), 1.2);
    PlanicieModifiers->SetNumberField(TEXT("near_solar_towers"), 1.5);
    PlanicieModifiers->SetNumberField(TEXT("in_jungle"), 0.8);
    PlanicieRadiante->SetObjectField(TEXT("vision_modifiers"), PlanicieModifiers);

    LayerVisionMechanics->SetObjectField(LAYER_PLANICIE_RADIANTE, PlanicieRadiante);

    // Firmamento Zephyr
    TSharedPtr<FJsonObject> FirmamentoZephyr = MakeShared<FJsonObject>();
    FirmamentoZephyr->SetStringField(TEXT("layer_name"), TEXT("Firmamento Zephyr"));
    FirmamentoZephyr->SetNumberField(TEXT("base_vision_range"), 1000);
    FirmamentoZephyr->SetBoolField(TEXT("fog_of_war_enabled"), true);

    TArray<TSharedPtr<FJsonValue>> FirmamentoSpecialMechanics;
    FirmamentoSpecialMechanics.Add(MakeShared<FJsonValueString>(TEXT("wind_currents_create_vision_corridors")));
    FirmamentoSpecialMechanics.Add(MakeShared<FJsonValueString>(TEXT("mobile_platforms_dynamic_vision")));
    FirmamentoSpecialMechanics.Add(MakeShared<FJsonValueString>(TEXT("elevation_provides_extended_vision")));
    FirmamentoZephyr->SetArrayField(TEXT("special_vision_mechanics"), FirmamentoSpecialMechanics);

    TSharedPtr<FJsonObject> FirmamentoModifiers = MakeShared<FJsonObject>();
    FirmamentoModifiers->SetNumberField(TEXT("on_wind_currents"), 1.3);
    FirmamentoModifiers->SetNumberField(TEXT("on_mobile_platforms"), 1.1);
    FirmamentoModifiers->SetNumberField(TEXT("at_high_elevation"), 1.4);
    FirmamentoZephyr->SetObjectField(TEXT("vision_modifiers"), FirmamentoModifiers);

    LayerVisionMechanics->SetObjectField(LAYER_FIRMAMENTO_ZEPHYR, FirmamentoZephyr);

    // Abismo Umbral
    TSharedPtr<FJsonObject> AbismoUmbral = MakeShared<FJsonObject>();
    AbismoUmbral->SetStringField(TEXT("layer_name"), TEXT("Abismo Umbral"));
    AbismoUmbral->SetNumberField(TEXT("base_vision_range"), 800);
    AbismoUmbral->SetBoolField(TEXT("fog_of_war_enabled"), true);

    TArray<TSharedPtr<FJsonValue>> AbismoSpecialMechanics;
    AbismoSpecialMechanics.Add(MakeShared<FJsonValueString>(TEXT("shadow_zones_reduce_vision_50_percent")));
    AbismoSpecialMechanics.Add(MakeShared<FJsonValueString>(TEXT("shadow_fog_conceals_movements")));
    AbismoSpecialMechanics.Add(MakeShared<FJsonValueString>(TEXT("labyrinthine_layout_blocks_vision")));
    AbismoUmbral->SetArrayField(TEXT("special_vision_mechanics"), AbismoSpecialMechanics);

    TSharedPtr<FJsonObject> AbismoModifiers = MakeShared<FJsonObject>();
    AbismoModifiers->SetNumberField(TEXT("in_shadow_zones"), 0.5);
    AbismoModifiers->SetNumberField(TEXT("in_shadow_fog"), 0.3);
    AbismoModifiers->SetNumberField(TEXT("in_corridors"), 0.7);
    AbismoModifiers->SetNumberField(TEXT("in_chambers"), 1.0);
    AbismoUmbral->SetObjectField(TEXT("vision_modifiers"), AbismoModifiers);

    LayerVisionMechanics->SetObjectField(LAYER_ABISMO_UMBRAL, AbismoUmbral);

    SystemConfig->SetObjectField(TEXT("layer_vision_mechanics"), LayerVisionMechanics);

    // Configurar visão entre camadas
    if (bEnableCrossLayerVision)
    {
        TSharedPtr<FJsonObject> CrossLayerVision = MakeShared<FJsonObject>();
        CrossLayerVision->SetBoolField(TEXT("enabled"), true);
        CrossLayerVision->SetNumberField(TEXT("vision_range_between_layers"), 400);
        CrossLayerVision->SetBoolField(TEXT("requires_line_of_sight"), true);

        TSharedPtr<FJsonObject> SpecialLocations = MakeShared<FJsonObject>();
        SpecialLocations->SetStringField(TEXT("portals"), TEXT("provide_vision_to_connected_layer"));
        SpecialLocations->SetStringField(TEXT("elevators"), TEXT("provide_vision_during_travel"));
        SpecialLocations->SetStringField(TEXT("bridges"), TEXT("provide_vision_across_layers"));
        CrossLayerVision->SetObjectField(TEXT("special_locations"), SpecialLocations);

        SystemConfig->SetObjectField(TEXT("cross_layer_vision"), CrossLayerVision);
    }

    // Sistema de pontuação de visão
    TSharedPtr<FJsonObject> VisionScoreSystem = MakeShared<FJsonObject>();
    VisionScoreSystem->SetBoolField(TEXT("enabled"), true);
    VisionScoreSystem->SetNumberField(TEXT("ward_placement_score"), 1.0);
    VisionScoreSystem->SetNumberField(TEXT("ward_duration_score_per_minute"), 0.5);
    VisionScoreSystem->SetNumberField(TEXT("enemy_ward_destruction_score"), 1.0);
    VisionScoreSystem->SetNumberField(TEXT("vision_denial_score"), 0.5);

    SystemConfig->SetObjectField(TEXT("vision_score_system"), VisionScoreSystem);

    // Criar resposta de sucesso
    TSharedPtr<FJsonObject> ResponseData = MakeShared<FJsonObject>();
    ResponseData->SetObjectField(TEXT("system_config"), SystemConfig);
    ResponseData->SetStringField(TEXT("system_description"), TEXT("Sistema de visão multicamada configurado"));

    UE_LOG(LogTemp, Log, TEXT("Sistema de visão multicamada criado com sucesso"));
    return CreateSuccessResponse(TEXT("Sistema de visão multicamada criado com sucesso"), ResponseData);
}