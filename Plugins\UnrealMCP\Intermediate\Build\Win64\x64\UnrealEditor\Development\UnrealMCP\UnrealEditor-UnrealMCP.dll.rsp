/MANIFEST:NO
/NOLOGO
/DEBUG:FULL
/errorReport:prompt
/MACHINE:x64
/SUBSYSTEM:WINDOWS
/FIXED:No
/NXCOMPAT
/STACK:12000000
/DELAY:UNLOAD
/DLL
/PDBALTPATH:%_PDB%
/d2:-ExtendedWarningInfo
/OPT:NOREF
/OPT:NOICF
/INCREMENTAL:NO
/ignore:4199
/ignore:4099
/ALTERNATENAME:__imp___std_init_once_begin_initialize=__imp_InitOnceBeginInitialize
/ALTERNATENAME:__imp___std_init_once_complete=__imp_InitOnceComplete
/DELAYLOAD:"d3d12.dll"
/DELAYLOAD:"DBGHELP.DLL"
/LIBPATH:"C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/lib/x64"
/LIBPATH:"C:/Program Files (x86)/Windows Kits/10/lib/10.0.26100.0/ucrt/x64"
/LIBPATH:"C:/Program Files (x86)/Windows Kits/10/lib/10.0.26100.0/um/x64"
/NODEFAULTLIB:"LIBCMT"
/NODEFAULTLIB:"LIBCPMT"
/NODEFAULTLIB:"LIBCMTD"
/NODEFAULTLIB:"LIBCPMTD"
/NODEFAULTLIB:"MSVCRTD"
/NODEFAULTLIB:"MSVCPRTD"
/NODEFAULTLIB:"LIBC"
/NODEFAULTLIB:"LIBCP"
/NODEFAULTLIB:"LIBCD"
/NODEFAULTLIB:"LIBCPD"
/FUNCTIONPADMIN:6
/NOIMPLIB
/NOEXP
/NATVIS:"../Plugins/FX/Niagara/Intermediate/Build/Win64/x64/UnrealEditor/Development/Niagara/Niagara.natvis"
/NATVIS:"../Intermediate/Build/Win64/x64/UnrealEditor/Development/MassEntity/MassEntity.natvis"
/NATVIS:"../Plugins/Runtime/StateTree/Intermediate/Build/Win64/x64/UnrealEditor/Development/StateTreeModule/StateTree.natvis"
/NATVIS:"../Plugins/PCG/Intermediate/Build/Win64/x64/UnrealEditor/Development/PCG/PCG.natvis"
/NATVIS:"../Intermediate/Build/Win64/x64/UnrealEditor/Development/IrisCore/IrisCore.natvis"
/NATVIS:"../Intermediate/Build/Win64/x64/UnrealEditor/Development/RenderCore/RenderCore.natvis"
"C:/Game/Auracron/Intermediate/Build/Win64/x64/AuracronEditor/Development/UnrealEd/SharedPCH.UnrealEd.Project.ValApi.ValExpApi.Cpp20.h.obj"
"C:/Game/Auracron/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/Module.UnrealMCP.cpp.obj"
"C:/Game/Auracron/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/MCPServerRunnable.cpp.obj"
"C:/Game/Auracron/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/UnrealMCPBridge.cpp.obj"
"C:/Game/Auracron/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/UnrealMCPModule.cpp.obj"
"C:/Game/Auracron/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/UnrealMCPAICommands.cpp.obj"
"C:/Game/Auracron/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/UnrealMCPAnalyticsTelemetryCommands.cpp.obj"
"C:/Game/Auracron/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/UnrealMCPAudioSystemCommands.cpp.obj"
"C:/Game/Auracron/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/UnrealMCPBackendServicesCommands.cpp.obj"
"C:/Game/Auracron/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/UnrealMCPBlueprintCommands.cpp.obj"
"C:/Game/Auracron/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/UnrealMCPBlueprintNodeCommands.cpp.obj"
"C:/Game/Auracron/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/UnrealMCPChaosPhysicsCommands.cpp.obj"
"C:/Game/Auracron/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/UnrealMCPCloudServicesCommands.cpp.obj"
"C:/Game/Auracron/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/UnrealMCPCollisionCommands.cpp.obj"
"C:/Game/Auracron/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/UnrealMCPCombatMechanicsCommands.cpp.obj"
"C:/Game/Auracron/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/UnrealMCPCommonUtils.cpp.obj"
"C:/Game/Auracron/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/UnrealMCPEditorCommands.cpp.obj"
"C:/Game/Auracron/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/UnrealMCPGamePhasesCommands.cpp.obj"
"C:/Game/Auracron/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/UnrealMCPHardwareDetectionCommands.cpp.obj"
"C:/Game/Auracron/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/UnrealMCPJungleSystemCommands.cpp.obj"
"C:/Game/Auracron/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/UnrealMCPLaneMechanicsCommands.cpp.obj"
"C:/Game/Auracron/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/UnrealMCPMaterialCommands.cpp.obj"
"C:/Game/Auracron/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/UnrealMCPMultilayerMapCommands.cpp.obj"
"C:/Game/Auracron/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/UnrealMCPNetworkCommands.cpp.obj"
"C:/Game/Auracron/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/UnrealMCPNetworkingCommands.cpp.obj"
"C:/Game/Auracron/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/UnrealMCPNiagaraCommands.cpp.obj"
"C:/Game/Auracron/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/UnrealMCPObjectivesStructuresCommands.cpp.obj"
"C:/Game/Auracron/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/UnrealMCPPathfindingCommands.cpp.obj"
"C:/Game/Auracron/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/UnrealMCPPerformanceCommands.cpp.obj"
"C:/Game/Auracron/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/UnrealMCPPlatformCommands.cpp.obj"
"C:/Game/Auracron/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/UnrealMCPProceduralCommands.cpp.obj"
"C:/Game/Auracron/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/UnrealMCPProjectCommands.cpp.obj"
"C:/Game/Auracron/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/UnrealMCPRealmCommands.cpp.obj"
"C:/Game/Auracron/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/UnrealMCPRenderingPipelineCommands.cpp.obj"
"C:/Game/Auracron/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/UnrealMCPSecurityAntiCheatCommands.cpp.obj"
"C:/Game/Auracron/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/UnrealMCPUMGCommands.cpp.obj"
"C:/Game/Auracron/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/UnrealMCPVerticalNavigationCommands.cpp.obj"
"C:/Game/Auracron/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/UnrealMCPVisionCommands.cpp.obj"
"C:/Game/Auracron/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/UnrealMCPWorldPartitionCommands.cpp.obj"
"C:/Game/Auracron/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/Default.rc2.res"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealEd/UnrealEditor-UnrealEd.lib"
"../Plugins/Editor/EditorScriptingUtilities/Intermediate/Build/Win64/x64/UnrealEditor/Development/EditorScriptingUtilities/UnrealEditor-EditorScriptingUtilities.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/EditorSubsystem/UnrealEditor-EditorSubsystem.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/Slate/UnrealEditor-Slate.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/SlateCore/UnrealEditor-SlateCore.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/UMG/UnrealEditor-UMG.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/Kismet/UnrealEditor-Kismet.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/KismetCompiler/UnrealEditor-KismetCompiler.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/BlueprintGraph/UnrealEditor-BlueprintGraph.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/Projects/UnrealEditor-Projects.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/AssetRegistry/UnrealEditor-AssetRegistry.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/PropertyEditor/UnrealEditor-PropertyEditor.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/ToolMenus/UnrealEditor-ToolMenus.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/BlueprintEditorLibrary/UnrealEditor-BlueprintEditorLibrary.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/UMGEditor/UnrealEditor-UMGEditor.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/Core/UnrealEditor-Core.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/CoreUObject/UnrealEditor-CoreUObject.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/Engine/UnrealEditor-Engine.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/InputCore/UnrealEditor-InputCore.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/Networking/UnrealEditor-Networking.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/Sockets/UnrealEditor-Sockets.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/HTTP/UnrealEditor-HTTP.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/Json/UnrealEditor-Json.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/JsonUtilities/UnrealEditor-JsonUtilities.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/DeveloperSettings/UnrealEditor-DeveloperSettings.lib"
"../Plugins/FX/Niagara/Intermediate/Build/Win64/x64/UnrealEditor/Development/Niagara/UnrealEditor-Niagara.lib"
"../Plugins/FX/Niagara/Intermediate/Build/Win64/x64/UnrealEditor/Development/NiagaraEditor/UnrealEditor-NiagaraEditor.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/Landscape/UnrealEditor-Landscape.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/Foliage/UnrealEditor-Foliage.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/NavigationSystem/UnrealEditor-NavigationSystem.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/AIModule/UnrealEditor-AIModule.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/GameplayTasks/UnrealEditor-GameplayTasks.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/MassEntity/UnrealEditor-MassEntity.lib"
"../Plugins/Runtime/MassGameplay/Intermediate/Build/Win64/x64/UnrealEditor/Development/MassMovement/UnrealEditor-MassMovement.lib"
"../Plugins/Runtime/MassGameplay/Intermediate/Build/Win64/x64/UnrealEditor/Development/MassCommon/UnrealEditor-MassCommon.lib"
"../Plugins/Runtime/MassGameplay/Intermediate/Build/Win64/x64/UnrealEditor/Development/MassRepresentation/UnrealEditor-MassRepresentation.lib"
"../Plugins/Runtime/MassGameplay/Intermediate/Build/Win64/x64/UnrealEditor/Development/MassLOD/UnrealEditor-MassLOD.lib"
"../Plugins/Runtime/MassGameplay/Intermediate/Build/Win64/x64/UnrealEditor/Development/MassSimulation/UnrealEditor-MassSimulation.lib"
"../Plugins/Runtime/MassGameplay/Intermediate/Build/Win64/x64/UnrealEditor/Development/MassSpawner/UnrealEditor-MassSpawner.lib"
"../Plugins/Runtime/StateTree/Intermediate/Build/Win64/x64/UnrealEditor/Development/StateTreeModule/UnrealEditor-StateTreeModule.lib"
"../Plugins/Runtime/GameplayAbilities/Intermediate/Build/Win64/x64/UnrealEditor/Development/GameplayAbilities/UnrealEditor-GameplayAbilities.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/GameplayTags/UnrealEditor-GameplayTags.lib"
"../Plugins/Runtime/DataRegistry/Intermediate/Build/Win64/x64/UnrealEditor/Development/DataRegistry/UnrealEditor-DataRegistry.lib"
"../Plugins/PCG/Intermediate/Build/Win64/x64/UnrealEditor/Development/PCG/UnrealEditor-PCG.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/DesktopPlatform/UnrealEditor-DesktopPlatform.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/ClothingSystemRuntimeCommon/UnrealEditor-ClothingSystemRuntimeCommon.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/Chaos/UnrealEditor-Chaos.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/ChaosCore/UnrealEditor-ChaosCore.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/ChaosSolverEngine/UnrealEditor-ChaosSolverEngine.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/PhysicsCore/UnrealEditor-PhysicsCore.lib"
"../Plugins/Experimental/GeometryCollectionPlugin/Intermediate/Build/Win64/x64/UnrealEditor/Development/GeometryCollectionEditor/UnrealEditor-GeometryCollectionEditor.lib"
"../Plugins/Runtime/Metasound/Intermediate/Build/Win64/x64/UnrealEditor/Development/MetasoundEngine/UnrealEditor-MetasoundEngine.lib"
"../Plugins/Runtime/Metasound/Intermediate/Build/Win64/x64/UnrealEditor/Development/MetasoundFrontend/UnrealEditor-MetasoundFrontend.lib"
"../Plugins/Runtime/Metasound/Intermediate/Build/Win64/x64/UnrealEditor/Development/MetasoundGenerator/UnrealEditor-MetasoundGenerator.lib"
"../Plugins/Runtime/ReplicationGraph/Intermediate/Build/Win64/x64/UnrealEditor/Development/ReplicationGraph/UnrealEditor-ReplicationGraph.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/IrisCore/UnrealEditor-IrisCore.lib"
"../Plugins/Online/OnlineSubsystem/Intermediate/Build/Win64/x64/UnrealEditor/Development/OnlineSubsystem/UnrealEditor-OnlineSubsystem.lib"
"../Plugins/Online/OnlineSubsystemUtils/Intermediate/Build/Win64/x64/UnrealEditor/Development/OnlineSubsystemUtils/UnrealEditor-OnlineSubsystemUtils.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/GeometryCollectionEngine/UnrealEditor-GeometryCollectionEngine.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/FieldSystemEngine/UnrealEditor-FieldSystemEngine.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/RHI/UnrealEditor-RHI.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/RHICore/UnrealEditor-RHICore.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/RenderCore/UnrealEditor-RenderCore.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/Renderer/UnrealEditor-Renderer.lib"
"../Plugins/FX/Niagara/Intermediate/Build/Win64/x64/UnrealEditor/Development/NiagaraCore/UnrealEditor-NiagaraCore.lib"
"../Plugins/FX/Niagara/Intermediate/Build/Win64/x64/UnrealEditor/Development/NiagaraShader/UnrealEditor-NiagaraShader.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/KismetWidgets/UnrealEditor-KismetWidgets.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/MaterialEditor/UnrealEditor-MaterialEditor.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/NetCore/UnrealEditor-NetCore.lib"
"delayimp.lib"
"wininet.lib"
"rpcrt4.lib"
"ws2_32.lib"
"dbghelp.lib"
"comctl32.lib"
"Winmm.lib"
"kernel32.lib"
"user32.lib"
"gdi32.lib"
"winspool.lib"
"comdlg32.lib"
"advapi32.lib"
"shell32.lib"
"ole32.lib"
"oleaut32.lib"
"uuid.lib"
"odbc32.lib"
"odbccp32.lib"
"netapi32.lib"
"iphlpapi.lib"
"setupapi.lib"
"synchronization.lib"
"dwmapi.lib"
"imm32.lib"
/OUT:"C:/Game/Auracron/Plugins/UnrealMCP/Binaries/Win64/UnrealEditor-UnrealMCP.dll"
/PDB:"C:/Game/Auracron/Plugins/UnrealMCP/Binaries/Win64/UnrealEditor-UnrealMCP.pdb"
/ignore:4078