// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeAuracron_init() {}
	static FPackageRegistrationInfo Z_Registration_Info_UPackage__Script_Auracron;
	FORCENOINLINE UPackage* Z_Construct_UPackage__Script_Auracron()
	{
		if (!Z_Registration_Info_UPackage__Script_Auracron.OuterSingleton)
		{
			static const UECodeGen_Private::FPackageParams PackageParams = {
				"/Script/Auracron",
				nullptr,
				0,
				PKG_CompiledIn | 0x00000000,
				0xC9D49119,
				0x4DE2798B,
				METADATA_PARAMS(0, nullptr)
			};
			UECodeGen_Private::ConstructUPackage(Z_Registration_Info_UPackage__Script_Auracron.OuterSingleton, PackageParams);
		}
		return Z_Registration_Info_UPackage__Script_Auracron.OuterSingleton;
	}
	static FRegisterCompiledInInfo Z_CompiledInDeferPackage_UPackage__Script_Auracron(Z_Construct_UPackage__Script_Auracron, TEXT("/Script/Auracron"), Z_Registration_Info_UPackage__Script_Auracron, CONSTRUCT_RELOAD_VERSION_INFO(FPackageReloadVersionInfo, 0xC9D49119, 0x4DE2798B));
PRAGMA_ENABLE_DEPRECATION_WARNINGS
