#pragma once

#include "CoreMinimal.h"
#include "Dom/JsonObject.h"
#include "Dom/JsonValue.h"
#include "Engine/Engine.h"
#include "Engine/CollisionProfile.h"
#include "Components/PrimitiveComponent.h"
#include "PhysicsEngine/BodySetup.h"

/**
 * Handles Multi-Layer Collision System commands for MCP server
 * Implements collision channel management, profile configuration,
 * layer-specific collision rules, and performance optimization
 */
class UNREALMCP_API FUnrealMCPCollisionCommands
{
public:
    FUnrealMCPCollisionCommands();
    ~FUnrealMCPCollisionCommands();

    // Main command handler
    TSharedPtr<FJsonObject> HandleCommand(const FString& CommandType, const TSharedPtr<FJsonObject>& Params);

private:
    // Collision Channel Management
    TSharedPtr<FJsonObject> HandleCreateCollisionChannel(const TSharedPtr<FJsonObject>& Params);
    TSharedPtr<FJsonObject> HandleConfigureCollisionProfile(const TSharedPtr<FJsonObject>& Params);
    
    // Layer-Specific Collision Rules
    TSharedPtr<FJsonObject> HandleSetLayerCollisionRules(const TSharedPtr<FJsonObject>& Params);
    TSharedPtr<FJsonObject> HandleConfigureCollisionSizeScaling(const TSharedPtr<FJsonObject>& Params);
    TSharedPtr<FJsonObject> HandleCreateLayerCollisionMatrix(const TSharedPtr<FJsonObject>& Params);
    
    // Collision Optimization
    TSharedPtr<FJsonObject> HandleOptimizeCollisionDetection(const TSharedPtr<FJsonObject>& Params);
    TSharedPtr<FJsonObject> HandleConfigureCollisionComplexity(const TSharedPtr<FJsonObject>& Params);
    TSharedPtr<FJsonObject> HandleSetupLayerCollisionFiltering(const TSharedPtr<FJsonObject>& Params);
    
    // System Status
    TSharedPtr<FJsonObject> HandleGetCollisionSystemStatus(const TSharedPtr<FJsonObject>& Params);
    
    // === Auracron Architecture Specific Commands ===
    TSharedPtr<FJsonObject> HandleSetupAuracronCollisionChannels(const TSharedPtr<FJsonObject>& Params);
    TSharedPtr<FJsonObject> HandleConfigureDota2CollisionSizes(const TSharedPtr<FJsonObject>& Params);
    TSharedPtr<FJsonObject> HandleSetupLayerCollisionInteractions(const TSharedPtr<FJsonObject>& Params);
    TSharedPtr<FJsonObject> HandleConfigureAuracronCollisionProfiles(const TSharedPtr<FJsonObject>& Params);
    
    // Helper functions
    ECollisionResponse StringToCollisionResponse(const FString& ResponseString);
    FString CollisionResponseToString(ECollisionResponse Response);
    ECollisionChannel StringToCollisionChannel(const FString& ChannelString);
    FString CollisionChannelToString(ECollisionChannel Channel);
    
    // Collision profile management helpers
    bool CreateCustomCollisionProfile(const FString& ProfileName, const TMap<FString, FString>& ChannelResponses);
    bool UpdateCollisionProfile(const FString& ProfileName, const TMap<FString, FString>& ChannelResponses);
    
    // Layer collision management
    bool ApplyLayerCollisionRules(const FString& LayerName, const TSharedPtr<FJsonObject>& Rules);
    bool SetupCollisionMatrix(const TArray<FString>& Layers, const TSharedPtr<FJsonObject>& Matrix);
    
    // Performance optimization helpers
    bool OptimizeLayerCollision(const FString& LayerName, const TSharedPtr<FJsonObject>& Settings);
    bool ConfigureActorCollisionComplexity(const FString& ActorName, const FString& ComplexityType);
    
    // Collision filtering helpers
    bool SetupCollisionFiltering(const FString& LayerName, const TSharedPtr<FJsonObject>& FilterSettings);
    
    // Validation helpers
    bool ValidateCollisionChannelName(const FString& ChannelName);
    bool ValidateCollisionProfileName(const FString& ProfileName);
    bool ValidateLayerName(const FString& LayerName);
    
    // Error handling
    TSharedPtr<FJsonObject> CreateErrorResponse(const FString& ErrorMessage);
    TSharedPtr<FJsonObject> CreateSuccessResponse(const TSharedPtr<FJsonObject>& Data = nullptr);
};