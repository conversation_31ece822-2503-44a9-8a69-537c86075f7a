#include "AuracronGameMode.h"
#include "Engine/World.h"
#include "TimerManager.h"

AAuracronGameMode::AAuracronGameMode()
{
    PrimaryActorTick.bCanEverTick = true;
    GameStartTime = 0.0f;
    CurrentPhase = TEXT("Early Game");
}

void AAuracronGameMode::BeginPlay()
{
    Super::BeginPlay();
    
    GameStartTime = GetWorld()->GetTimeSeconds();
    
    // Inicializar sistema multicamada
    InitializeMultilayerSystem();
    
    UE_LOG(LogTemp, Warning, TEXT("AURACRON Game Mode iniciado - Sistema Multicamada ativo"));
}

void AAuracronGameMode::InitializeMultilayerSystem()
{
    if (bEnablePlanicieRadiante)
    {
        UE_LOG(LogTemp, Log, TEXT("Planície Radiante (Z: 0-2000) - ATIVA"));
    }
    
    if (bEnableFirmamentoZephyr)
    {
        UE_LOG(LogTemp, Log, TEXT("Firmamento Zephyr (Z: 2000-4000) - ATIVO"));
    }
    
    if (bEnableAbismoUmbral)
    {
        UE_LOG(LogTemp, Log, TEXT("Abismo Umbral (Z: 4000-6000) - ATIVO"));
    }
}

void AAuracronGameMode::TransitionToNextPhase()
{
    float CurrentTime = GetWorld()->GetTimeSeconds() - GameStartTime;
    float CurrentTimeMinutes = CurrentTime / 60.0f;
    
    if (CurrentTimeMinutes < EarlyGameDuration)
    {
        CurrentPhase = TEXT("Early Game");
    }
    else if (CurrentTimeMinutes < EarlyGameDuration + MidGameDuration)
    {
        CurrentPhase = TEXT("Mid Game");
    }
    else
    {
        CurrentPhase = TEXT("Late Game");
    }
    
    UE_LOG(LogTemp, Warning, TEXT("Transição para: %s (%.1f minutos)"), *CurrentPhase, CurrentTimeMinutes);
}

FString AAuracronGameMode::GetCurrentGamePhase() const
{
    return CurrentPhase;
}
