{"Version": "1.2", "Data": {"Source": "c:\\game\\auracron\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\auracron\\module.auracron.gen.cpp", "ProvidedModule": "", "PCH": "c:\\game\\auracron\\intermediate\\build\\win64\\x64\\auracroneditor\\development\\unrealed\\sharedpch.unrealed.project.valapi.valexpapi.cpp20.h.pch", "Includes": ["c:\\game\\auracron\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\auracron\\definitions.auracron.h", "c:\\game\\auracron\\intermediate\\build\\win64\\unrealeditor\\inc\\auracron\\uht\\auracron.init.gen.cpp", "c:\\game\\auracron\\intermediate\\build\\win64\\unrealeditor\\inc\\auracron\\uht\\auracrongamemode.gen.cpp", "c:\\game\\auracron\\source\\auracron\\public\\auracrongamemode.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\gameframework\\gamemodebase.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\engine\\serverstatreplicator.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\serverstatreplicator.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\gamemodebase.generated.h", "c:\\game\\auracron\\intermediate\\build\\win64\\unrealeditor\\inc\\auracron\\uht\\auracrongamemode.generated.h", "c:\\game\\auracron\\intermediate\\build\\win64\\unrealeditor\\inc\\auracron\\uht\\multilayermanager.gen.cpp", "c:\\game\\auracron\\source\\auracron\\public\\multilayermanager.h", "c:\\game\\auracron\\intermediate\\build\\win64\\unrealeditor\\inc\\auracron\\uht\\multilayermanager.generated.h"], "ImportedModules": [], "ImportedHeaderUnits": []}}