"""Vertical Navigation Tools for Unreal MCP.

This module provides tools for creating and managing vertical navigation
systems including portals, elevators, and dimensional bridges.
"""

import logging
from typing import Dict, List, Any, Optional
from mcp.server.fastmcp import FastMCP, Context

# Get logger
logger = logging.getLogger("UnrealMCP")

def register_vertical_navigation_tools(mcp: FastMCP):
    """Register Vertical Navigation tools with the MCP server."""
    
    @mcp.tool()
    def create_primary_portals_system(
        ctx: Context,
        portals_config: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Create the Primary Portals system for layer transitions.
        
        Args:
            ctx: MCP Context
            portals_config: Portals configuration as JSON object containing:
                - portals_per_layer: Number of portals per layer (default: 4)
                - activation_time: Time to activate portal in seconds (default: 3.0)
                - cooldown_time: Portal cooldown in seconds (default: 10.0)
                - enable_combat_restriction: Disable portals during combat (default: True)
        
        Returns:
            Dict[str, Any]: Operation result
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            # Default configuration for Primary Portals
            default_config = {
                "system_name": "primary_portals_system",
                "portals_per_layer": 4,
                "activation_time": 3.0,
                "cooldown_time": 10.0,
                "enable_combat_restriction": True,
                "portal_connections": {
                    "planicie_to_firmamento": {
                        "count": 4,
                        "locations": ["Norte", "Sul", "Leste", "Oeste"],
                        "activation_cost": "Nenhum",
                        "travel_time": 2.0
                    },
                    "firmamento_to_abismo": {
                        "count": 4,
                        "locations": ["Norte", "Sul", "Leste", "Oeste"],
                        "activation_cost": "Nenhum",
                        "travel_time": 2.5
                    },
                    "planicie_to_abismo": {
                        "count": 2,
                        "locations": ["Central Norte", "Central Sul"],
                        "activation_cost": "50 Mana",
                        "travel_time": 4.0,
                        "special_requirement": "Nível 6+"
                    }
                },
                "portal_mechanics": [
                    "instant_travel_between_layers",
                    "combat_restriction_active",
                    "cooldown_per_champion",
                    "visual_telegraph_3_seconds",
                    "strategic_positioning_required"
                ]
            }
            
            # Merge provided config with defaults
            if portals_config:
                default_config.update(portals_config)
            
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            logger.info(f"Creating Primary Portals system with config: {default_config}")
            response = unreal.send_command("create_primary_portals_system", default_config)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Primary Portals system creation response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error creating Primary Portals system: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def create_mystic_elevators_system(
        ctx: Context,
        elevators_config: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Create the Mystic Elevators system for controlled vertical movement.
        
        Args:
            ctx: MCP Context
            elevators_config: Elevators configuration as JSON object containing:
                - total_elevators: Total number of elevators (default: 4)
                - travel_time: Time to travel between layers in seconds (default: 5.0)
                - capacity: Maximum champions per elevator (default: 5)
                - enable_combat_usage: Allow usage during combat (default: False)
        
        Returns:
            Dict[str, Any]: Operation result
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            # Default configuration for Mystic Elevators
            default_config = {
                "system_name": "mystic_elevators_system",
                "total_elevators": 4,
                "travel_time": 5.0,
                "capacity": 5,
                "enable_combat_usage": False,
                "elevator_locations": {
                    "elevator_1": {
                        "name": "Elevador Cristalino",
                        "position": "Nordeste",
                        "connects": ["Planície Radiante", "Firmamento Zephyr"],
                        "special_effect": "Regeneração durante viagem"
                    },
                    "elevator_2": {
                        "name": "Elevador Sombrio",
                        "position": "Noroeste",
                        "connects": ["Firmamento Zephyr", "Abismo Umbral"],
                        "special_effect": "Invisibilidade temporária"
                    },
                    "elevator_3": {
                        "name": "Elevador Ventoso",
                        "position": "Sudeste",
                        "connects": ["Planície Radiante", "Firmamento Zephyr"],
                        "special_effect": "Velocidade aumentada"
                    },
                    "elevator_4": {
                        "name": "Elevador Abissal",
                        "position": "Sudoeste",
                        "connects": ["Todas as camadas"],
                        "special_effect": "Teletransporte direto",
                        "activation_requirement": "Controle de equipe"
                    }
                },
                "elevator_mechanics": [
                    "slower_than_portals_but_safer",
                    "team_coordination_advantage",
                    "special_buffs_during_travel",
                    "strategic_control_points",
                    "multi_champion_transport"
                ]
            }
            
            # Merge provided config with defaults
            if elevators_config:
                default_config.update(elevators_config)
            
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            logger.info(f"Creating Mystic Elevators system with config: {default_config}")
            response = unreal.send_command("create_mystic_elevators_system", default_config)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Mystic Elevators system creation response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error creating Mystic Elevators system: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def create_dimensional_bridges_system(
        ctx: Context,
        bridges_config: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Create the Dimensional Bridges system for temporary connections.
        
        Args:
            ctx: MCP Context
            bridges_config: Bridges configuration as JSON object containing:
                - bridge_duration: Duration of bridge in seconds (default: 30.0)
                - activation_cost: Mana cost to activate (default: 100)
                - cooldown_time: Cooldown between activations in seconds (default: 120.0)
                - enable_team_requirement: Require team coordination (default: True)
        
        Returns:
            Dict[str, Any]: Operation result
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            # Default configuration for Dimensional Bridges
            default_config = {
                "system_name": "dimensional_bridges_system",
                "bridge_duration": 30.0,
                "activation_cost": 100,
                "cooldown_time": 120.0,
                "enable_team_requirement": True,
                "bridge_types": {
                    "energy_bridge": {
                        "name": "Ponte de Energia",
                        "connects": ["Planície Radiante", "Firmamento Zephyr"],
                        "duration": 30.0,
                        "width": "2 champions lado a lado",
                        "special_effect": "Velocidade aumentada em 25%"
                    },
                    "shadow_bridge": {
                        "name": "Ponte Sombria",
                        "connects": ["Firmamento Zephyr", "Abismo Umbral"],
                        "duration": 25.0,
                        "width": "1 champion por vez",
                        "special_effect": "Invisibilidade durante travessia"
                    },
                    "crystal_bridge": {
                        "name": "Ponte Cristalina",
                        "connects": ["Planície Radiante", "Abismo Umbral"],
                        "duration": 20.0,
                        "width": "3 champions lado a lado",
                        "special_effect": "Escudo mágico durante travessia",
                        "activation_requirement": "2+ champions próximos"
                    }
                },
                "bridge_mechanics": [
                    "temporary_connections_strategic_timing",
                    "team_coordination_required",
                    "high_risk_high_reward",
                    "limited_duration_creates_urgency",
                    "multiple_activation_points"
                ]
            }
            
            # Merge provided config with defaults
            if bridges_config:
                default_config.update(bridges_config)
            
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            logger.info(f"Creating Dimensional Bridges system with config: {default_config}")
            response = unreal.send_command("create_dimensional_bridges_system", default_config)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Dimensional Bridges system creation response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error creating Dimensional Bridges system: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}

    @mcp.tool()
    def setup_vertical_navigation_strategies(
        ctx: Context,
        strategies_config: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Setup vertical navigation strategies for different game phases.

        Args:
            ctx: MCP Context
            strategies_config: Strategies configuration as JSON object containing:
                - enable_early_game: Enable early game strategies (default: True)
                - enable_mid_game: Enable mid game strategies (default: True)
                - enable_late_game: Enable late game strategies (default: True)

        Returns:
            Dict[str, Any]: Operation result
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            # Default configuration for Navigation Strategies
            default_config = {
                "system_name": "vertical_navigation_strategies",
                "strategies": {
                    "early_game": {
                        "name": "Estratégias Early Game",
                        "enabled": True,
                        "focus": "Exploração e estabelecimento de controle",
                        "recommended_methods": ["Portais primários", "Elevadores seguros"],
                        "avoid_methods": ["Pontes dimensionais"],
                        "key_objectives": [
                            "Estabelecer visão em múltiplas camadas",
                            "Controlar pontos de transição",
                            "Evitar confrontos em território desconhecido"
                        ]
                    },
                    "mid_game": {
                        "name": "Estratégias Mid Game",
                        "enabled": True,
                        "focus": "Controle de objetivos e rotações",
                        "recommended_methods": ["Todos os métodos disponíveis"],
                        "key_objectives": [
                            "Rotações rápidas entre objetivos",
                            "Flanqueamento através de camadas",
                            "Controle de pontos estratégicos"
                        ]
                    },
                    "late_game": {
                        "name": "Estratégias Late Game",
                        "enabled": True,
                        "focus": "Execução de teamfights e finalização",
                        "recommended_methods": ["Pontes dimensionais", "Elevadores coordenados"],
                        "key_objectives": [
                            "Engajamentos coordenados",
                            "Escape routes planejadas",
                            "Controle total do mapa vertical"
                        ]
                    }
                }
            }

            # Merge provided config with defaults
            if strategies_config:
                default_config.update(strategies_config)

            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            logger.info(f"Setting up vertical navigation strategies with config: {default_config}")
            response = unreal.send_command("setup_vertical_navigation_strategies", default_config)

            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}

            logger.info(f"Vertical navigation strategies setup response: {response}")
            return response

        except Exception as e:
            error_msg = f"Error setting up vertical navigation strategies: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}

    @mcp.tool()
    def setup_complete_vertical_navigation_system(
        ctx: Context,
        system_config: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Setup the complete vertical navigation system with all components.

        Args:
            ctx: MCP Context
            system_config: System configuration as JSON object containing:
                - enable_all_systems: Enable all navigation systems (default: True)
                - auto_configure: Automatically configure optimal settings (default: True)

        Returns:
            Dict[str, Any]: Operation result
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            # Default system configuration
            default_config = {
                "system_name": "complete_vertical_navigation_system",
                "enable_all_systems": True,
                "auto_configure": True,
                "components": {
                    "primary_portals": True,
                    "mystic_elevators": True,
                    "dimensional_bridges": True,
                    "navigation_strategies": True
                }
            }

            # Merge provided config with defaults
            if system_config:
                default_config.update(system_config)

            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            logger.info(f"Setting up complete vertical navigation system with config: {default_config}")

            # Setup all components if enabled
            results = []
            if default_config["components"]["primary_portals"]:
                portal_result = unreal.send_command("create_primary_portals_system", {})
                results.append({"component": "primary_portals", "result": portal_result})

            if default_config["components"]["mystic_elevators"]:
                elevator_result = unreal.send_command("create_mystic_elevators_system", {})
                results.append({"component": "mystic_elevators", "result": elevator_result})

            if default_config["components"]["dimensional_bridges"]:
                bridge_result = unreal.send_command("create_dimensional_bridges_system", {})
                results.append({"component": "dimensional_bridges", "result": bridge_result})

            if default_config["components"]["navigation_strategies"]:
                strategy_result = unreal.send_command("setup_vertical_navigation_strategies", {})
                results.append({"component": "navigation_strategies", "result": strategy_result})

            # Create final response
            response_data = {
                "system_config": default_config,
                "component_results": results,
                "total_components": len(results),
                "system_status": "fully_configured"
            }

            response = {
                "success": True,
                "message": "Complete vertical navigation system configured successfully",
                "data": response_data
            }

            logger.info(f"Complete vertical navigation system setup response: {response}")
            return response

        except Exception as e:
            error_msg = f"Error setting up complete vertical navigation system: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
