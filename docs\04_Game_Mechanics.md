# ⚔️ **AURACRON - GAME MECHANICS**

## **📋 ÍNDICE**
- [Mecânicas de Mapa Multicamada](#mecânicas-de-mapa-multicamada)
- [Mecânicas de Lane](#mecânicas-de-lane)
- [Sistema de Jungle](#sistema-de-jungle)
- [Navegação Vertical](#navegação-vertical)
- [Fases da Partida](#fases-da-partida)
- [Objetivos e Estruturas](#objetivos-e-estruturas)
- [Mecânicas de Combate](#mecânicas-de-combate)
- [Sistema de Visão](#sistema-de-visão)

---

## **🗺️ MECÂNICAS DE MAPA MULTICAMADA**

### **Sistema de Três Camadas**

O mapa do AURACRON é composto por **três camadas verticais interconectadas**, cada uma com mecânicas únicas:

#### **☀️ Planície Radiante (Camada Inferior - Z: 0-2000)**
- **Layout Familiar**: 3 lanes tradicionais (Top, Mid, Bot)
- **Jungle Terrestre**: 24 camps neutros distribuídos simetricamente
- **Mecânicas Especiais**: 
  - Crescimento acelerado de minions próximo a Cristais de Luz
  - Torres que se fortalecem com energia solar
  - Buffs que aumentam regeneração e sustain

#### **🌪️ Firmamento Zephyr (Camada Média - Z: 2000-4000)**
- **Layout Dinâmico**: 2 correntes de vento + zona central
- **Plataformas Móveis**: Estruturas que mudam de posição a cada 3 minutos
- **Mecânicas Especiais**:
  - Correntes de vento que aceleram movimento (+30% velocidade)
  - Plataformas flutuantes conectadas por pontes de energia
  - Vórtices que permitem movimento rápido entre pontos

#### **🌑 Abismo Umbral (Camada Superior - Z: 4000-6000)**
- **Layout Labiríntico**: Rede complexa de corredores e câmaras
- **Zonas de Sombra**: Áreas com visibilidade reduzida (50% alcance)
- **Mecânicas Especiais**:
  - Névoa sombria que oculta movimentos
  - Armadilhas ambientais ativadas por proximidade
  - Câmaras secretas com recompensas especiais

### **Conectividade Entre Camadas**

#### **🌀 Portais Primários**
- **Quantidade**: 4 por camada (16 total)
- **Localização**: Cantos do mapa + centro
- **Mecânica**: 
  - Tempo de ativação: 3 segundos
  - Cooldown pessoal: 30 segundos
  - Capacidade: 1 jogador por vez
  - Vulnerabilidade: Pode ser interrompido por dano

#### **🏗️ Elevadores Místicos**
- **Quantidade**: 2 por lado (4 total)
- **Localização**: Próximo às bases de cada equipe
- **Mecânica**:
  - Capacidade: Equipe completa (5 jogadores)
  - Tempo de viagem: 5 segundos
  - Vulnerabilidade: Pode ser atacado durante transporte
  - Cooldown: 60 segundos após uso

#### **🌉 Pontes Dimensionais**
- **Ativação**: Conquistar objetivos específicos
- **Duração**: 2 minutos
- **Localização**: Varia baseada no objetivo conquistado
- **Mecânica**: Conexão direta entre camadas específicas

### **Pathfinding Multicamada**

#### **Algoritmo A* Adaptado**
```
Custos de Movimento:
- Horizontal (mesma camada): 1 unidade
- Portal (transição): 5 unidades
- Elevador (transição): 3 unidades
- Ponte Dimensional: 1 unidade
```

#### **Navegação Assistida**
- **Auto-pathfinding**: Calcula rotas otimizadas entre camadas
- **Indicadores Visuais**: Trilhas luminosas e setas direcionais
- **Sugestões de IA**: Recomendações de rota baseadas no contexto

---

## **🛣️ MECÂNICAS DE LANE**

### **Estrutura das Lanes**

#### **🔝 TOP LANE**
- **Comprimento**: Mais longa, favorece trades estendidos
- **Características**: Isolada, foco em 1v1, teleport crucial
- **Campeões Típicos**: Tanks, Bruisers, Split Pushers
- **Objetivos Próximos**: Herald, Scuttle superior

**Mecânicas Específicas**:
- **Freeze Zone**: Área próxima à torre onde é fácil congelar a wave
- **Gank Paths**: Múltiplas rotas através da selva
- **TP Flanks**: Posições estratégicas para teleports

#### **🎯 MID LANE**
- **Comprimento**: Mais curta, trades rápidos
- **Características**: Central, acesso a ambos os lados
- **Campeões Típicos**: Mages, Assassinos, Roamers
- **Objetivos Próximos**: Ambos os Scuttles, controle de rio

**Mecânicas Específicas**:
- **Roam Windows**: Janelas para ajudar outras lanes
- **River Control**: Controle do rio para objetivos
- **Wave Clear**: Prioridade para clear rápido

#### **⬇️ BOT LANE**
- **Comprimento**: Padrão, foco em 2v2
- **Características**: Duo lane, sinergia crucial
- **Campeões Típicos**: ADC + Support
- **Objetivos Próximos**: Dragão, Scuttle inferior

**Mecânicas Específicas**:
- **All-in Windows**: Momentos para engajamento total
- **Dragon Control**: Controle para objetivos épicos
- **Bot Lane Synergy**: Combos entre ADC e Support

### **Fases da Lane**

#### **🌅 EARLY LANING (0-10 minutos)**

**Objetivos Primários**:
- Estabelecer vantagem de CS
- Controlar posicionamento da wave
- Evitar ganks enquanto pressiona
- Estabelecer controle de visão

**Mecânicas-Chave**:
- **Last Hitting**: Timing preciso para CS
- **Trading Stance**: Posicionamento para trades
- **Wave Management**: Controle do push/freeze
- **Back Timing**: Momento ideal para recall

#### **⚡ MID LANING (10-20 minutos)**

**Objetivos Primários**:
- Transição para teamfights
- Controle de objetivos neutros
- Roaming e map pressure
- Estabelecer vantagens de item

**Mecânicas-Chave**:
- **Roam Timing**: Quando deixar a lane
- **Objective Setup**: Preparação para dragões/herald
- **Vision Control**: Wards estratégicos
- **Power Spikes**: Aproveitar picos de força

#### **🔥 LATE LANING (20+ minutos)**

**Objetivos Primários**:
- Teamfight positioning
- Objective control
- Split push pressure
- End game execution

**Mecânicas-Chave**:
- **Teamfight Execution**: Coordenação perfeita
- **Objective Prioritization**: Escolhas estratégicas
- **Split Push Management**: Pressão lateral
- **End Game Calls**: Decisões finais

---

## **🌲 SISTEMA DE JUNGLE**

### **Monstros da Selva por Camada**

#### **☀️ Planície Radiante (Jungle Terrestre)**

**🔴 RED BUFF - Guardião Carmesim**
- **Vida**: 1800 (+180 por minuto)
- **Dano**: 78 (+6 por minuto)
- **Buff**: +20% Slow em ataques, +5 HP/s regeneração
- **Duração**: 90 segundos
- **Respawn**: 5 minutos

**🔵 BLUE BUFF - Sentinela Azul**
- **Vida**: 1700 (+170 por minuto)
- **Dano**: 72 (+5 por minuto)
- **Buff**: +10% CDR, +5 MP/s regeneração
- **Duração**: 90 segundos
- **Respawn**: 5 minutos

**🐺 WOLVES - Matilha Sombria**
- **Grande Lobo**: 1200 vida (+120/min)
- **Lobos Menores**: 400 vida cada (+40/min)
- **Ouro Total**: 95
- **Respawn**: 2 minutos

**🐸 GROMP - Sapo Gigante**
- **Vida**: 1500 (+150 por minuto)
- **Dano**: 84 (+7 por minuto)
- **Passiva**: Poison que causa dano ao longo do tempo
- **Ouro**: 105
- **Respawn**: 2 minutos

**🪨 KRUGS - Golems de Pedra**
- **Krug Grande**: 1000 vida (+100/min)
- **Krug Médio**: 600 vida (+60/min)
- **Krugs Pequenos**: 200 vida cada (+20/min)
- **Ouro Total**: 84
- **Respawn**: 2 minutos 15 segundos

**🦅 RAPTORS - Aves Predadoras**
- **Raptor Grande**: 800 vida (+80/min)
- **Raptors Pequenos**: 250 vida cada (+25/min)
- **Ouro Total**: 100
- **Respawn**: 2 minutos

#### **🌪️ Firmamento Zephyr (Jungle Aéreo)**

**⚡ STORM ELEMENTAL - Elemental da Tempestade**
- **Vida**: 2200 (+200 por minuto)
- **Habilidades**: Raios que saltam entre alvos
- **Buff**: +15% Velocidade de Movimento, imunidade a slows
- **Duração**: 120 segundos
- **Respawn**: 6 minutos

**🌪️ WIND SPIRITS - Espíritos do Vento**
- **Espírito Maior**: 900 vida (+90/min)
- **Espíritos Menores**: 300 vida cada (+30/min)
- **Passiva**: Empurram inimigos ao morrer
- **Ouro Total**: 110
- **Respawn**: 2 minutos 30 segundos

**☁️ CLOUD DRAKES - Dragões das Nuvens**
- **Drake Alfa**: 1100 vida (+110/min)
- **Drakes Beta**: 400 vida cada (+40/min)
- **Habilidade**: Voo curto que evita ataques terrestres
- **Ouro Total**: 95
- **Respawn**: 2 minutos 15 segundos

#### **🌑 Abismo Umbral (Jungle Subterrâneo)**

**👤 SHADOW WRAITH - Espectro Sombrio**
- **Vida**: 2000 (+180 por minuto)
- **Habilidades**: Invisibilidade temporária
- **Buff**: +20% Lethality, +10% Spell Vamp
- **Duração**: 100 segundos
- **Respawn**: 5 minutos 30 segundos

**🕷️ VOID SPIDERS - Aranhas do Vazio**
- **Aranha Rainha**: 1000 vida (+100/min)
- **Aranhas Filhotes**: 200 vida cada (+20/min)
- **Passiva**: Spawnam filhotes ao morrer
- **Ouro Total**: 105
- **Respawn**: 2 minutos 45 segundos

**💀 BONE COLLECTORS - Coletores de Ossos**
- **Coletor Maior**: 850 vida (+85/min)
- **Coletores Menores**: 350 vida cada (+35/min)
- **Habilidade**: Ressuscitam uma vez com 50% da vida
- **Ouro Total**: 90
- **Respawn**: 2 minutos 30 segundos

### **Scuttle Crabs Especiais**

#### **🦀 SCUTTLE CRAB TERRESTRE**
- **Localização**: Rio da Planície Radiante
- **Vida**: 1000 (+35 por minuto)
- **Buff**: Visão + velocidade em rio
- **Ouro**: 70-140 (escala com tempo)
- **Respawn**: 2 minutos 30 segundos

#### **🌊 SCUTTLE CRAB ETÉREA**
- **Localização**: Correntes de ar do Firmamento
- **Vida**: 1200 (+40 por minuto)
- **Buff**: Visão + velocidade de movimento aéreo
- **Ouro**: 80-150 (escala com tempo)
- **Respawn**: 3 minutos

#### **🕳️ SCUTTLE CRAB SOMBRIA**
- **Localização**: Túneis do Abismo
- **Vida**: 900 (+30 por minuto)
- **Buff**: Visão + detecção de invisibilidade
- **Ouro**: 75-145 (escala com tempo)
- **Respawn**: 2 minutos 45 segundos

### **Rotas de Clear Otimizadas**

#### **🔴 CLEAR INICIANDO NO RED**
1. **Red Buff** → **Krugs** → **Raptors** → **Wolves** → **Blue Buff** → **Gromp**
2. **Tempo Total**: ~3:30
3. **Nível Atingido**: 4
4. **Vida Restante**: ~60%

#### **🔵 CLEAR INICIANDO NO BLUE**
1. **Blue Buff** → **Gromp** → **Wolves** → **Raptors** → **Red Buff** → **Krugs**
2. **Tempo Total**: ~3:45
3. **Nível Atingido**: 4
4. **Vida Restante**: ~55%

#### **⚡ CLEAR VERTICAL (CROSS-REALM)**
1. **Red Terrestre** → **Storm Elemental** → **Shadow Wraith** → **Blue Terrestre**
2. **Tempo Total**: ~4:15
3. **Nível Atingido**: 4
4. **Buffs Únicos**: 3 diferentes

---

## **🚀 NAVEGAÇÃO VERTICAL**

### **Mecânicas de Transição Entre Camadas**

#### **🌀 Sistema de Portais**

**Portais Primários**:
- **Localização**: 4 por camada (Nordeste, Noroeste, Sudeste, Sudoeste)
- **Ativação**: Channel de 3 segundos
- **Cooldown**: 30 segundos por jogador
- **Interrupção**: Qualquer dano cancela o channel
- **Indicação Visual**: Aura brilhante indica destino disponível

**Mecânicas de Uso**:
```
Sequência de Ativação:
1. Jogador se aproxima do portal (raio de 200 unidades)
2. Interface mostra destinos disponíveis
3. Jogador seleciona camada de destino
4. Channel de 3 segundos inicia
5. Teletransporte instantâneo ao completar
```

**Estratégias de Portal**:
- **Escape Routes**: Rotas de fuga em situações críticas
- **Flanking**: Ataques surpresa de camadas diferentes
- **Objective Control**: Rotação rápida para objetivos
- **Split Push**: Pressão simultânea em múltiplas camadas

#### **🏗️ Elevadores Místicos**

**Características**:
- **Capacidade**: 5 jogadores (equipe completa)
- **Tempo de Viagem**: 5 segundos entre camadas
- **Vulnerabilidade**: Pode ser atacado durante o transporte
- **Cooldown Global**: 60 segundos após uso
- **Localização**: Próximo às bases (2 por lado)

**Mecânicas Especiais**:
- **Proteção Parcial**: 50% de redução de dano durante transporte
- **Cancelamento**: Dano excessivo (>30% HP total) cancela o elevador
- **Prioridade**: Equipe que ativou primeiro tem prioridade
- **Indicação**: Barra de progresso visível para ambas as equipes

**Usos Estratégicos**:
- **Team Rotations**: Movimentação coordenada de equipe
- **Objective Rushes**: Chegada simultânea em objetivos
- **Defensive Retreats**: Recuo coordenado sob pressão
- **Surprise Engages**: Engajamentos inesperados

#### **🌉 Pontes Dimensionais**

**Ativação por Objetivos**:
- **Guardião da Aurora**: Ponte Planície ↔ Firmamento (2 min)
- **Senhor dos Ventos**: Ponte Firmamento ↔ Abismo (2 min)
- **Arqui-Sombra**: Ponte Abismo ↔ Planície (2 min)
- **Cristais/Esferas/Fragmentos**: Pontes temporárias específicas (90 seg)

**Características**:
- **Duração**: 90-120 segundos baseado no objetivo
- **Capacidade**: Ilimitada
- **Velocidade**: Movimento normal (sem penalidade)
- **Visibilidade**: Visível para ambas as equipes
- **Destruição**: Não pode ser destruída, apenas expira

### **Estratégias de Navegação Multicamada**

#### **🎯 Rotação Estratégica**

**Early Game (0-15 min)**:
- **Foco**: Estabelecer controle na Planície Radiante
- **Transições**: Limitadas, apenas para objetivos específicos
- **Prioridade**: Portais próximos às lanes para ganks

**Mid Game (15-30 min)**:
- **Foco**: Controle de múltiplas camadas
- **Transições**: Frequentes para controle de objetivos
- **Prioridade**: Elevadores para movimentação de equipe

**Late Game (30+ min)**:
- **Foco**: Controle total do mapa vertical
- **Transições**: Constantes para pressão máxima
- **Prioridade**: Pontes dimensionais para vantagem tática

#### **🛡️ Controle de Acesso**

**Ward Placement**:
- **Portais**: Wards próximos para detectar rotações
- **Elevadores**: Controle visual das áreas de embarque
- **Pontes**: Monitoramento de pontes ativas

**Denial Strategies**:
- **Portal Camping**: Posicionamento para interceptar transições
- **Elevator Control**: Controle das áreas próximas aos elevadores
- **Bridge Timing**: Aproveitamento máximo de pontes temporárias

#### **⚡ Combos de Navegação**

**Portal → Elevador**:
1. Portal para camada adjacente
2. Movimento rápido para elevador
3. Transporte de equipe para terceira camada
4. Vantagem posicional máxima

**Ponte → Portal**:
1. Uso de ponte dimensional ativa
2. Posicionamento estratégico
3. Portal para flanking
4. Ataque coordenado

**Elevador → Ponte**:
1. Elevador com equipe completa
2. Controle de objetivo para ativar ponte
3. Pressão sustentada em múltiplas camadas
4. Dominação territorial

### **Considerações de Performance**

#### **🎮 Otimização Mobile**
- **Auto-Navigation**: IA sugere rotas otimizadas
- **Gesture Controls**: Swipe para seleção rápida de camada
- **Visual Indicators**: Setas e trilhas claras
- **Simplified UI**: Interface adaptada para telas menores

#### **🌐 Sincronização de Rede**
- **Predictive Movement**: Compensação de latência
- **State Compression**: Redução de dados de transição
- **Priority Updates**: Priorização de updates críticos
- **Rollback Netcode**: Correção de dessincronia

---

## **⏰ FASES DA PARTIDA**

### **🌅 EARLY GAME (0-15 minutos)**

#### **Objetivos da Fase**
- Estabelecer vantagens de lane
- Controlar visão do mapa
- Secure first objectives
- Desenvolver vantagens econômicas

#### **Marcos Temporais**
- **1:30**: Minions spawnam
- **3:15**: Primeiro clear da jungle completo
- **5:00**: Primeiro Herald spawn
- **8:00**: Primeiro Dragão spawn
- **14:00**: Herald despawna

#### **Estratégias Comuns**
- **Lane Priority**: Estabelecer controle de lane
- **Jungle Tracking**: Rastrear posição do jungler inimigo
- **Vision Control**: Wards defensivos e ofensivos
- **Gank Setup**: Coordenação para ganks

### **⚡ MID GAME (15-30 minutos)**

#### **Objetivos da Fase**
- Transição para teamfights
- Controle de objetivos épicos
- Estabelecer map control
- Converter vantagens em estruturas

#### **Marcos Temporais**
- **15:00**: Segundo Herald spawn
- **20:00**: Baron Auracron spawn
- **25:00**: Dragões Soul disponível
- **30:00**: Transição para late game

#### **Estratégias Comuns**
- **Objective Trading**: Trocar objetivos estrategicamente
- **Split Push**: Pressão lateral coordenada
- **Teamfight Positioning**: Formações para combate
- **Vision Denial**: Controle e negação de visão

### **🔥 LATE GAME (30+ minutos)**

#### **Objetivos da Fase**
- Teamfights decisivos
- Controle de Baron e Elder
- Push para vitória
- Execução perfeita

#### **Marcos Temporais**
- **30:00**: Elder Dragon disponível
- **35:00**: Baron buffs mais poderosos
- **40:00**: Respawns mais longos
- **45:00**: Death timers críticos

#### **Estratégias Comuns**
- **Baron Control**: Controle absoluto da área
- **Elder Dragon**: Buff de execução
- **Perfect Teamfights**: Coordenação máxima
- **End Game Calls**: Decisões finais

---

## **🏰 OBJETIVOS E ESTRUTURAS**

### **Dragões Primordiais**

#### **🔥 DRAGÃO INFERNAL**
- **Spawn**: 8:00, depois a cada 5 minutos
- **Vida**: 3500 (+240 por minuto após 15:00)
- **Buff**: +8% AD e AP para a equipe
- **Stack**: Até 4 stacks (32% total)
- **Soul**: Explosões de dano em habilidades

#### **🌊 DRAGÃO OCEÂNICO**
- **Spawn**: 8:00, depois a cada 5 minutos
- **Vida**: 3500 (+240 por minuto após 15:00)
- **Buff**: +6% de cura e regeneração
- **Stack**: Até 4 stacks (24% total)
- **Soul**: Regeneração massiva fora de combate

#### **🌪️ DRAGÃO DAS NUVENS**
- **Spawn**: 8:00, depois a cada 5 minutos
- **Vida**: 3500 (+240 por minuto após 15:00)
- **Buff**: +3% velocidade de movimento
- **Stack**: Até 4 stacks (12% total)
- **Soul**: Dash curto após usar ultimate

#### **⛰️ DRAGÃO DA MONTANHA**
- **Spawn**: 8:00, depois a cada 5 minutos
- **Vida**: 3500 (+240 por minuto após 15:00)
- **Buff**: +6% de dano contra estruturas
- **Stack**: Até 4 stacks (24% total)
- **Soul**: Escudo após causar dano a campeões

#### **🌟 ELDER DRAGON**
- **Spawn**: 30:00, depois a cada 6 minutos
- **Vida**: 6000 (+300 por minuto)
- **Buff**: +20% de dano, execução em 20% de vida
- **Duração**: 150 segundos
- **Requisito**: Uma equipe deve ter Dragon Soul

### **Baron Auracron**

#### **📊 Estatísticas Base**
- **Spawn**: 20:00
- **Vida**: 8000 (+180 por minuto)
- **Dano**: 300 (+15 por minuto)
- **Resistências**: 120 Armadura, 70 RM
- **Respawn**: 6 minutos

#### **🎯 Habilidades**
- **Acid Pool**: Cria poças de ácido que causam dano
- **Tentacle Slam**: Ataque em área que empurra
- **Void Corruption**: Reduz cura recebida
- **Death Spiral**: Ultimate que puxa todos para o centro

#### **💪 Buff do Baron**
- **Duração**: 180 segundos
- **AD/AP**: +40
- **Minion Enhancement**: Minions ficam mais fortes
- **Recall Speed**: Recall 4 segundos mais rápido
- **Mana/Energy Regen**: +15 por segundo

### **Torres**

#### **🏗️ TORRES EXTERNAS**
- **Vida**: 2500
- **Armadura**: 40
- **Dano**: 152 (+4 por minuto)
- **Ouro**: 650 (dividido pela equipe)
- **Proteção**: Fortification até 5:00

#### **🏗️ TORRES INTERNAS**
- **Vida**: 3000
- **Armadura**: 55
- **Dano**: 170 (+6 por minuto)
- **Ouro**: 750 (dividido pela equipe)
- **Laser**: Foco em campeões após 3 ataques

#### **🏗️ TORRES INIBIDORAS**
- **Vida**: 3500
- **Armadura**: 65
- **Dano**: 190 (+8 por minuto)
- **Ouro**: 850 (dividido pela equipe)
- **True Sight**: Revela invisibilidade

#### **🏗️ TORRES NEXUS**
- **Vida**: 4000
- **Armadura**: 75
- **Dano**: 210 (+10 por minuto)
- **Ouro**: 50 (dividido pela equipe)
- **Regeneração**: +5 HP/s quando não atacada

### **Inibidores**

#### **📊 Estatísticas**
- **Vida**: 4000
- **Regeneração**: +10 HP/s
- **Respawn**: 4 minutos
- **Ouro**: 50 (dividido pela equipe)

#### **🔥 Super Minions**
Quando um inibidor é destruído:
- **Spawn**: A cada 30 segundos
- **Vida**: 1500 (+150 por minuto)
- **Dano**: 190 (+19 por minuto)
- **Resistências**: 80 Armadura, 80 RM
- **Habilidades**: Imune a CC, regeneração

---

## **⚔️ MECÂNICAS DE COMBATE**

### **Sistema de Dano**

#### **🗡️ DANO FÍSICO**
- **Reduzido por**: Armadura
- **Fórmula**: Dano × (100 / (100 + Armadura))
- **Penetração**: Lethality (flat) e % Penetration
- **True Damage**: Ignora armadura completamente

#### **🔮 DANO MÁGICO**
- **Reduzido por**: Resistência Mágica
- **Fórmula**: Dano × (100 / (100 + RM))
- **Penetração**: Magic Pen (flat) e % Penetration
- **Spell Vamp**: Cura baseada em dano mágico

#### **⚡ DANO HÍBRIDO**
- **Composição**: 50% físico + 50% mágico
- **Redução**: Calculada separadamente
- **Penetração**: Ambos os tipos aplicam
- **Itemização**: Requer builds balanceadas

### **Controle de Multidão (CC)**

#### **🚫 HARD CC**
- **Stun**: Impede todas as ações
- **Root**: Impede movimento, permite habilidades
- **Suppress**: Stun que não pode ser removido
- **Knockup**: Displacement que não pode ser reduzido

#### **⚠️ SOFT CC**
- **Slow**: Reduz velocidade de movimento
- **Blind**: Impede ataques básicos
- **Silence**: Impede uso de habilidades
- **Disarm**: Impede ataques básicos

#### **🛡️ TENACIDADE**
- **Função**: Reduz duração de CC
- **Fórmula**: Duração × (1 - Tenacidade%)
- **Não afeta**: Knockups, Suppressions
- **Fontes**: Itens, runas, habilidades

### **Sistema de Cura**

#### **💚 TIPOS DE CURA**
- **Life Steal**: % do dano físico vira cura
- **Spell Vamp**: % do dano mágico vira cura
- **Regeneração**: HP/s constante
- **Cura Direta**: Quantidade fixa instantânea

#### **🚫 GRIEVOUS WOUNDS**
- **Função**: Reduz cura recebida
- **Valores**: 40% ou 60% de redução
- **Duração**: 3 segundos
- **Fontes**: Itens específicos, habilidades

---

## **👁️ SISTEMA DE VISÃO**

### **Tipos de Wards**

#### **🟡 STEALTH WARD**
- **Duração**: 150 segundos
- **Alcance**: 900 unidades
- **Vida**: 3 hits para destruir
- **Limite**: 3 por jogador no mapa
- **Custo**: Gratuito (Trinket)

#### **🔴 CONTROL WARD**
- **Duração**: Permanente até destruído
- **Alcance**: 900 unidades
- **Vida**: 4 hits para destruir
- **Função**: Revela wards invisíveis
- **Limite**: 1 por jogador no mapa
- **Custo**: 75 ouro

#### **🔵 FARSIGHT WARD**
- **Duração**: Permanente até destruído
- **Alcance**: 4000 unidades de colocação
- **Vida**: 1 hit para destruir
- **Função**: Visão de longo alcance
- **Cooldown**: 90 segundos
- **Custo**: Gratuito (Trinket upgrade)

### **Mecânicas de Visão**

#### **👁️ LINE OF SIGHT**
- **Obstáculos**: Paredes, estruturas bloqueiam visão
- **Brush**: Esconde unidades dentro
- **Elevation**: Terreno elevado oferece vantagem
- **Fog of War**: Áreas sem visão ficam escuras

#### **🕵️ TRUE SIGHT**
- **Função**: Revela unidades invisíveis
- **Fontes**: Control wards, algumas habilidades
- **Alcance**: Limitado à área do efeito
- **Duração**: Varia por fonte

#### **👤 CAMOUFLAGE vs INVISIBILIDADE**
- **Camouflage**: Revelado por proximidade (1000 units)
- **Invisibilidade**: Apenas True Sight revela
- **Duração**: Geralmente limitada
- **Quebra**: Ações específicas removem

---

## **🔗 INTEGRAÇÃO COM OUTROS SISTEMAS**

### **Conexão com Dynamic Realm System**
- Mecânicas específicas por camada
- Transições afetam cooldowns e recursos
- Objetivos únicos por realm
- Buffs específicos por ambiente

### **Conexão com Champions & Abilities**
- Sígilos Auracron modificam mecânicas básicas
- Evoluções de habilidades afetam interações
- Passivas únicas por campeão
- Combos cross-realm específicos

### **Conexão com Economy & Items**
- Itens modificam mecânicas de combate
- Builds específicas para diferentes estratégias
- Economia baseada em performance em mecânicas
- Power spikes definidos por itens

---

**Nota**: Estas mecânicas formam a base do gameplay de AURACRON, sendo expandidas e modificadas pelos sistemas únicos do jogo como os Sígilos Auracron e o Dynamic Realm System.