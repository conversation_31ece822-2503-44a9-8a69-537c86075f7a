"""Objectives and Structures Tools for Unreal MCP.

This module provides tools for creating and managing game objectives and structures
including Primordial Dragons, Baron <PERSON>, Towers, and Inhibitors.
"""

import logging
from typing import Dict, List, Any, Optional
from mcp.server.fastmcp import FastMCP, Context

# Get logger
logger = logging.getLogger("UnrealMCP")

def register_objectives_structures_tools(mcp: FastMCP):
    """Register Objectives and Structures tools with the MCP server."""
    
    @mcp.tool()
    def create_primordial_dragons_system(
        ctx: Context,
        dragons_config: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Create the Primordial Dragons system with all dragon types.
        
        Args:
            ctx: MCP Context
            dragons_config: Dragons configuration as JSON object containing:
                - enable_infernal_dragon: Enable Infernal Dragon (default: True)
                - enable_ocean_dragon: Enable Ocean Dragon (default: True)
                - enable_mountain_dragon: Enable Mountain Dragon (default: True)
                - enable_cloud_dragon: Enable Cloud Dragon (default: True)
                - enable_elder_dragon: Enable Elder Dragon (default: True)
                - spawn_timer: Dragon spawn timer in seconds (default: 300)
        
        Returns:
            Dict[str, Any]: Operation result
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            # Default configuration for Primordial Dragons
            default_config = {
                "system_name": "primordial_dragons_system",
                "spawn_timer": 300,
                "dragons": {
                    "infernal_dragon": {
                        "name": "Dragão Infernal",
                        "enabled": True,
                        "health": 3500,
                        "health_per_minute": 240,
                        "damage": 300,
                        "damage_per_minute": 18,
                        "abilities": [
                            "Breath of Fire - AoE burn damage",
                            "Molten Armor - Reflects damage",
                            "Infernal Charge - Gap closer with knockback"
                        ],
                        "team_buff": {
                            "name": "Infernal Might",
                            "effect": "+8% Attack Damage and Ability Power",
                            "duration": 150,
                            "stacks": True,
                            "max_stacks": 3
                        },
                        "gold_reward": 25,
                        "experience_reward": 75
                    },
                    "ocean_dragon": {
                        "name": "Dragão Oceânico",
                        "enabled": True,
                        "health": 3200,
                        "health_per_minute": 220,
                        "damage": 280,
                        "damage_per_minute": 16,
                        "abilities": [
                            "Tidal Wave - Line skillshot with slow",
                            "Healing Waters - AoE heal over time",
                            "Aquatic Shield - Damage absorption"
                        ],
                        "team_buff": {
                            "name": "Ocean's Blessing",
                            "effect": "+6% missing health and mana regeneration",
                            "duration": 150,
                            "stacks": True,
                            "max_stacks": 3
                        },
                        "gold_reward": 25,
                        "experience_reward": 75
                    },
                    "mountain_dragon": {
                        "name": "Dragão da Montanha",
                        "enabled": True,
                        "health": 4000,
                        "health_per_minute": 280,
                        "damage": 320,
                        "damage_per_minute": 20,
                        "abilities": [
                            "Boulder Throw - High damage projectile",
                            "Stone Skin - Damage reduction",
                            "Earthquake - AoE knockup"
                        ],
                        "team_buff": {
                            "name": "Mountain's Fortitude",
                            "effect": "+6% bonus armor and magic resistance",
                            "duration": 150,
                            "stacks": True,
                            "max_stacks": 3
                        },
                        "gold_reward": 25,
                        "experience_reward": 75
                    },
                    "cloud_dragon": {
                        "name": "Dragão das Nuvens",
                        "enabled": True,
                        "health": 3000,
                        "health_per_minute": 200,
                        "damage": 260,
                        "damage_per_minute": 14,
                        "abilities": [
                            "Lightning Strike - Instant damage",
                            "Wind Barrier - Projectile blocking",
                            "Storm Flight - Temporary untargetability"
                        ],
                        "team_buff": {
                            "name": "Cloud's Swiftness",
                            "effect": "+3% movement speed and +6% ultimate CDR",
                            "duration": 150,
                            "stacks": True,
                            "max_stacks": 3
                        },
                        "gold_reward": 25,
                        "experience_reward": 75
                    },
                    "elder_dragon": {
                        "name": "Dragão Ancião",
                        "enabled": True,
                        "health": 6000,
                        "health_per_minute": 180,
                        "damage": 400,
                        "damage_per_minute": 45,
                        "spawn_requirement": "35 minutes + 2 dragon souls",
                        "abilities": [
                            "Elder's Wrath - Execute threshold",
                            "Ancient Roar - Fear and damage",
                            "Draconic Fury - Enrage mechanic"
                        ],
                        "team_buff": {
                            "name": "Aspect of the Dragon",
                            "effect": "Burning damage on attacks and spells",
                            "duration": 150,
                            "execute_threshold": "20% max health"
                        },
                        "gold_reward": 50,
                        "experience_reward": 150
                    }
                }
            }
            
            # Merge provided config with defaults
            if dragons_config:
                default_config.update(dragons_config)
            
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            logger.info(f"Creating Primordial Dragons system with config: {default_config}")
            response = unreal.send_command("create_primordial_dragons_system", default_config)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Primordial Dragons system creation response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error creating Primordial Dragons system: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def create_baron_auracron_system(
        ctx: Context,
        baron_config: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Create the Baron Auracron system - the ultimate objective.
        
        Args:
            ctx: MCP Context
            baron_config: Baron configuration as JSON object containing:
                - spawn_time: Baron spawn time in minutes (default: 20)
                - respawn_timer: Baron respawn timer in seconds (default: 420)
                - enable_phases: Enable Baron phase mechanics (default: True)
        
        Returns:
            Dict[str, Any]: Operation result
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            # Default configuration for Baron Auracron
            default_config = {
                "system_name": "baron_auracron_system",
                "spawn_time": 20,
                "respawn_timer": 420,
                "enable_phases": True,
                "baron_stats": {
                    "name": "Baron Auracron",
                    "health": 8000,
                    "health_per_minute": 180,
                    "damage": 400,
                    "damage_per_minute": 50,
                    "armor": 120,
                    "magic_resistance": 70,
                    "location": "Baron Pit - Upper River"
                },
                "abilities": {
                    "auracron_slam": {
                        "name": "Auracron Slam",
                        "type": "AoE Knockback",
                        "damage": 300,
                        "cooldown": 8,
                        "range": 400
                    },
                    "void_spikes": {
                        "name": "Void Spikes",
                        "type": "Line Skillshot",
                        "damage": 250,
                        "cooldown": 6,
                        "range": 900,
                        "effect": "Reduces healing by 50%"
                    },
                    "baron_gaze": {
                        "name": "Baron's Gaze",
                        "type": "Debuff",
                        "effect": "Reduces damage by 50%",
                        "duration": 4,
                        "cooldown": 20
                    },
                    "auracron_wrath": {
                        "name": "Auracron's Wrath",
                        "type": "Enrage",
                        "trigger": "Below 25% health",
                        "effect": "+100% attack speed, +50% damage"
                    }
                },
                "team_buff": {
                    "name": "Hand of Baron",
                    "effects": [
                        "+40 Attack Damage",
                        "+40 Ability Power",
                        "Empowered Recall (4 seconds)",
                        "Enhanced minions with Baron aura",
                        "Minions gain +50% damage to structures"
                    ],
                    "duration": 180,
                    "aura_range": 1200
                },
                "rewards": {
                    "gold_per_player": 300,
                    "experience_per_player": 150,
                    "team_gold": 1500
                }
            }
            
            # Merge provided config with defaults
            if baron_config:
                default_config.update(baron_config)
            
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            logger.info(f"Creating Baron Auracron system with config: {default_config}")
            response = unreal.send_command("create_baron_auracron_system", default_config)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Baron Auracron system creation response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error creating Baron Auracron system: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}

    @mcp.tool()
    def create_towers_system(
        ctx: Context,
        towers_config: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Create the Towers system for all lanes and layers.

        Args:
            ctx: MCP Context
            towers_config: Towers configuration as JSON object containing:
                - enable_outer_towers: Enable outer towers (default: True)
                - enable_inner_towers: Enable inner towers (default: True)
                - enable_inhibitor_towers: Enable inhibitor towers (default: True)
                - enable_nexus_towers: Enable nexus towers (default: True)

        Returns:
            Dict[str, Any]: Operation result
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            # Default configuration for Towers
            default_config = {
                "system_name": "towers_system",
                "tower_types": {
                    "outer_tower": {
                        "name": "Torre Externa",
                        "enabled": True,
                        "health": 2500,
                        "armor": 40,
                        "magic_resistance": 40,
                        "damage": 152,
                        "damage_per_minute": 4,
                        "attack_speed": 0.83,
                        "range": 775,
                        "gold_reward": 650,
                        "local_gold": 100,
                        "special_mechanics": [
                            "Fortification - 66% damage reduction for 5 minutes",
                            "Warming Up - Increasing damage on same target",
                            "Backdoor Protection - Extra resistances without minions"
                        ]
                    },
                    "inner_tower": {
                        "name": "Torre Interna",
                        "enabled": True,
                        "health": 3000,
                        "armor": 55,
                        "magic_resistance": 55,
                        "damage": 170,
                        "damage_per_minute": 4,
                        "attack_speed": 0.83,
                        "range": 775,
                        "gold_reward": 700,
                        "local_gold": 125,
                        "special_mechanics": [
                            "Enhanced Fortification - 75% damage reduction",
                            "Laser Focus - True damage after 3 shots",
                            "Structural Integrity - Requires outer tower down"
                        ]
                    },
                    "inhibitor_tower": {
                        "name": "Torre do Inibidor",
                        "enabled": True,
                        "health": 4000,
                        "armor": 65,
                        "magic_resistance": 65,
                        "damage": 200,
                        "damage_per_minute": 4,
                        "attack_speed": 0.83,
                        "range": 775,
                        "gold_reward": 750,
                        "local_gold": 150,
                        "special_mechanics": [
                            "Maximum Fortification - 80% damage reduction",
                            "Guardian Protocol - Shields nearby structures",
                            "Requires inner tower destruction"
                        ]
                    },
                    "nexus_tower": {
                        "name": "Torre do Nexus",
                        "enabled": True,
                        "health": 4500,
                        "armor": 75,
                        "magic_resistance": 75,
                        "damage": 230,
                        "damage_per_minute": 4,
                        "attack_speed": 0.83,
                        "range": 775,
                        "gold_reward": 50,
                        "local_gold": 50,
                        "special_mechanics": [
                            "Ultimate Fortification - 85% damage reduction",
                            "Nexus Defense - Regenerates health",
                            "Final Stand - Increased damage when alone"
                        ]
                    }
                },
                "global_mechanics": {
                    "fortification_duration": 300,
                    "backdoor_protection": True,
                    "warming_up_stacks": 6,
                    "warming_up_damage_per_stack": 25,
                    "structure_regeneration": True,
                    "regeneration_delay": 30
                }
            }

            # Merge provided config with defaults
            if towers_config:
                default_config.update(towers_config)

            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            logger.info(f"Creating Towers system with config: {default_config}")
            response = unreal.send_command("create_towers_system", default_config)

            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}

            logger.info(f"Towers system creation response: {response}")
            return response

        except Exception as e:
            error_msg = f"Error creating Towers system: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}

    @mcp.tool()
    def create_inhibitors_system(
        ctx: Context,
        inhibitors_config: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Create the Inhibitors system for all lanes.

        Args:
            ctx: MCP Context
            inhibitors_config: Inhibitors configuration as JSON object containing:
                - respawn_timer: Inhibitor respawn time in seconds (default: 300)
                - enable_super_minions: Enable super minions when destroyed (default: True)

        Returns:
            Dict[str, Any]: Operation result
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            # Default configuration for Inhibitors
            default_config = {
                "system_name": "inhibitors_system",
                "respawn_timer": 300,
                "enable_super_minions": True,
                "inhibitor_stats": {
                    "health": 4000,
                    "armor": 0,
                    "magic_resistance": 0,
                    "gold_reward": 50,
                    "local_gold": 50,
                    "experience_reward": 0
                },
                "inhibitor_mechanics": {
                    "requires_inhibitor_tower": True,
                    "spawns_super_minions": True,
                    "super_minion_duration": 300,
                    "respawn_warning": 60,
                    "destruction_announcement": True
                },
                "super_minion_stats": {
                    "health": 1500,
                    "health_per_minute": 200,
                    "damage": 190,
                    "damage_per_minute": 3,
                    "armor": 30,
                    "magic_resistance": -30,
                    "gold_reward": 60,
                    "experience_reward": 92,
                    "special_abilities": [
                        "Damage reduction vs champions",
                        "Increased damage vs structures",
                        "Immunity to displacement"
                    ]
                },
                "lane_configuration": {
                    "top_lane": {"enabled": True, "position": "Top Base"},
                    "mid_lane": {"enabled": True, "position": "Mid Base"},
                    "bot_lane": {"enabled": True, "position": "Bot Base"}
                }
            }

            # Merge provided config with defaults
            if inhibitors_config:
                default_config.update(inhibitors_config)

            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            logger.info(f"Creating Inhibitors system with config: {default_config}")
            response = unreal.send_command("create_inhibitors_system", default_config)

            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}

            logger.info(f"Inhibitors system creation response: {response}")
            return response

        except Exception as e:
            error_msg = f"Error creating Inhibitors system: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
