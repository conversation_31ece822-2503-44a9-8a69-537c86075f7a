// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "Engine/Engine.h"
#include "Engine/World.h"
#include "GameFramework/Actor.h"
#include "Components/ActorComponent.h"
#include "Engine/StaticMeshActor.h"
#include "Components/StaticMeshComponent.h"
#include "Materials/MaterialInterface.h"
#include "Engine/Texture2D.h"
#include "Dom/JsonObject.h"
#include "Serialization/JsonSerializer.h"
#include "Serialization/JsonWriter.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/FileHelper.h"
#include "Engine/GameEngine.h"
#include "Kismet/GameplayStatics.h"
#include "Engine/LevelStreaming.h"
#include "Engine/WorldComposition.h"
#include "Engine/LevelStreamingDynamic.h"
#include "Streaming/LevelStreamingDelegates.h"
#include "Engine/AssetManager.h"
#include "Engine/StreamableManager.h"
#include "HAL/ThreadSafeBool.h"
#include "Async/AsyncWork.h"
#include "Containers/Queue.h"
#include "Engine/NetDriver.h"
#include "Components/BrushComponent.h"
#include "GameFramework/GameModeBase.h"
#include "GameFramework/PlayerController.h"
#include "Components/SceneComponent.h"
#include "Engine/CollisionProfile.h"
#include "PhysicsEngine/BodySetup.h"
#include "Components/PrimitiveComponent.h"
#include "Engine/StaticMesh.h"
#include "Materials/Material.h"
#include "Particles/ParticleSystem.h"
#include "Sound/SoundBase.h"
#include "UObject/SoftObjectPath.h"
#include "Engine/AssetUserData.h"
#include "WorldPartition/WorldPartition.h"
#include "WorldPartition/WorldPartitionSubsystem.h"
#include "Subsystems/WorldSubsystem.h"
#include "Engine/LevelScriptActor.h"
#include "Components/BoxComponent.h"
#include "Components/SphereComponent.h"
#include "Engine/TriggerVolume.h"
#include "GameFramework/Volume.h"
#include "Engine/PostProcessVolume.h"
#include "Components/PostProcessComponent.h"
#include "Engine/ReplicationDriver.h"
#include "Net/UnrealNetwork.h"
#include "GameFramework/GameNetworkManager.h"
#include "Engine/NetConnection.h"
#include "GameFramework/OnlineReplStructs.h"
#include "Engine/DemoNetDriver.h"
#include "Misc/NetworkGuid.h"
#include "GameFramework/Info.h"
#include "GameFramework/GameStateBase.h"
#include "GameFramework/PlayerState.h"
#include "Components/WidgetComponent.h"
#include "Blueprint/UserWidget.h"
#include "Components/TextBlock.h"
#include "Components/Button.h"
#include "Components/Image.h"
#include "Components/ProgressBar.h"
#include "Components/Slider.h"
#include "Animation/AnimationAsset.h"
#include "Animation/AnimSequence.h"
#include "Animation/AnimBlueprint.h"
#include "Animation/AnimInstance.h"
#include "Components/SkeletalMeshComponent.h"
#include "Engine/SkeletalMesh.h"
#include "Materials/MaterialParameterCollection.h"
#include "Materials/MaterialParameterCollectionInstance.h"
#include "Engine/DirectionalLight.h"
#include "Engine/PointLight.h"
#include "Engine/SpotLight.h"
#include "Engine/SkyLight.h"
#include "Components/LightComponent.h"
#include "Components/DirectionalLightComponent.h"
#include "Components/PointLightComponent.h"
#include "Components/SpotLightComponent.h"
#include "Components/SkyLightComponent.h"
#include "Engine/TextureCube.h"
#include "Engine/ReflectionCapture.h"
#include "Components/ReflectionCaptureComponent.h"
#include "Engine/Scene.h"
#include "EngineUtils.h"
#include "Engine/Selection.h"
#include "Editor/EditorEngine.h"
#include "UnrealEdGlobals.h"
#include "Editor/UnrealEdEngine.h"
#include "AssetRegistry/AssetRegistryModule.h"
#include "Developer/AssetTools/Public/AssetToolsModule.h"
#include "Editor/ContentBrowser/Public/ContentBrowserModule.h"
#include "Framework/Notifications/NotificationManager.h"
#include "Widgets/Notifications/SNotificationList.h"
#include "Misc/MessageDialog.h"
#include "HAL/PlatformApplicationMisc.h"
#include "GenericPlatform/GenericPlatformMisc.h"
#include "Misc/App.h"
#include "Misc/DateTime.h"
#include "Misc/Timespan.h"
#include "HAL/PlatformProcess.h"
#include "Misc/Paths.h"
#include "HAL/FileManager.h"
#include "Misc/ConfigCacheIni.h"
#include "GameFramework/SaveGame.h"
#include "Engine/LocalPlayer.h"
#include "GameFramework/HUD.h"
#include "Engine/Canvas.h"
#include "Engine/Font.h"
#include "Slate/SceneViewport.h"
#include "Framework/Application/SlateApplication.h"
#include "Input/Events.h"
#include "GenericPlatform/ICursor.h"
#include "Widgets/SViewport.h"
#include "Widgets/SWindow.h"
#include "Framework/Application/SlateUser.h"
#include "Input/Reply.h"
#include "Layout/Geometry.h"
#include "Math/Vector2D.h"
#include "Math/IntPoint.h"
#include "Math/IntRect.h"
#include "RenderResource.h"
#include "RenderingThread.h"
#include "GlobalShader.h"
#include "ShaderParameters.h"
#include "RHICommandList.h"
#include "RHI.h"
#include "RHIResources.h"
#include "RendererInterface.h"
#include "SceneInterface.h"
#include "PrimitiveSceneProxy.h"
#include "MeshPassProcessor.h"
#include "SceneManagement.h"
#include "PrimitiveViewRelevance.h"
#include "Materials/MaterialRenderProxy.h"
#include "StaticMeshResources.h"
#include "Engine/TextureRenderTarget2D.h"
#include "Engine/CanvasRenderTarget2D.h"
#include "Components/SceneCaptureComponent2D.h"
#include "Engine/SceneCapture2D.h"
#include "Camera/CameraComponent.h"
#include "GameFramework/SpringArmComponent.h"
#include "GameFramework/Pawn.h"
#include "GameFramework/Character.h"
#include "GameFramework/CharacterMovementComponent.h"
#include "Components/CapsuleComponent.h"
#include "PhysicsEngine/PhysicsSettings.h"
#include "Engine/RendererSettings.h"
#include "PhysicsEngine/BodyInstance.h"
#include "PhysicsEngine/PhysicsAsset.h"
#include "PhysicsEngine/PhysicsConstraintComponent.h"
#include "PhysicsEngine/RadialForceComponent.h"
#include "Chaos/ChaosEngineInterface.h"
#include "Chaos/ChaosSolverActor.h"
#include "Field/FieldSystemActor.h"
#include "Field/FieldSystemComponent.h"
#include "GeometryCollection/GeometryCollectionActor.h"
#include "GeometryCollection/GeometryCollectionComponent.h"
#include "NiagaraActor.h"
#include "NiagaraComponent.h"
#include "NiagaraSystem.h"
#include "NiagaraEmitter.h"
#include "NiagaraScript.h"
#include "NiagaraParameterCollection.h"
#include "Landscape.h"
#include "LandscapeProxy.h"
#include "LandscapeComponent.h"
#include "LandscapeInfo.h"
#include "LandscapeLayerInfoObject.h"
#include "LandscapeMaterialInstanceConstant.h"
#include "LandscapeGrassType.h"
#include "LandscapeSplineActor.h"
#include "LandscapeSplineControlPoint.h"
#include "LandscapeSplineSegment.h"
#include "FoliageType.h"
#include "InstancedFoliageActor.h"
#include "Components/InstancedStaticMeshComponent.h"
#include "Components/HierarchicalInstancedStaticMeshComponent.h"
#include "AI/NavigationSystemBase.h"
#include "NavigationSystem.h"
#include "NavMesh/NavMeshBoundsVolume.h"
#include "NavMesh/RecastNavMesh.h"
#include "NavigationData.h"
#include "Navigation/NavLinkProxy.h"
#include "NavModifierVolume.h"
#include "NavMesh/NavMeshRenderingComponent.h"
#include "AIController.h"
#include "BehaviorTree/BehaviorTree.h"
#include "BehaviorTree/BehaviorTreeComponent.h"
#include "BehaviorTree/BlackboardComponent.h"
#include "BehaviorTree/Blackboard/BlackboardKeyType.h"
#include "Perception/AIPerceptionComponent.h"
#include "Perception/AISenseConfig.h"
#include "Perception/AISense_Sight.h"
#include "Perception/AISense_Hearing.h"
#include "Perception/AISense_Touch.h"
#include "Perception/AISense_Damage.h"
#include "Perception/AIPerceptionStimuliSourceComponent.h"
#include "GameplayTasksComponent.h"
#include "GameplayTask.h"
#include "AITypes.h"
#include "Navigation/PathFollowingComponent.h"
#include "Navigation/CrowdFollowingComponent.h"
#include "DetourCrowdAIController.h"
#include "MassEntitySubsystem.h"
#include "MassEntityManager.h"
#include "MassProcessingTypes.h"
#include "MassProcessor.h"
#include "MassMovementFragments.h"
#include "MassCommonFragments.h"
#include "MassRepresentationFragments.h"
#include "MassLODFragments.h"
#include "MassSimulationSubsystem.h"
#include "MassSpawner.h"
#include "MassEntityConfigAsset.h"
#include "GameplayTagContainer.h"
#include "GameplayTagsManager.h"
#include "GameplayTagAssetInterface.h"
#include "DataRegistry.h"
#include "DataRegistrySubsystem.h"
#include "DataRegistrySource.h"
#include "Engine/DataTable.h"
#include "Engine/CurveTable.h"
#include "Engine/UserDefinedStruct.h"
#include "Engine/UserDefinedEnum.h"
#include "Engine/Blueprint.h"
#include "Engine/BlueprintGeneratedClass.h"
#include "Kismet2/BlueprintEditorUtils.h"
#include "K2Node.h"
#include "K2Node_Event.h"
#include "K2Node_CallFunction.h"
#include "K2Node_VariableGet.h"
#include "K2Node_VariableSet.h"
#include "KismetCompilerModule.h"
#include "ToolMenus.h"
#include "Framework/MultiBox/MultiBoxBuilder.h"
#include "Styling/AppStyle.h"
#include "Styling/SlateStyle.h"
#include "Brushes/SlateImageBrush.h"
#include "Styling/SlateTypes.h"
#include "Widgets/Input/SButton.h"
#include "Widgets/Input/SCheckBox.h"
#include "Widgets/Input/SSlider.h"
#include "Widgets/Input/SSpinBox.h"
#include "Widgets/Input/SEditableTextBox.h"
#include "Widgets/Input/SMultiLineEditableTextBox.h"
#include "Widgets/Input/SComboBox.h"
#include "Widgets/Layout/SBox.h"
#include "Widgets/Layout/SBorder.h"
#include "Widgets/Layout/SSplitter.h"
#include "Widgets/Layout/SScrollBox.h"
#include "Widgets/Layout/SExpandableArea.h"
#include "Widgets/Text/STextBlock.h"
#include "Widgets/Images/SImage.h"
#include "Widgets/Views/SListView.h"
#include "Widgets/Views/STreeView.h"
#include "Widgets/Views/STableViewBase.h"
#include "Widgets/Views/STableRow.h"
#include "Widgets/Views/SHeaderRow.h"
#include "Framework/Docking/TabManager.h"
#include "Framework/Docking/WorkspaceItem.h"
#include "Widgets/Docking/SDockTab.h"
#include "DesktopPlatformModule.h"
#include "IDesktopPlatform.h"
#include "GenericPlatform/GenericPlatformFile.h"
#include "Misc/SecureHash.h"
#include "Misc/Base64.h"
#include "Misc/Compression.h"
#include "Serialization/Archive.h"
#include "Serialization/MemoryWriter.h"
#include "Serialization/MemoryReader.h"
#include "Serialization/BufferArchive.h"
#include "Serialization/ObjectAndNameAsStringProxyArchive.h"
#include "UObject/SavePackage.h"
#include "UObject/Package.h"
#include "UObject/UObjectGlobals.h"
#include "UObject/UObjectHash.h"
#include "UObject/UObjectIterator.h"
#include "UObject/PropertyPortFlags.h"
#include "UObject/TextProperty.h"

/**
 * Classe responsável por gerenciar comandos do Sistema de Mapa Multicamada.
 * 
 * Esta classe fornece funcionalidades para:
 * - Criar as três camadas específicas do AURACRON (Planície Radiante, Firmamento Zephyr, Abismo Umbral)
 * - Configurar mecânicas específicas de cada camada
 * - Gerenciar conectividade entre camadas
 * - Configurar pathfinding multicamada
 * - Otimizar performance do sistema
 */
class UNREALMCP_API FUnrealMCPMultilayerMapCommands
{
public:
    FUnrealMCPMultilayerMapCommands();
    ~FUnrealMCPMultilayerMapCommands();

    // Método principal para processar comandos
    TSharedPtr<FJsonObject> HandleCommand(const FString& CommandType, const TSharedPtr<FJsonObject>& Params);
    
    // ========================================================================
    // Métodos Principais do Sistema de Mapa Multicamada
    // ========================================================================

    /**
     * Cria a camada Planície Radiante (camada inferior Z: 0-2000).
     * 
     * @param CommandData Dados do comando contendo configurações da camada
     * @return Resposta JSON com informações da camada criada
     */
    TSharedPtr<FJsonObject> HandleCreatePlanicieRadianteLayer(const TSharedPtr<FJsonObject>& CommandData);

    /**
     * Cria a camada Firmamento Zephyr (camada média Z: 2000-4000).
     * 
     * @param CommandData Dados do comando contendo configurações da camada
     * @return Resposta JSON com informações da camada criada
     */
    TSharedPtr<FJsonObject> HandleCreateFirmamentoZephyrLayer(const TSharedPtr<FJsonObject>& CommandData);

    /**
     * Cria a camada Abismo Umbral (camada superior Z: 4000-6000).
     * 
     * @param CommandData Dados do comando contendo configurações da camada
     * @return Resposta JSON com informações da camada criada
     */
    TSharedPtr<FJsonObject> HandleCreateAbismoUmbralLayer(const TSharedPtr<FJsonObject>& CommandData);

    /**
     * Configura o sistema completo de três camadas.
     * 
     * @param CommandData Dados do comando contendo configurações do sistema
     * @return Resposta JSON com informações do sistema configurado
     */
    TSharedPtr<FJsonObject> HandleSetupCompleteMultilayerSystem(const TSharedPtr<FJsonObject>& CommandData);

    // ========================================================================
    // Constantes Públicas
    // ========================================================================

    // Tipos de resposta
    static const FString RESPONSE_SUCCESS;
    static const FString RESPONSE_ERROR;
    static const FString RESPONSE_WARNING;
    static const FString RESPONSE_INFO;

private:
    // ========================================================================
    // Constantes e Enums Privados
    // ========================================================================

    // Nomes das camadas
    static const FString LAYER_PLANICIE_RADIANTE;
    static const FString LAYER_FIRMAMENTO_ZEPHYR;
    static const FString LAYER_ABISMO_UMBRAL;

    // Ranges Z das camadas
    static const float PLANICIE_Z_MIN;
    static const float PLANICIE_Z_MAX;
    static const float FIRMAMENTO_Z_MIN;
    static const float FIRMAMENTO_Z_MAX;
    static const float ABISMO_Z_MIN;
    static const float ABISMO_Z_MAX;

    // ========================================================================
    // Funções Auxiliares
    // ========================================================================

    /**
     * Cria resposta de erro.
     */
    TSharedPtr<FJsonObject> CreateErrorResponse(const FString& ErrorMessage, const FString& ErrorCode = TEXT("MULTILAYER_ERROR"));

    /**
     * Cria resposta de sucesso.
     */
    TSharedPtr<FJsonObject> CreateSuccessResponse(const TSharedPtr<FJsonObject>& Data, const FString& Message = TEXT("Operation completed successfully"));

    /**
     * Valida configuração de camada.
     */
    bool ValidateLayerConfig(const TSharedPtr<FJsonObject>& LayerConfig, FString& ErrorMessage);

    // ========================================================================
    // Variáveis Privadas
    // ========================================================================

    /** Cache de configurações de camadas */
    TMap<FString, TSharedPtr<FJsonObject>> LayerConfigCache;

    /** Estado atual das camadas */
    TMap<FString, TSharedPtr<FJsonObject>> LayerStates;

    /** Flag para indicar se o sistema está inicializado */
    bool bIsInitialized;

    /** Timestamp da última atualização */
    FDateTime LastUpdateTime;
};
