// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "Dom/JsonObject.h"

/**
 * Classe responsável por gerenciar comandos de Objetivos e Estruturas.
 * 
 * Esta classe fornece funcionalidades para:
 * - <PERSON><PERSON><PERSON> siste<PERSON> de Dragões Primordiais (Infernal, Ocean, Mountain, Cloud, Elder)
 * - Criar siste<PERSON> (spawn 20min, Hand of Baron buff)
 * - Cria<PERSON> <PERSON><PERSON><PERSON> (Outer, Inner, Inhibitor, Nexus)
 * - Criar sistema de Inibidores (respawn 5min, super minions)
 */
class UNREALMCP_API FUnrealMCPObjectivesStructuresCommands
{
public:
    FUnrealMCPObjectivesStructuresCommands();
    ~FUnrealMCPObjectivesStructuresCommands();

    // Método principal para processar comandos
    TSharedPtr<FJsonObject> HandleCommand(const FString& CommandType, const TSharedPtr<FJsonObject>& Params);
    
    // ========================================================================
    // Métodos Principais de Objetivos e Estruturas
    // ========================================================================

    /**
     * Cria sistema de Dragões Primordiais.
     */
    TSharedPtr<FJsonObject> HandleCreatePrimordialDragonsSystem(const TSharedPtr<FJsonObject>& CommandData);

    /**
     * Cria sistema de Baron Auracron.
     */
    TSharedPtr<FJsonObject> HandleCreateBaronAuracronSystem(const TSharedPtr<FJsonObject>& CommandData);

    /**
     * Cria sistema de Torres.
     */
    TSharedPtr<FJsonObject> HandleCreateTowerSystem(const TSharedPtr<FJsonObject>& CommandData);

    /**
     * Cria sistema de Inibidores.
     */
    TSharedPtr<FJsonObject> HandleCreateInhibitorSystem(const TSharedPtr<FJsonObject>& CommandData);

    // ========================================================================
    // Constantes Públicas
    // ========================================================================

    // Tipos de resposta
    static const FString RESPONSE_SUCCESS;
    static const FString RESPONSE_ERROR;
    static const FString RESPONSE_WARNING;
    static const FString RESPONSE_INFO;

private:
    // ========================================================================
    // Funções Auxiliares
    // ========================================================================

    /**
     * Cria resposta de erro.
     */
    TSharedPtr<FJsonObject> CreateErrorResponse(const FString& ErrorMessage, const FString& ErrorCode = TEXT("OBJECTIVES_STRUCTURES_ERROR"));

    /**
     * Cria resposta de sucesso.
     */
    TSharedPtr<FJsonObject> CreateSuccessResponse(const TSharedPtr<FJsonObject>& Data, const FString& Message = TEXT("Operation completed successfully"));

    // ========================================================================
    // Variáveis Privadas
    // ========================================================================

    /** Cache de configurações de objetivos */
    TMap<FString, TSharedPtr<FJsonObject>> ObjectiveConfigCache;

    /** Estado atual dos objetivos */
    TMap<FString, TSharedPtr<FJsonObject>> ObjectiveStates;

    /** Flag para indicar se o sistema está inicializado */
    bool bIsInitialized;

    /** Timestamp da última atualização */
    FDateTime LastUpdateTime;
};
