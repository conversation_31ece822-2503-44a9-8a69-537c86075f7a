{"Version": "1.2", "Data": {"Source": "c:\\game\\auracron\\plugins\\unrealmcp\\source\\unrealmcp\\private\\commands\\unrealmcpobjectivesstructurescommands.cpp", "ProvidedModule": "", "PCH": "c:\\game\\auracron\\intermediate\\build\\win64\\x64\\auracroneditor\\development\\unrealed\\sharedpch.unrealed.project.valapi.valexpapi.cpp20.h.pch", "Includes": ["c:\\game\\auracron\\plugins\\unrealmcp\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\unrealmcp\\definitions.unrealmcp.h", "c:\\game\\auracron\\plugins\\unrealmcp\\source\\unrealmcp\\public\\commands\\unrealmcpobjectivesstructurescommands.h"], "ImportedModules": [], "ImportedHeaderUnits": []}}