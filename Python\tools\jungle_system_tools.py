"""Jungle System Tools for Unreal MCP.

This module provides tools for creating and managing the jungle system
with monsters per layer, buffs, and optimized clear routes.
"""

import logging
from typing import Dict, List, Any, Optional
from mcp.server.fastmcp import FastMCP, Context

# Get logger
logger = logging.getLogger("UnrealMCP")

def register_jungle_system_tools(mcp: FastMCP):
    """Register Jungle System tools with the MCP server."""
    
    @mcp.tool()
    def create_planicie_radiante_jungle(
        ctx: Context,
        jungle_config: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Create jungle monsters for Planície Radiante (Terrestrial Jungle).
        
        Args:
            ctx: MCP Context
            jungle_config: Jungle configuration as JSON object containing:
                - enable_red_buff: Enable Red Buff - Guardi<PERSON> (default: True)
                - enable_blue_buff: Enable Blue Buff - Sentinela Azul (default: True)
                - enable_wolves: Enable Wolves - <PERSON><PERSON><PERSON> (default: True)
                - enable_gromp: Enable Gromp - <PERSON><PERSON> (default: True)
                - enable_krugs: Enable Krugs - Go<PERSON><PERSON>ed<PERSON> (default: True)
                - enable_raptors: Enable Raptors - Aves Predadoras (default: True)
                - camps_count: Total number of jungle camps (default: 24)
        
        Returns:
            Dict[str, Any]: Operation result
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            # Default configuration for Planície Radiante Jungle
            default_config = {
                "jungle_name": "planicie_radiante_jungle",
                "layer_name": "planicie_radiante",
                "jungle_type": "terrestrial",
                "camps_count": 24,
                "monsters": {
                    "red_buff": {
                        "name": "Guardião Carmesim",
                        "enabled": True,
                        "health": 1800,
                        "health_per_minute": 180,
                        "damage": 78,
                        "damage_per_minute": 6,
                        "buff_effects": ["+20% Slow em ataques", "+5 HP/s regeneração"],
                        "buff_duration": 90,
                        "respawn_time": 300
                    },
                    "blue_buff": {
                        "name": "Sentinela Azul",
                        "enabled": True,
                        "health": 1700,
                        "health_per_minute": 170,
                        "damage": 72,
                        "damage_per_minute": 5,
                        "buff_effects": ["+10% CDR", "+5 MP/s regeneração"],
                        "buff_duration": 90,
                        "respawn_time": 300
                    },
                    "wolves": {
                        "name": "Matilha Sombria",
                        "enabled": True,
                        "large_wolf_health": 1200,
                        "large_wolf_health_per_minute": 120,
                        "small_wolves_health": 400,
                        "small_wolves_health_per_minute": 40,
                        "small_wolves_count": 2,
                        "total_gold": 95,
                        "respawn_time": 120
                    },
                    "gromp": {
                        "name": "Sapo Gigante",
                        "enabled": True,
                        "health": 1500,
                        "health_per_minute": 150,
                        "damage": 84,
                        "damage_per_minute": 7,
                        "passive": "Poison que causa dano ao longo do tempo",
                        "gold": 105,
                        "respawn_time": 120
                    },
                    "krugs": {
                        "name": "Golems de Pedra",
                        "enabled": True,
                        "large_krug_health": 1000,
                        "large_krug_health_per_minute": 100,
                        "medium_krug_health": 600,
                        "medium_krug_health_per_minute": 60,
                        "small_krugs_health": 200,
                        "small_krugs_health_per_minute": 20,
                        "small_krugs_count": 4,
                        "total_gold": 84,
                        "respawn_time": 135
                    },
                    "raptors": {
                        "name": "Aves Predadoras",
                        "enabled": True,
                        "large_raptor_health": 800,
                        "large_raptor_health_per_minute": 80,
                        "small_raptors_health": 250,
                        "small_raptors_health_per_minute": 25,
                        "small_raptors_count": 5,
                        "total_gold": 100,
                        "respawn_time": 120
                    }
                }
            }
            
            # Merge provided config with defaults
            if jungle_config:
                default_config.update(jungle_config)
            
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            logger.info(f"Creating Planície Radiante jungle with config: {default_config}")
            response = unreal.send_command("create_planicie_radiante_jungle", default_config)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Planície Radiante jungle creation response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error creating Planície Radiante jungle: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def create_firmamento_zephyr_jungle(
        ctx: Context,
        jungle_config: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Create jungle monsters for Firmamento Zephyr (Aerial Jungle).
        
        Args:
            ctx: MCP Context
            jungle_config: Jungle configuration as JSON object containing:
                - enable_storm_elemental: Enable Storm Elemental (default: True)
                - enable_wind_spirits: Enable Wind Spirits (default: True)
                - enable_cloud_drakes: Enable Cloud Drakes (default: True)
        
        Returns:
            Dict[str, Any]: Operation result
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            # Default configuration for Firmamento Zephyr Jungle
            default_config = {
                "jungle_name": "firmamento_zephyr_jungle",
                "layer_name": "firmamento_zephyr",
                "jungle_type": "aerial",
                "monsters": {
                    "storm_elemental": {
                        "name": "Elemental da Tempestade",
                        "enabled": True,
                        "health": 2200,
                        "health_per_minute": 200,
                        "abilities": ["Raios que saltam entre alvos"],
                        "buff_effects": ["+15% Velocidade de Movimento", "imunidade a slows"],
                        "buff_duration": 120,
                        "respawn_time": 360
                    },
                    "wind_spirits": {
                        "name": "Espíritos do Vento",
                        "enabled": True,
                        "large_spirit_health": 900,
                        "large_spirit_health_per_minute": 90,
                        "small_spirits_health": 300,
                        "small_spirits_health_per_minute": 30,
                        "small_spirits_count": 2,
                        "passive": "Empurram inimigos ao morrer",
                        "total_gold": 110,
                        "respawn_time": 150
                    },
                    "cloud_drakes": {
                        "name": "Dragões das Nuvens",
                        "enabled": True,
                        "alpha_drake_health": 1100,
                        "alpha_drake_health_per_minute": 110,
                        "beta_drakes_health": 400,
                        "beta_drakes_health_per_minute": 40,
                        "beta_drakes_count": 2,
                        "ability": "Voo curto que evita ataques terrestres",
                        "total_gold": 95,
                        "respawn_time": 135
                    }
                }
            }
            
            # Merge provided config with defaults
            if jungle_config:
                default_config.update(jungle_config)
            
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            logger.info(f"Creating Firmamento Zephyr jungle with config: {default_config}")
            response = unreal.send_command("create_firmamento_zephyr_jungle", default_config)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Firmamento Zephyr jungle creation response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error creating Firmamento Zephyr jungle: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def create_abismo_umbral_jungle(
        ctx: Context,
        jungle_config: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Create jungle monsters for Abismo Umbral (Underground Jungle).
        
        Args:
            ctx: MCP Context
            jungle_config: Jungle configuration as JSON object containing:
                - enable_shadow_wraith: Enable Shadow Wraith (default: True)
                - enable_void_spiders: Enable Void Spiders (default: True)
                - enable_bone_collectors: Enable Bone Collectors (default: True)
        
        Returns:
            Dict[str, Any]: Operation result
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            # Default configuration for Abismo Umbral Jungle
            default_config = {
                "jungle_name": "abismo_umbral_jungle",
                "layer_name": "abismo_umbral",
                "jungle_type": "underground",
                "monsters": {
                    "shadow_wraith": {
                        "name": "Espectro Sombrio",
                        "enabled": True,
                        "health": 2000,
                        "health_per_minute": 180,
                        "abilities": ["Invisibilidade temporária"],
                        "buff_effects": ["+20% Lethality", "+10% Spell Vamp"],
                        "buff_duration": 100,
                        "respawn_time": 330
                    },
                    "void_spiders": {
                        "name": "Aranhas do Vazio",
                        "enabled": True,
                        "queen_spider_health": 1000,
                        "queen_spider_health_per_minute": 100,
                        "spiderlings_health": 200,
                        "spiderlings_health_per_minute": 20,
                        "spiderlings_count": 4,
                        "passive": "Spawnam filhotes ao morrer",
                        "total_gold": 105,
                        "respawn_time": 165
                    },
                    "bone_collectors": {
                        "name": "Coletores de Ossos",
                        "enabled": True,
                        "large_collector_health": 850,
                        "large_collector_health_per_minute": 85,
                        "small_collectors_health": 350,
                        "small_collectors_health_per_minute": 35,
                        "small_collectors_count": 2,
                        "ability": "Ressuscitam uma vez com 50% da vida",
                        "total_gold": 90,
                        "respawn_time": 150
                    }
                }
            }
            
            # Merge provided config with defaults
            if jungle_config:
                default_config.update(jungle_config)
            
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            logger.info(f"Creating Abismo Umbral jungle with config: {default_config}")
            response = unreal.send_command("create_abismo_umbral_jungle", default_config)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Abismo Umbral jungle creation response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error creating Abismo Umbral jungle: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}

    @mcp.tool()
    def create_scuttle_crabs_system(
        ctx: Context,
        scuttle_config: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Create the special Scuttle Crabs system for all layers.

        Args:
            ctx: MCP Context
            scuttle_config: Scuttle configuration as JSON object containing:
                - enable_terrestrial: Enable Terrestrial Scuttle Crab (default: True)
                - enable_ethereal: Enable Ethereal Scuttle Crab (default: True)
                - enable_shadow: Enable Shadow Scuttle Crab (default: True)

        Returns:
            Dict[str, Any]: Operation result
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            # Default configuration for Scuttle Crabs
            default_config = {
                "system_name": "scuttle_crabs_system",
                "scuttle_crabs": {
                    "terrestrial": {
                        "name": "Scuttle Crab Terrestre",
                        "enabled": True,
                        "location": "Rio da Planície Radiante",
                        "health": 1000,
                        "health_per_minute": 35,
                        "buff_effects": ["Visão", "velocidade em rio"],
                        "gold_min": 70,
                        "gold_max": 140,
                        "respawn_time": 150
                    },
                    "ethereal": {
                        "name": "Scuttle Crab Etérea",
                        "enabled": True,
                        "location": "Correntes de ar do Firmamento",
                        "health": 1200,
                        "health_per_minute": 40,
                        "buff_effects": ["Visão", "velocidade de movimento aéreo"],
                        "gold_min": 80,
                        "gold_max": 150,
                        "respawn_time": 180
                    },
                    "shadow": {
                        "name": "Scuttle Crab Sombria",
                        "enabled": True,
                        "location": "Túneis do Abismo",
                        "health": 900,
                        "health_per_minute": 30,
                        "buff_effects": ["Visão", "detecção de invisibilidade"],
                        "gold_min": 75,
                        "gold_max": 145,
                        "respawn_time": 165
                    }
                }
            }

            # Merge provided config with defaults
            if scuttle_config:
                default_config.update(scuttle_config)

            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            logger.info(f"Creating Scuttle Crabs system with config: {default_config}")
            response = unreal.send_command("create_scuttle_crabs_system", default_config)

            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}

            logger.info(f"Scuttle Crabs system creation response: {response}")
            return response

        except Exception as e:
            error_msg = f"Error creating Scuttle Crabs system: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}

    @mcp.tool()
    def setup_jungle_clear_routes(
        ctx: Context,
        routes_config: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Setup optimized jungle clear routes.

        Args:
            ctx: MCP Context
            routes_config: Routes configuration as JSON object containing:
                - enable_red_start: Enable Red Buff start route (default: True)
                - enable_blue_start: Enable Blue Buff start route (default: True)
                - enable_vertical_clear: Enable cross-realm vertical clear (default: True)

        Returns:
            Dict[str, Any]: Operation result
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            # Default configuration for Clear Routes
            default_config = {
                "system_name": "jungle_clear_routes",
                "routes": {
                    "red_start": {
                        "name": "Clear Iniciando no Red",
                        "enabled": True,
                        "sequence": ["Red Buff", "Krugs", "Raptors", "Wolves", "Blue Buff", "Gromp"],
                        "total_time_minutes": 3.5,
                        "level_reached": 4,
                        "health_remaining_percent": 60
                    },
                    "blue_start": {
                        "name": "Clear Iniciando no Blue",
                        "enabled": True,
                        "sequence": ["Blue Buff", "Gromp", "Wolves", "Raptors", "Red Buff", "Krugs"],
                        "total_time_minutes": 3.75,
                        "level_reached": 4,
                        "health_remaining_percent": 55
                    },
                    "vertical_clear": {
                        "name": "Clear Vertical (Cross-Realm)",
                        "enabled": True,
                        "sequence": ["Red Terrestre", "Storm Elemental", "Shadow Wraith", "Blue Terrestre"],
                        "total_time_minutes": 4.25,
                        "level_reached": 4,
                        "unique_buffs": 3
                    }
                }
            }

            # Merge provided config with defaults
            if routes_config:
                default_config.update(routes_config)

            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            logger.info(f"Setting up jungle clear routes with config: {default_config}")
            response = unreal.send_command("setup_jungle_clear_routes", default_config)

            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}

            logger.info(f"Jungle clear routes setup response: {response}")
            return response

        except Exception as e:
            error_msg = f"Error setting up jungle clear routes: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
