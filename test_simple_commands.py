import sys
sys.path.append('Python')
from unreal_mcp_server import get_unreal_connection
import json

print('🔍 DIAGNÓSTICO SIMPLES - COMANDOS BÁSICOS')
print('=' * 50)

conn = get_unreal_connection()
if not conn:
    print('❌ ERRO: Unreal Engine não está rodando')
    exit(1)

print('✅ Conexão TCP estabelecida!')
print()

# Testar apenas comandos que sabemos que funcionam
simple_commands = [
    ('ping', {}),
    ('get_project_name', {}),
    ('get_selected_actors', {}),
]

print('🧪 Testando comandos que funcionaram antes...')
for cmd_name, cmd_params in simple_commands:
    print(f'🎯 {cmd_name}...', end=' ')
    try:
        response = conn.send_command(cmd_name, cmd_params)
        if response and response.get('status') == 'success':
            print('✅ OK')
        else:
            error = response.get('error', 'Unknown') if response else 'No response'
            print(f'❌ ERRO: {error}')
    except Exception as e:
        print(f'❌ EXCEPTION: {e}')

print()
print('🔍 Testando comandos novos um por vez...')

# Testar comandos novos individualmente
new_commands = [
    ('get_engine_version', {}),
    ('get_world_info', {}),
    ('get_level_info', {}),
    ('list_available_commands', {}),
]

for cmd_name, cmd_params in new_commands:
    print(f'🎯 {cmd_name}...', end=' ')
    try:
        # Usar timeout menor para diagnóstico rápido
        conn.socket.settimeout(3)  # 3 segundos apenas
        response = conn.send_command(cmd_name, cmd_params)
        if response and response.get('status') == 'success':
            print('✅ OK')
        else:
            error = response.get('error', 'Unknown') if response else 'No response'
            print(f'❌ ERRO: {error}')
    except Exception as e:
        print(f'❌ TIMEOUT/EXCEPTION: {str(e)[:50]}...')
    finally:
        conn.socket.settimeout(5)  # Voltar ao timeout normal

print()
print('🔍 DIAGNÓSTICO COMPLETO')
print('Se os comandos básicos funcionam mas os novos dão timeout,')
print('o problema está na implementação dos novos comandos no C++.')
