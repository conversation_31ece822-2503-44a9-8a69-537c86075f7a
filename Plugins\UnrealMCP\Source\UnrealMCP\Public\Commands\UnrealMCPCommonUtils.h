#pragma once

#include "CoreMinimal.h"
#include "Json.h"
#include "EdGraph/EdGraphPin.h"
#include "Engine/World.h"
#include "GameFramework/Actor.h"

// Forward declarations
class AActor;
class UBlueprint;
class UEdGraph;
class UEdGraphNode;
class UEdGraphPin;
class UK2Node_Event;
class UK2Node_CallFunction;
class UK2Node_VariableGet;
class UK2Node_VariableSet;
class UK2Node_InputAction;
class UK2Node_Self;
class UFunction;
class AStaticMeshActor;
class ASkeletalMeshActor;
class APointLight;
class ASpotLight;
class ADirectionalLight;
class ATriggerVolume;
class ABlockingVolume;
class ACharacter;
class APawn;
class AAIController;
class UStaticMeshComponent;
class USkeletalMeshComponent;
class UBoxComponent;
class USphereComponent;
class UCapsuleComponent;
class UStaticMesh;
class USkeletalMesh;
class UMaterialInterface;
class UBehaviorTree;
class UBlackboardData;

/**
 * MCP Actor creation parameters structure
 */
struct UNREALMCP_API FMCPActorCreationParams
{
    // Basic actor properties
    FString ActorName;
    FString ActorType;
    FVector Location = FVector::ZeroVector;
    FRotator Rotation = FRotator::ZeroRotator;
    FVector Scale = FVector::OneVector;

    // Asset paths
    FString StaticMeshPath;
    FString SkeletalMeshPath;
    FString MaterialPath;
    FString BlueprintPath;
    FString BehaviorTreePath;
    FString BlackboardPath;

    // Component properties
    TMap<FString, FString> ComponentProperties;
    TMap<FString, float> FloatProperties;
    TMap<FString, bool> BoolProperties;
    TMap<FString, FVector> VectorProperties;
    TMap<FString, FRotator> RotatorProperties;
    TMap<FString, FLinearColor> ColorProperties;

    // AI properties
    bool bEnableAI = false;
    FString AIControllerClass;

    // Physics properties
    bool bEnablePhysics = false;
    bool bEnableCollision = true;
    float Mass = 1.0f;

    // Rendering properties
    bool bCastShadow = true;
    bool bReceivesDecals = true;
    int32 RenderPriority = 0;

    FMCPActorCreationParams()
    {
        ActorName = TEXT("");
        ActorType = TEXT("Actor");
    }
};

/**
 * Common utilities for UnrealMCP commands
 */
class UNREALMCP_API FUnrealMCPCommonUtils
{
public:
    // JSON utilities
    static TSharedPtr<FJsonObject> CreateErrorResponse(const FString& Message);
    static TSharedPtr<FJsonObject> CreateSuccessResponse(const TSharedPtr<FJsonObject>& Data = nullptr);
    static void GetIntArrayFromJson(const TSharedPtr<FJsonObject>& JsonObject, const FString& FieldName, TArray<int32>& OutArray);
    static void GetFloatArrayFromJson(const TSharedPtr<FJsonObject>& JsonObject, const FString& FieldName, TArray<float>& OutArray);
    static FVector2D GetVector2DFromJson(const TSharedPtr<FJsonObject>& JsonObject, const FString& FieldName);
    static FVector GetVectorFromJson(const TSharedPtr<FJsonObject>& JsonObject, const FString& FieldName);
    static FRotator GetRotatorFromJson(const TSharedPtr<FJsonObject>& JsonObject, const FString& FieldName);
    
    // Actor utilities
    static TSharedPtr<FJsonValue> ActorToJson(AActor* Actor);
    static TSharedPtr<FJsonObject> ActorToJsonObject(AActor* Actor, bool bDetailed = false);

    // Advanced Actor Creation System
    static AActor* CreateActor(UWorld* World, const FMCPActorCreationParams& Params);
    static AActor* CreateActorFromJson(UWorld* World, const TSharedPtr<FJsonObject>& JsonParams);
    static FMCPActorCreationParams ParseActorCreationParams(const TSharedPtr<FJsonObject>& JsonParams);
    static bool ConfigureActorComponents(AActor* Actor, const FMCPActorCreationParams& Params);
    static bool ConfigureActorProperties(AActor* Actor, const FMCPActorCreationParams& Params);
    static bool ConfigureActorPhysics(AActor* Actor, const FMCPActorCreationParams& Params);
    static bool ConfigureActorRendering(AActor* Actor, const FMCPActorCreationParams& Params);
    static bool ConfigureActorAI(AActor* Actor, const FMCPActorCreationParams& Params);

    // Specialized Actor Creation
    static AStaticMeshActor* CreateStaticMeshActor(UWorld* World, const FMCPActorCreationParams& Params);
    static ASkeletalMeshActor* CreateSkeletalMeshActor(UWorld* World, const FMCPActorCreationParams& Params);
    static APointLight* CreatePointLight(UWorld* World, const FMCPActorCreationParams& Params);
    static ASpotLight* CreateSpotLight(UWorld* World, const FMCPActorCreationParams& Params);
    static ADirectionalLight* CreateDirectionalLight(UWorld* World, const FMCPActorCreationParams& Params);
    static ATriggerVolume* CreateTriggerVolume(UWorld* World, const FMCPActorCreationParams& Params);
    static ABlockingVolume* CreateBlockingVolume(UWorld* World, const FMCPActorCreationParams& Params);
    static ACharacter* CreateCharacter(UWorld* World, const FMCPActorCreationParams& Params);
    static APawn* CreatePawn(UWorld* World, const FMCPActorCreationParams& Params);

    // Component utilities
    static UStaticMeshComponent* AddStaticMeshComponent(AActor* Actor, const FString& ComponentName, const FString& MeshPath = TEXT(""));
    static USkeletalMeshComponent* AddSkeletalMeshComponent(AActor* Actor, const FString& ComponentName, const FString& MeshPath = TEXT(""));
    static UBoxComponent* AddBoxCollisionComponent(AActor* Actor, const FString& ComponentName, const FVector& BoxExtent = FVector(50.0f));
    static USphereComponent* AddSphereCollisionComponent(AActor* Actor, const FString& ComponentName, float SphereRadius = 50.0f);
    static UCapsuleComponent* AddCapsuleCollisionComponent(AActor* Actor, const FString& ComponentName, float CapsuleRadius = 50.0f, float CapsuleHalfHeight = 100.0f);

    // Asset loading utilities
    static UStaticMesh* LoadStaticMesh(const FString& AssetPath);
    static USkeletalMesh* LoadSkeletalMesh(const FString& AssetPath);
    static UMaterialInterface* LoadMaterial(const FString& AssetPath);
    static UBlueprint* LoadBlueprint(const FString& AssetPath);
    static UBehaviorTree* LoadBehaviorTree(const FString& AssetPath);
    static UBlackboardData* LoadBlackboard(const FString& AssetPath);

    // Validation utilities
    static bool ValidateActorName(const FString& ActorName);
    static bool ValidateAssetPath(const FString& AssetPath);
    static bool IsValidLocation(const FVector& Location);
    static bool IsValidRotation(const FRotator& Rotation);
    static bool IsValidScale(const FVector& Scale);
    
    // Blueprint utilities
    static UBlueprint* FindBlueprint(const FString& BlueprintName);
    static UBlueprint* FindBlueprintByName(const FString& BlueprintName);
    static UEdGraph* FindOrCreateEventGraph(UBlueprint* Blueprint);
    
    // Blueprint node utilities
    static UK2Node_Event* CreateEventNode(UEdGraph* Graph, const FString& EventName, const FVector2D& Position);
    static UK2Node_CallFunction* CreateFunctionCallNode(UEdGraph* Graph, UFunction* Function, const FVector2D& Position);
    static UK2Node_VariableGet* CreateVariableGetNode(UEdGraph* Graph, UBlueprint* Blueprint, const FString& VariableName, const FVector2D& Position);
    static UK2Node_VariableSet* CreateVariableSetNode(UEdGraph* Graph, UBlueprint* Blueprint, const FString& VariableName, const FVector2D& Position);
    static UK2Node_InputAction* CreateInputActionNode(UEdGraph* Graph, const FString& ActionName, const FVector2D& Position);
    static UK2Node_Self* CreateSelfReferenceNode(UEdGraph* Graph, const FVector2D& Position);
    static bool ConnectGraphNodes(UEdGraph* Graph, UEdGraphNode* SourceNode, const FString& SourcePinName, 
                                UEdGraphNode* TargetNode, const FString& TargetPinName);
    static UEdGraphPin* FindPin(UEdGraphNode* Node, const FString& PinName, EEdGraphPinDirection Direction = EGPD_MAX);
    static UK2Node_Event* FindExistingEventNode(UEdGraph* Graph, const FString& EventName);

    // Property utilities
    static bool SetObjectProperty(UObject* Object, const FString& PropertyName, 
                                 const TSharedPtr<FJsonValue>& Value, FString& OutErrorMessage);
};