"""
Collision Tools for Unreal MCP.

This module provides tools for creating and manipulating collision systems in Unreal Engine.
Implements Multi-Layer Collision System tools for MCP server integration.

Based on Technical Architecture Document:
- Custom collision channels for different layers
- Configurable collision sizes and interactions
- Layer-specific collision management
- Performance optimization for collision detection
"""

import logging
from typing import Dict, List, Any, Optional
from mcp.server.fastmcp import FastMCP, Context

# Get logger
logger = logging.getLogger("UnrealMCP")

def register_collision_tools(mcp: FastMCP):
    """Register collision tools with the MCP server."""

    @mcp.tool()
    def create_collision_channel(
        ctx: Context,
        channel_name: str,
        channel_type: str = "ECR_Block",
        description: str = ""
    ) -> Dict[str, Any]:
        """Create a custom collision channel for multi-layer system."""
        # Import inside function to avoid circular imports
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
                
            params = {
                "channel_name": channel_name,
                "channel_type": channel_type,
                "description": description
            }
            
            logger.info(f"Creating collision channel with params: {params}")
            response = unreal.send_command("create_collision_channel", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Collision channel creation response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error creating collision channel: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}

    @mcp.tool()
    def configure_collision_profile(
        ctx: Context,
        profile_name: str,
        collision_settings: Dict[str, str]
    ) -> Dict[str, Any]:
        """Configure a collision profile with specific channel responses."""
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
                
            params = {
                "profile_name": profile_name,
                "collision_settings": collision_settings
            }
            
            logger.info(f"Configuring collision profile with params: {params}")
            response = unreal.send_command("configure_collision_profile", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Collision profile configuration response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error configuring collision profile: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}

    @mcp.tool()
    def set_layer_collision_rules(
        ctx: Context,
        layer_name: str,
        collision_rules: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Set collision rules for a specific layer."""
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
                
            params = {
                "layer_name": layer_name,
                "collision_rules": collision_rules
            }
            
            logger.info(f"Setting layer collision rules with params: {params}")
            response = unreal.send_command("set_layer_collision_rules", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Layer collision rules response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error setting layer collision rules: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}

    @mcp.tool()
    def configure_collision_size_scaling(
        ctx: Context,
        layer_name: str,
        scale_factor: float,
        size_rules: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Configure collision size scaling for a layer."""
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
                
            params = {
                "layer_name": layer_name,
                "scale_factor": scale_factor,
                "size_rules": size_rules
            }
            
            logger.info(f"Configuring collision size scaling with params: {params}")
            response = unreal.send_command("configure_collision_size_scaling", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Collision size scaling response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error configuring collision size scaling: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}

    @mcp.tool()
    def create_layer_collision_matrix(
        ctx: Context,
        layers: List[str],
        interaction_matrix: Dict[str, Dict[str, str]]
    ) -> Dict[str, Any]:
        """Create a collision interaction matrix between layers."""
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
                
            params = {
                "layers": layers,
                "interaction_matrix": interaction_matrix
            }
            
            logger.info(f"Creating layer collision matrix with params: {params}")
            response = unreal.send_command("create_layer_collision_matrix", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Layer collision matrix response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error creating layer collision matrix: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}

    @mcp.tool()
    def optimize_collision_detection(
        ctx: Context,
        layer_name: str,
        optimization_settings: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Optimize collision detection for a specific layer."""
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
                
            params = {
                "layer_name": layer_name,
                "optimization_settings": optimization_settings
            }
            
            logger.info(f"Optimizing collision detection with params: {params}")
            response = unreal.send_command("optimize_collision_detection", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Collision optimization response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error optimizing collision detection: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def get_collision_system_status(ctx: Context) -> Dict[str, Any]:
        """Get current status of the collision system."""
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
                
            logger.info("Getting collision system status")
            response = unreal.send_command("get_collision_system_status", {})
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Collision system status response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error getting collision system status: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def configure_collision_complexity(
        ctx: Context,
        actor_name: str,
        complexity_type: str,
        custom_settings: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Configure collision complexity for an actor."""
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
                
            params = {
                "actor_name": actor_name,
                "complexity_type": complexity_type,
                "custom_settings": custom_settings or {}
            }
            
            logger.info(f"Configuring collision complexity with params: {params}")
            response = unreal.send_command("configure_collision_complexity", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Collision complexity configuration response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error configuring collision complexity: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def setup_layer_collision_filtering(
        ctx: Context,
        layer_name: str,
        filter_settings: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Setup collision filtering for a specific layer."""
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
                
            params = {
                "layer_name": layer_name,
                "filter_settings": filter_settings
            }
            
            logger.info(f"Setting up layer collision filtering with params: {params}")
            response = unreal.send_command("setup_layer_collision_filtering", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Layer collision filtering response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error setting up layer collision filtering: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}

    # === Ferramentas Específicas da Arquitetura Auracron ===
    
    @mcp.tool()
    def setup_auracron_collision_channels(
        ctx: Context
    ) -> Dict[str, Any]:
        """Configura os 5 collision channels específicos da arquitetura Auracron (LayerRadiante, LayerZephyr, LayerUmbral, VerticalConnector, CrossLayer)."""
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            params = {}
            
            logger.info(f"Setting up Auracron collision channels with params: {params}")
            response = unreal.send_command("setup_auracron_collision_channels", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Setup Auracron collision channels response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error setting up Auracron collision channels: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def configure_dota2_collision_sizes(
        ctx: Context,
        unit_type: str,
        custom_size: Optional[float] = None
    ) -> Dict[str, Any]:
        """Configura tamanhos de colisão baseados em Dota 2 (Heróis: 24u, Creeps: 16u, Torres: 144u, Árvores: 64u, Estruturas: 128-256u)."""
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            params = {
                "unit_type": str(unit_type)
            }
            
            if custom_size is not None:
                params["custom_size"] = float(custom_size)
            
            logger.info(f"Configuring Dota2 collision sizes with params: {params}")
            response = unreal.send_command("configure_dota2_collision_sizes", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Configure Dota2 collision sizes response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error configuring Dota2 collision sizes: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def setup_layer_collision_interactions(
        ctx: Context,
        enable_cross_layer_collision: bool = True,
        vertical_connector_collision: bool = True
    ) -> Dict[str, Any]:
        """Configura interações de colisão entre camadas da arquitetura Auracron."""
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            params = {
                "enable_cross_layer_collision": bool(enable_cross_layer_collision),
                "vertical_connector_collision": bool(vertical_connector_collision)
            }
            
            logger.info(f"Setting up layer collision interactions with params: {params}")
            response = unreal.send_command("setup_layer_collision_interactions", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Setup layer collision interactions response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error setting up layer collision interactions: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def configure_auracron_collision_profiles(
        ctx: Context,
        layer_name: str
    ) -> Dict[str, Any]:
        """Configura perfis de colisão específicos para cada camada da arquitetura Auracron (Planície, Firmamento, Abismo)."""
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            params = {
                "layer_name": str(layer_name)
            }
            
            logger.info(f"Configuring Auracron collision profiles with params: {params}")
            response = unreal.send_command("configure_auracron_collision_profiles", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Configure Auracron collision profiles response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error configuring Auracron collision profiles: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}

    logger.info("Collision tools registered successfully")