// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "MultilayerManager.h"

#ifdef AURACRON_MultilayerManager_generated_h
#error "MultilayerManager.generated.h already included, missing '#pragma once' in MultilayerManager.h"
#endif
#define AURACRON_MultilayerManager_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

// ********** Begin Class AMultilayerManager *******************************************************
#define FID_Game_Auracron_Source_Auracron_Public_MultilayerManager_h_12_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execGetLayerPosition); \
	DECLARE_FUNCTION(execSetLayerVisibility); \
	DECLARE_FUNCTION(execInitializeLayers);


AURACRON_API UClass* Z_Construct_UClass_AMultilayerManager_NoRegister();

#define FID_Game_Auracron_Source_Auracron_Public_MultilayerManager_h_12_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesAMultilayerManager(); \
	friend struct Z_Construct_UClass_AMultilayerManager_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRON_API UClass* Z_Construct_UClass_AMultilayerManager_NoRegister(); \
public: \
	DECLARE_CLASS2(AMultilayerManager, AActor, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/Auracron"), Z_Construct_UClass_AMultilayerManager_NoRegister) \
	DECLARE_SERIALIZER(AMultilayerManager)


#define FID_Game_Auracron_Source_Auracron_Public_MultilayerManager_h_12_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	AMultilayerManager(AMultilayerManager&&) = delete; \
	AMultilayerManager(const AMultilayerManager&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, AMultilayerManager); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(AMultilayerManager); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(AMultilayerManager) \
	NO_API virtual ~AMultilayerManager();


#define FID_Game_Auracron_Source_Auracron_Public_MultilayerManager_h_9_PROLOG
#define FID_Game_Auracron_Source_Auracron_Public_MultilayerManager_h_12_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Game_Auracron_Source_Auracron_Public_MultilayerManager_h_12_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Game_Auracron_Source_Auracron_Public_MultilayerManager_h_12_INCLASS_NO_PURE_DECLS \
	FID_Game_Auracron_Source_Auracron_Public_MultilayerManager_h_12_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class AMultilayerManager;

// ********** End Class AMultilayerManager *********************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Game_Auracron_Source_Auracron_Public_MultilayerManager_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
