#pragma once

#include "CoreMinimal.h"
#include "Engine/Engine.h"
#include "Engine/World.h"
#include "Components/ActorComponent.h"
#include "Components/StaticMeshComponent.h"
#include "Components/SceneComponent.h"
#include "GameFramework/Actor.h"
#include "Kismet/GameplayStatics.h"
#include "Engine/StaticMesh.h"
#include "Materials/Material.h"
#include "Materials/MaterialInstance.h"
#include "Components/BoxComponent.h"
#include "Components/SphereComponent.h"
#include "Engine/CollisionProfile.h"
#include "PhysicsEngine/BodySetup.h"
#include "Dom/JsonObject.h"
#include "Serialization/JsonSerializer.h"
#include "Serialization/JsonWriter.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/FileHelper.h"
#include "Misc/Paths.h"
#include "Engine/TextureRenderTarget2D.h"
#include "Engine/Canvas.h"
#include "DrawDebugHelpers.h"

/**
 * Classe responsável por gerenciar comandos relacionados ao Sistema de Visão Tridimensional
 * Inclui Fog of War multicamada, alcances de visão por camada e Line of Sight
 */
class UNREALMCP_API FUnrealMCPVisionCommands
{
public:
    FUnrealMCPVisionCommands();
    ~FUnrealMCPVisionCommands();

    // Método principal para processar comandos
    TSharedPtr<FJsonObject> HandleCommand(const FString& CommandType, const TSharedPtr<FJsonObject>& Params);
    
    // === Comandos Principais ===
    
    /**
     * Cria uma nova camada de Fog of War para visão tridimensional
     * @param CommandData Dados do comando contendo configurações da camada
     * @return Resposta JSON com resultado da operação
     */
    TSharedPtr<FJsonObject> HandleCreateFogOfWarLayer(const TSharedPtr<FJsonObject>& CommandData);
    
    /**
     * Configura alcances de visão específicos para uma camada
     * @param CommandData Dados do comando contendo configurações de visão
     * @return Resposta JSON com resultado da operação
     */
    TSharedPtr<FJsonObject> HandleConfigureVisionRangeLayer(const TSharedPtr<FJsonObject>& CommandData);
    
    /**
     * Configura sistema de Line of Sight para uma camada específica
     * @param CommandData Dados do comando contendo configurações de LOS
     * @return Resposta JSON com resultado da operação
     */
    TSharedPtr<FJsonObject> HandleSetupLineOfSightSystem(const TSharedPtr<FJsonObject>& CommandData);
    
    /**
     * Cria volumes que bloqueiam visão em uma camada específica
     * @param CommandData Dados do comando contendo informações dos volumes
     * @return Resposta JSON com resultado da operação
     */
    TSharedPtr<FJsonObject> HandleCreateVisionBlockingVolumes(const TSharedPtr<FJsonObject>& CommandData);
    
    /**
     * Configura atualizações dinâmicas do Fog of War
     * @param CommandData Dados do comando contendo configurações de atualização
     * @return Resposta JSON com resultado da operação
     */
    TSharedPtr<FJsonObject> HandleConfigureDynamicFogUpdates(const TSharedPtr<FJsonObject>& CommandData);
    
    /**
     * Configura interações de visão entre múltiplas camadas
     * @param CommandData Dados do comando contendo regras de interação
     * @return Resposta JSON com resultado da operação
     */
    TSharedPtr<FJsonObject> HandleSetupMultilayerVisionInteractions(const TSharedPtr<FJsonObject>& CommandData);
    
    /**
     * Cria sensores de visão para detecção automática
     * @param CommandData Dados do comando contendo configurações dos sensores
     * @return Resposta JSON com resultado da operação
     */
    TSharedPtr<FJsonObject> HandleCreateVisionSensors(const TSharedPtr<FJsonObject>& CommandData);
    
    /**
     * Configura sistema de oclusão para otimização de visão
     * @param CommandData Dados do comando contendo configurações de oclusão
     * @return Resposta JSON com resultado da operação
     */
    TSharedPtr<FJsonObject> HandleConfigureVisionOcclusionSystem(const TSharedPtr<FJsonObject>& CommandData);
    
    /**
     * Configura persistência do Fog of War entre sessões
     * @param CommandData Dados do comando contendo configurações de persistência
     * @return Resposta JSON com resultado da operação
     */
    TSharedPtr<FJsonObject> HandleSetupFogOfWarPersistence(const TSharedPtr<FJsonObject>& CommandData);
    
    /**
     * Calcula cobertura de visão para posições específicas
     * @param CommandData Dados do comando contendo posições e configurações
     * @return Resposta JSON com resultado do cálculo
     */
    TSharedPtr<FJsonObject> HandleCalculateVisionCoverage(const TSharedPtr<FJsonObject>& CommandData);
    
    /**
     * Otimiza performance do sistema de visão
     * @param CommandData Dados do comando contendo configurações de otimização
     * @return Resposta JSON com resultado da otimização
     */
    TSharedPtr<FJsonObject> HandleOptimizeVisionPerformance(const TSharedPtr<FJsonObject>& CommandData);
    
    /**
     * Ativa visualização de debug para o sistema de visão
     * @param CommandData Dados do comando contendo opções de debug
     * @return Resposta JSON com resultado da ativação
     */
    TSharedPtr<FJsonObject> HandleDebugVisionSystem(const TSharedPtr<FJsonObject>& CommandData);
    
    /**
     * Valida a configuração do sistema de visão multicamada
     * @param CommandData Dados do comando contendo camadas para validar
     * @return Resposta JSON com resultado da validação
     */
    TSharedPtr<FJsonObject> HandleValidateVisionSetup(const TSharedPtr<FJsonObject>& CommandData);
    
    /**
     * Obtém o status do sistema de visão tridimensional
     * @param CommandData Dados do comando contendo opções de status
     * @return Resposta JSON com status do sistema
     */
    TSharedPtr<FJsonObject> HandleGetVisionSystemStatus(const TSharedPtr<FJsonObject>& CommandData);

    /**
     * Cria o sistema completo de wards (Stealth, Control, Farsight).
     *
     * @param CommandData Dados do comando contendo configurações dos wards
     * @return Resposta JSON com informações do sistema criado
     */
    TSharedPtr<FJsonObject> HandleCreateWardSystem(const TSharedPtr<FJsonObject>& CommandData);

    /**
     * Cria o sistema de visão multicamada para o mapa de três camadas.
     *
     * @param CommandData Dados do comando contendo configurações da visão multicamada
     * @return Resposta JSON com informações do sistema criado
     */
    TSharedPtr<FJsonObject> HandleCreateMultilayerVisionSystem(const TSharedPtr<FJsonObject>& CommandData);

    // === Funções Auxiliares ===
    
    /**
     * Converte string para tipo de fog
     * @param FogTypeStr String do tipo de fog
     * @return Tipo de fog correspondente
     */
    static FString ConvertStringToFogType(const FString& FogTypeStr);
    
    /**
     * Converte string para tipo de sensor de visão
     * @param SensorTypeStr String do tipo de sensor
     * @return Tipo de sensor correspondente
     */
    static FString ConvertStringToVisionSensorType(const FString& SensorTypeStr);
    
    /**
     * Converte string para algoritmo de oclusão
     * @param OcclusionAlgorithmStr String do algoritmo
     * @return Algoritmo de oclusão correspondente
     */
    static FString ConvertStringToOcclusionAlgorithm(const FString& OcclusionAlgorithmStr);
    
    /**
     * Converte string para tipo de persistência
     * @param PersistenceTypeStr String do tipo de persistência
     * @return Tipo de persistência correspondente
     */
    static FString ConvertStringToPersistenceType(const FString& PersistenceTypeStr);
    
    /**
     * Valida configurações de camada de fog
     * @param LayerConfig Configurações da camada
     * @return True se válidas, false caso contrário
     */
    static bool ValidateFogLayerConfig(const TSharedPtr<FJsonObject>& LayerConfig);
    
    /**
     * Valida configurações de sensor de visão
     * @param SensorConfig Configurações do sensor
     * @return True se válidas, false caso contrário
     */
    static bool ValidateVisionSensorConfig(const TSharedPtr<FJsonObject>& SensorConfig);
    
    /**
     * Valida configurações de Line of Sight
     * @param LOSConfig Configurações de LOS
     * @return True se válidas, false caso contrário
     */
    static bool ValidateLineOfSightConfig(const TSharedPtr<FJsonObject>& LOSConfig);
    
    /**
     * Cria resposta de sucesso
     * @param Message Mensagem de sucesso
     * @param Data Dados adicionais (opcional)
     * @return Objeto JSON de resposta
     */
    static TSharedPtr<FJsonObject> CreateSuccessResponse(const FString& Message, const TSharedPtr<FJsonObject>& Data = nullptr);
    
    /**
     * Cria resposta de erro
     * @param ErrorMessage Mensagem de erro
     * @param ErrorCode Código de erro (opcional)
     * @return Objeto JSON de resposta
     */
    static TSharedPtr<FJsonObject> CreateErrorResponse(const FString& ErrorMessage, const FString& ErrorCode = TEXT("VISION_ERROR"));
    
    /**
     * Obtém mundo atual
     * @return Ponteiro para o mundo atual
     */
    static UWorld* GetCurrentWorld();
    
    /**
     * Calcula distância 3D entre dois pontos
     * @param Point1 Primeiro ponto
     * @param Point2 Segundo ponto
     * @return Distância entre os pontos
     */
    static float CalculateDistance3D(const FVector& Point1, const FVector& Point2);
    
    /**
     * Verifica se um ponto está dentro de um volume
     * @param Point Ponto a verificar
     * @param VolumeCenter Centro do volume
     * @param VolumeExtent Extensão do volume
     * @return True se o ponto está dentro do volume
     */
    static bool IsPointInVolume(const FVector& Point, const FVector& VolumeCenter, const FVector& VolumeExtent);
    
    /**
     * Realiza trace de linha para verificar visibilidade
     * @param Start Ponto inicial
     * @param End Ponto final
     * @param TraceChannel Canal de trace
     * @return True se há linha de visão clara
     */
    static bool PerformLineOfSightTrace(const FVector& Start, const FVector& End, ECollisionChannel TraceChannel);

private:
    // === Constantes ===
    
    // Tipos de resposta
    static const FString RESPONSE_SUCCESS;
    static const FString RESPONSE_ERROR;
    static const FString RESPONSE_WARNING;
    
    // Tipos de fog
    static const FString FOG_TYPE_STANDARD;
    static const FString FOG_TYPE_VOLUMETRIC;
    static const FString FOG_TYPE_HEIGHT_BASED;
    static const FString FOG_TYPE_DISTANCE_BASED;
    
    // Tipos de sensor de visão
    static const FString SENSOR_TYPE_OMNIDIRECTIONAL;
    static const FString SENSOR_TYPE_DIRECTIONAL;
    static const FString SENSOR_TYPE_CONE;
    static const FString SENSOR_TYPE_SECTOR;
    
    // Algoritmos de oclusão
    static const FString OCCLUSION_RAYCAST;
    static const FString OCCLUSION_RASTERIZATION;
    static const FString OCCLUSION_HIERARCHICAL;
    static const FString OCCLUSION_HYBRID;
    
    // Tipos de persistência
    static const FString PERSISTENCE_MEMORY;
    static const FString PERSISTENCE_FILE;
    static const FString PERSISTENCE_DATABASE;
    static const FString PERSISTENCE_CLOUD;

    // Tipos de wards
    static const FString WARD_TYPE_STEALTH;
    static const FString WARD_TYPE_CONTROL;
    static const FString WARD_TYPE_FARSIGHT;

    // Nomes das camadas
    static const FString LAYER_PLANICIE_RADIANTE;
    static const FString LAYER_FIRMAMENTO_ZEPHYR;
    static const FString LAYER_ABISMO_UMBRAL;
    
    // === Variáveis Privadas ===
    
    // Cache de camadas de fog criadas
    TMap<FString, TWeakObjectPtr<AActor>> FogLayers;
    
    // Cache de sensores de visão
    TMap<FString, TArray<TWeakObjectPtr<AActor>>> VisionSensors;
    
    // Cache de volumes de bloqueio
    TMap<FString, TArray<TWeakObjectPtr<AActor>>> BlockingVolumes;
    
    // Configurações de performance
    TSharedPtr<FJsonObject> PerformanceSettings;
    
    // Estado do sistema de debug
    bool bDebugEnabled;
    
    // === Métodos Específicos da Arquitetura Auracron ===
    
    /**
     * Configura camadas de visão específicas da arquitetura Auracron
     * @param CommandData Dados do comando contendo configurações das camadas
     * @return Resposta JSON com resultado da configuração
     */
    TSharedPtr<FJsonObject> HandleConfigureAuracronVisionLayers(const TSharedPtr<FJsonObject>& CommandData);
    
    /**
     * Configura sistema de visão multicamada para Auracron
     * @param CommandData Dados do comando contendo configurações do sistema
     * @return Resposta JSON com resultado da configuração
     */
    TSharedPtr<FJsonObject> HandleSetupMultilayerVisionSystem(const TSharedPtr<FJsonObject>& CommandData);
    
    /**
     * Configura alcances de visão específicos por camada
     * @param CommandData Dados do comando contendo nome da camada e alcance
     * @return Resposta JSON com resultado da configuração
     */
    TSharedPtr<FJsonObject> HandleConfigureLayerVisionRanges(const TSharedPtr<FJsonObject>& CommandData);
    
    /**
     * Configura visão para conectores verticais entre camadas
     * @param CommandData Dados do comando contendo configurações dos conectores
     * @return Resposta JSON com resultado da configuração
     */
    TSharedPtr<FJsonObject> HandleSetupVerticalConnectorVision(const TSharedPtr<FJsonObject>& CommandData);
    
    // === Métodos Auxiliares Privados ===
    
    /**
     * Inicializa configurações padrão
     */
    void InitializeDefaultSettings();
    
    /**
     * Limpa cache de objetos inválidos
     */
    void CleanupInvalidObjects();
    
    /**
     * Atualiza métricas de performance
     */
    void UpdatePerformanceMetrics();
    
    /**
     * Salva estado do sistema
     */
    void SaveSystemState();
    
    /**
     * Carrega estado do sistema
     */
    void LoadSystemState();
    
    /**
     * Calcula cobertura real de visão usando line tracing
     * @param ObserverPosition Posição do observador
     * @param MaxRange Alcance máximo de visão
     * @param Precision Precisão do cálculo (0.1 a 10.0)
     * @return Percentual de cobertura (0.0 a 1.0)
     */
    double CalculateRealVisionCoverage(const FVector& ObserverPosition, double MaxRange, double Precision);
    
    /**
     * Calcula uso real de memória do sistema de visão
     * @return Uso de memória em MB
     */
    double CalculateRealMemoryUsage();
    
    /**
     * Calcula número de traces ativos no sistema
     * @return Número de traces ativos
     */
    int32 CalculateActiveTraces();
    
    /**
     * Calcula taxa real de acerto do cache
     * @return Taxa de acerto (0.0 a 1.0)
     */
    double CalculateRealCacheHitRatio();
};