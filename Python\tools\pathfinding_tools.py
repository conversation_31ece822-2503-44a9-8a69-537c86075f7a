"""Pathfinding Tools for Unreal MCP.

This module provides tools for creating and managing pathfinding systems in Unreal Engine.
"""

import logging
from typing import Dict, List, Any, Tuple, Optional
from mcp.server.fastmcp import FastMCP, Context

# Get logger
logger = logging.getLogger("UnrealMCP")

def register_pathfinding_tools(mcp: FastMCP):
    """Register Pathfinding tools with the MCP server."""
    
    @mcp.tool()
    def create_navigation_mesh_layer(
        ctx: Context,
        layer_name: str,
        layer_height: float = 200.0,
        navmesh_settings: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Create a navigation mesh layer for 3D pathfinding.
        
        Args:
            ctx: MCP Context
            layer_name: Unique name for the layer
            layer_height: Height of the layer in the world
            navmesh_settings: Navmesh configuration as JSON object containing:
                - cell_size: Navmesh cell size (default: 19.0)
                - cell_height: Navmesh cell height (default: 10.0)
                - agent_height: Agent height (default: 180.0)
                - agent_radius: Agent radius (default: 34.0)
                - agent_max_climb: Maximum height agent can climb (default: 35.0)
                - agent_max_slope: Maximum slope agent can navigate (default: 45.0)
        
        Returns:
            Dict[str, Any]: Operation result
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            # Default navmesh settings
            default_settings = {
                "cell_size": 19.0,
                "cell_height": 10.0,
                "agent_height": 180.0,
                "agent_radius": 34.0,
                "agent_max_climb": 35.0,
                "agent_max_slope": 45.0
            }
            
            # Merge provided settings with defaults
            if navmesh_settings:
                default_settings.update(navmesh_settings)
            
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            params = {
                "layer_name": layer_name,
                "layer_height": layer_height,
                "navmesh_settings": default_settings
            }
            
            logger.info(f"Creating navigation mesh layer with params: {params}")
            response = unreal.send_command("create_navigation_mesh_layer", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Navigation mesh layer creation response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error creating navigation mesh layer: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def configure_astar_algorithm(
        ctx: Context,
        layer_name: str,
        algorithm_settings: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Configure A* algorithm for a specific layer.
        
        Args:
            ctx: MCP Context
            layer_name: Name of the layer
            algorithm_settings: Algorithm configuration as JSON object containing:
                - heuristic_type: Heuristic type (manhattan, euclidean, diagonal, octile) (default: "euclidean")
                - weight_factor: Heuristic weight factor (default: 1.0)
                - diagonal_cost: Cost for diagonal movement (default: 1.414)
                - tie_breaker: Tie breaker factor (default: 0.001)
                - max_search_nodes: Maximum number of nodes to search (default: 10000)
                - enable_smoothing: Enable path smoothing (default: True)
        
        Returns:
            Dict[str, Any]: Operation result
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            # Default algorithm settings
            default_settings = {
                "heuristic_type": "euclidean",
                "weight_factor": 1.0,
                "diagonal_cost": 1.414,
                "tie_breaker": 0.001,
                "max_search_nodes": 10000,
                "enable_smoothing": True
            }
            
            # Merge provided settings with defaults
            if algorithm_settings:
                default_settings.update(algorithm_settings)
            
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            params = {
                "layer_name": layer_name,
                "algorithm_settings": default_settings
            }
            
            logger.info(f"Configuring A* algorithm with params: {params}")
            response = unreal.send_command("configure_astar_algorithm", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"A* algorithm configuration response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error configuring A* algorithm: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def set_movement_costs(
        ctx: Context,
        layer_name: str,
        cost_settings: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Set movement costs for different terrain types and layer transitions.
        
        Args:
            ctx: MCP Context
            layer_name: Name of the layer
            cost_settings: Cost configuration as JSON object containing:
                - terrain_costs: Dictionary with costs per terrain type
                - layer_transition_costs: Costs for transitions between layers (optional)
                - default_cost: Default cost for unspecified terrains (optional)
                - penalty_multiplier: Penalty multiplier (optional)
        
        Returns:
            Dict[str, Any]: Operation result
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            # Validate that cost_settings contains at least terrain_costs
            if not cost_settings or "terrain_costs" not in cost_settings:
                error_msg = "cost_settings must contain at least 'terrain_costs'"
                logger.error(error_msg)
                return {"success": False, "message": error_msg}
            
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            params = {
                "layer_name": layer_name,
                "cost_settings": cost_settings
            }
            
            logger.info(f"Setting movement costs with params: {params}")
            response = unreal.send_command("set_movement_costs", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Movement costs response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error setting movement costs: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def create_layer_connections(
        ctx: Context,
        source_layer: str,
        target_layer: str,
        connection_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Create connections between navigation layers for 3D pathfinding.
        
        Args:
            ctx: MCP Context
            source_layer: Name of the source layer
            target_layer: Name of the target layer
            connection_data: Connection configuration as JSON object containing:
                - connection_type: Type of connection (ladder, teleport, jump, stairs, elevator)
                - source_position: Object with coordinates {x, y, z} in source layer
                - target_position: Object with coordinates {x, y, z} in target layer
                - bidirectional: Whether connection is bidirectional (optional, default: true)
                - cost: Connection cost (optional)
                - max_agents: Maximum simultaneous agents (optional)
        
        Returns:
            Dict[str, Any]: Operation result
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            # Validate required fields in connection_data
            required_fields = ["connection_type", "source_position", "target_position"]
            for field in required_fields:
                if field not in connection_data:
                    error_msg = f"connection_data must contain '{field}' field"
                    logger.error(error_msg)
                    return {"success": False, "message": error_msg}
            
            # Validate position structure
            for pos_field in ["source_position", "target_position"]:
                pos = connection_data[pos_field]
                if not isinstance(pos, dict) or not all(coord in pos for coord in ["x", "y", "z"]):
                    error_msg = f"{pos_field} must be an object with x, y, z coordinates"
                    logger.error(error_msg)
                    return {"success": False, "message": error_msg}
            
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            params = {
                "source_layer": source_layer,
                "target_layer": target_layer,
                "connection_data": connection_data
            }
            
            logger.info(f"Creating layer connections with params: {params}")
            response = unreal.send_command("create_layer_connections", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Layer connections response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error creating layer connections: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def configure_pathfinding_constraints(
        ctx: Context,
        constraints: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Configure pathfinding constraints and optimizations.
        
        Args:
            ctx: MCP Context
            constraints: Constraint configuration as JSON object containing:
                - layer_name: Name of the layer (required)
                - max_path_length: Maximum path length (optional, default: 10000.0)
                - max_search_time: Maximum search time in seconds (optional, default: 0.1)
                - allow_partial_paths: Allow partial paths (optional, default: true)
                - smooth_paths: Enable path smoothing (optional, default: true)
                - path_optimization: Path optimization method (optional, default: "string_pulling")
                - max_search_nodes: Maximum search nodes (optional, default: 10000)
                - search_timeout_ms: Search timeout in milliseconds (optional, default: 5000.0)
                - arrival_tolerance: Arrival tolerance (optional, default: 50.0)
        
        Returns:
            Dict[str, Any]: Operation result
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            # Validate required fields
            if not constraints or "layer_name" not in constraints:
                error_msg = "constraints must contain 'layer_name' field"
                logger.error(error_msg)
                return {"success": False, "message": error_msg}
            
            # Apply default values
            default_constraints = {
                "max_path_length": 10000.0,
                "max_search_time": 0.1,
                "allow_partial_paths": True,
                "smooth_paths": True,
                "path_optimization": "string_pulling",
                "max_search_nodes": 10000,
                "search_timeout_ms": 5000.0,
                "arrival_tolerance": 50.0
            }
            
            # Merge with provided constraints
            final_constraints = {**default_constraints, **constraints}
            
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            params = {
                "constraints": final_constraints
            }
            
            logger.info(f"Configuring pathfinding constraints with params: {params}")
            response = unreal.send_command("configure_pathfinding_constraints", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Pathfinding constraints response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error configuring pathfinding constraints: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def create_dynamic_obstacles(
        ctx: Context,
        obstacles: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        Cria obstáculos dinâmicos no sistema de navegação.
        
        Args:
            ctx: Contexto MCP
            obstacles: Lista de obstáculos, cada um contendo:
                - position: Array [x, y, z] com a posição do obstáculo (obrigatório)
                - size: Array [x, y, z] com o tamanho do obstáculo (obrigatório)
                - type: Tipo do obstáculo ("blocking", "obstacle", etc.) (opcional, padrão: "obstacle")
                - cost: Custo de movimento através do obstáculo (opcional, padrão: 10.0)
        
        Returns:
            Dict[str, Any]: Resultado da operação
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            # Validar entrada
            if not obstacles or not isinstance(obstacles, list):
                error_msg = "obstacles deve ser uma lista não vazia"
                logger.error(error_msg)
                return {"success": False, "message": error_msg}
            
            # Validar cada obstáculo
            for i, obstacle in enumerate(obstacles):
                if not isinstance(obstacle, dict):
                    error_msg = f"Obstáculo {i} deve ser um dicionário"
                    logger.error(error_msg)
                    return {"success": False, "message": error_msg}
                
                if "position" not in obstacle or not isinstance(obstacle["position"], list) or len(obstacle["position"]) != 3:
                    error_msg = f"Obstáculo {i} deve ter 'position' como array [x, y, z]"
                    logger.error(error_msg)
                    return {"success": False, "message": error_msg}
                
                if "size" not in obstacle or not isinstance(obstacle["size"], list) or len(obstacle["size"]) != 3:
                    error_msg = f"Obstáculo {i} deve ter 'size' como array [x, y, z]"
                    logger.error(error_msg)
                    return {"success": False, "message": error_msg}
                
                # Aplicar valores padrão se não fornecidos
                if "type" not in obstacle:
                    obstacle["type"] = "obstacle"
                if "cost" not in obstacle:
                    obstacle["cost"] = 10.0
            
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Conexão com Unreal não disponível"}
            
            params = {
                "obstacles": obstacles
            }
            
            response = unreal.send_command("create_dynamic_obstacles", params)
            
            if response and response.get("success"):
                logger.info(f"Obstáculos dinâmicos criados: {len(obstacles)}")
                return response
            else:
                error_msg = response.get("message", "Erro desconhecido") if response else "Sem resposta do Unreal"
                logger.error(f"Falha ao criar obstáculos: {error_msg}")
                return {"success": False, "message": error_msg}
                
        except Exception as e:
            error_msg = f"Erro ao criar obstáculos dinâmicos: {str(e)}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def find_path_multilayer(
        ctx: Context,
        start_position: List[float],
        end_position: List[float],
        agent_properties: Dict[str, Any] = None,
        allowed_layers: List[str] = None
    ) -> Dict[str, Any]:
        """Find a path across multiple navigation layers.
        
        Args:
            ctx: MCP Context
            start_position: Starting position [x, y, z]
            end_position: Target position [x, y, z]
            agent_properties: Agent properties as JSON object with 'radius' and 'height' (optional)
            allowed_layers: List of allowed layer names for pathfinding (optional)
        
        Returns:
            Dict[str, Any]: Response with path information or error
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            # Validate required parameters
            if not isinstance(start_position, list) or len(start_position) != 3:
                error_msg = "start_position must be a list of 3 numbers [x, y, z]"
                logger.error(error_msg)
                return {"success": False, "message": error_msg}
            
            if not isinstance(end_position, list) or len(end_position) != 3:
                error_msg = "end_position must be a list of 3 numbers [x, y, z]"
                logger.error(error_msg)
                return {"success": False, "message": error_msg}
            
            # Validate agent_properties if provided
            if agent_properties:
                if not isinstance(agent_properties, dict):
                    error_msg = "agent_properties must be a JSON object"
                    logger.error(error_msg)
                    return {"success": False, "message": error_msg}
                if "radius" not in agent_properties or "height" not in agent_properties:
                    error_msg = "agent_properties must contain 'radius' and 'height' fields"
                    logger.error(error_msg)
                    return {"success": False, "message": error_msg}
            
            # Validate allowed_layers if provided
            if allowed_layers and not isinstance(allowed_layers, list):
                error_msg = "allowed_layers must be a list of strings"
                logger.error(error_msg)
                return {"success": False, "message": error_msg}
            
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            params = {
                "start_position": start_position,
                "end_position": end_position
            }
            
            if agent_properties:
                params["agent_properties"] = agent_properties
            
            if allowed_layers:
                params["allowed_layers"] = allowed_layers
            
            logger.info(f"Finding multilayer path with params: {params}")
            response = unreal.send_command("find_path_multilayer", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Multilayer path response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error finding multilayer path: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def optimize_navigation_performance(
        ctx: Context,
        layer_name: str,
        optimization_settings: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Optimize navigation system performance.
        
        Args:
            ctx: MCP Context
            layer_name: Name of the navigation layer to optimize
            optimization_settings: Performance optimization settings as dict with optional fields:
                                 - optimize_tile_generation (bool): Enable tile generation optimization
                                 - optimize_pathfinding_cache (bool): Enable pathfinding cache optimization
                                 - optimize_memory_usage (bool): Enable memory usage optimization
                                 - max_cache_size (int): Maximum cache size
                                 - tile_generation_radius (float): Tile generation radius
        
        Returns:
            Dict[str, Any]: Response with optimization results or error
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            # Set default optimization settings if none provided
            if optimization_settings is None:
                optimization_settings = {
                    "optimize_tile_generation": True,
                    "optimize_pathfinding_cache": True,
                    "optimize_memory_usage": True,
                    "max_cache_size": 1000,
                    "tile_generation_radius": 5000.0
                }
            
            # Validate optimization_settings if provided
            if not isinstance(optimization_settings, dict):
                error_msg = "optimization_settings must be a dictionary"
                logger.error(error_msg)
                return {"success": False, "message": error_msg}
            
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            params = {
                "layer_name": layer_name,
                "optimization_settings": optimization_settings
            }
            
            logger.info(f"Optimizing navigation performance with params: {params}")
            response = unreal.send_command("optimize_navigation_performance", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Navigation optimization response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error optimizing navigation performance: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def get_pathfinding_system_status(
        ctx: Context,
        include_performance_metrics: bool = True
    ) -> Dict[str, Any]:
        """Get the status of the multilayer pathfinding system."""
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            params = {
                "include_performance_metrics": include_performance_metrics
            }
            
            logger.info(f"Getting pathfinding system status with params: {params}")
            response = unreal.send_command("get_pathfinding_system_status", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Pathfinding system status response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error getting pathfinding system status: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def configure_hierarchical_pathfinding(
        ctx: Context,
        layer_name: str,
        cluster_size: int = 10,
        max_hierarchy_levels: int = 3,
        precompute_clusters: bool = True
    ) -> Dict[str, Any]:
        """Configure hierarchical pathfinding for better performance on large maps."""
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            params = {
                "layer_name": layer_name,
                "cluster_size": cluster_size,
                "max_hierarchy_levels": max_hierarchy_levels,
                "precompute_clusters": precompute_clusters
            }
            
            logger.info(f"Configuring hierarchical pathfinding with params: {params}")
            response = unreal.send_command("configure_hierarchical_pathfinding", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Hierarchical pathfinding response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error configuring hierarchical pathfinding: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def setup_crowd_navigation(
        ctx: Context,
        layer_name: str,
        crowd_settings: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Configure crowd navigation with collision avoidance."""
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            params = {
                "layer_name": layer_name,
                "crowd_settings": crowd_settings
            }
            
            logger.info(f"Setting up crowd navigation with params: {params}")
            response = unreal.send_command("setup_crowd_navigation", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Crowd navigation response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error setting up crowd navigation: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def debug_navigation_layer(
        ctx: Context,
        layer_name: str,
        show_navmesh: bool = True,
        show_connections: bool = True,
        show_obstacles: bool = True
    ) -> Dict[str, Any]:
        """Enable debug visualization for a navigation layer."""
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            params = {
                "layer_name": layer_name,
                "show_navmesh": show_navmesh,
                "show_connections": show_connections,
                "show_obstacles": show_obstacles
            }
            
            logger.info(f"Debugging navigation layer with params: {params}")
            response = unreal.send_command("debug_navigation_layer", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Navigation debug response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error debugging navigation layer: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def validate_navigation_setup(
        ctx: Context,
        layer_names: List[str] = None
    ) -> Dict[str, Any]:
        """Validate the multilayer navigation system configuration."""
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            params = {
                "layer_names": layer_names or []
            }
            
            logger.info(f"Validating navigation setup with params: {params}")
            response = unreal.send_command("validate_navigation_setup", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Navigation validation response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error validating navigation setup: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    logger.info("Pathfinding tools registered successfully")