// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronGameMode.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronGameMode() {}

// ********** Begin Cross Module References ********************************************************
AURACRON_API UClass* Z_Construct_UClass_AAuracronGameMode();
AURACRON_API UClass* Z_Construct_UClass_AAuracronGameMode_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_AGameModeBase();
UPackage* Z_Construct_UPackage__Script_Auracron();
// ********** End Cross Module References **********************************************************

// ********** Begin Class AAuracronGameMode Function GetCurrentGamePhase ***************************
struct Z_Construct_UFunction_AAuracronGameMode_GetCurrentGamePhase_Statics
{
	struct AuracronGameMode_eventGetCurrentGamePhase_Parms
	{
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Auracron|GamePhases" },
		{ "ModuleRelativePath", "Public/AuracronGameMode.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_AAuracronGameMode_GetCurrentGamePhase_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronGameMode_eventGetCurrentGamePhase_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronGameMode_GetCurrentGamePhase_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronGameMode_GetCurrentGamePhase_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronGameMode_GetCurrentGamePhase_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronGameMode_GetCurrentGamePhase_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronGameMode, nullptr, "GetCurrentGamePhase", Z_Construct_UFunction_AAuracronGameMode_GetCurrentGamePhase_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronGameMode_GetCurrentGamePhase_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronGameMode_GetCurrentGamePhase_Statics::AuracronGameMode_eventGetCurrentGamePhase_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronGameMode_GetCurrentGamePhase_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronGameMode_GetCurrentGamePhase_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronGameMode_GetCurrentGamePhase_Statics::AuracronGameMode_eventGetCurrentGamePhase_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronGameMode_GetCurrentGamePhase()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronGameMode_GetCurrentGamePhase_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronGameMode::execGetCurrentGamePhase)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->GetCurrentGamePhase();
	P_NATIVE_END;
}
// ********** End Class AAuracronGameMode Function GetCurrentGamePhase *****************************

// ********** Begin Class AAuracronGameMode Function InitializeMultilayerSystem ********************
struct Z_Construct_UFunction_AAuracronGameMode_InitializeMultilayerSystem_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Auracron|Layers" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Fun\xc3\xa7\xc3\xb5""es Blueprint\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronGameMode.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fun\xc3\xa7\xc3\xb5""es Blueprint" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronGameMode_InitializeMultilayerSystem_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronGameMode, nullptr, "InitializeMultilayerSystem", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronGameMode_InitializeMultilayerSystem_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronGameMode_InitializeMultilayerSystem_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAuracronGameMode_InitializeMultilayerSystem()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronGameMode_InitializeMultilayerSystem_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronGameMode::execInitializeMultilayerSystem)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->InitializeMultilayerSystem();
	P_NATIVE_END;
}
// ********** End Class AAuracronGameMode Function InitializeMultilayerSystem **********************

// ********** Begin Class AAuracronGameMode Function TransitionToNextPhase *************************
struct Z_Construct_UFunction_AAuracronGameMode_TransitionToNextPhase_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Auracron|GamePhases" },
		{ "ModuleRelativePath", "Public/AuracronGameMode.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronGameMode_TransitionToNextPhase_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronGameMode, nullptr, "TransitionToNextPhase", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronGameMode_TransitionToNextPhase_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronGameMode_TransitionToNextPhase_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAuracronGameMode_TransitionToNextPhase()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronGameMode_TransitionToNextPhase_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronGameMode::execTransitionToNextPhase)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->TransitionToNextPhase();
	P_NATIVE_END;
}
// ********** End Class AAuracronGameMode Function TransitionToNextPhase ***************************

// ********** Begin Class AAuracronGameMode ********************************************************
void AAuracronGameMode::StaticRegisterNativesAAuracronGameMode()
{
	UClass* Class = AAuracronGameMode::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "GetCurrentGamePhase", &AAuracronGameMode::execGetCurrentGamePhase },
		{ "InitializeMultilayerSystem", &AAuracronGameMode::execInitializeMultilayerSystem },
		{ "TransitionToNextPhase", &AAuracronGameMode::execTransitionToNextPhase },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_AAuracronGameMode;
UClass* AAuracronGameMode::GetPrivateStaticClass()
{
	using TClass = AAuracronGameMode;
	if (!Z_Registration_Info_UClass_AAuracronGameMode.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronGameMode"),
			Z_Registration_Info_UClass_AAuracronGameMode.InnerSingleton,
			StaticRegisterNativesAAuracronGameMode,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_AAuracronGameMode.InnerSingleton;
}
UClass* Z_Construct_UClass_AAuracronGameMode_NoRegister()
{
	return AAuracronGameMode::GetPrivateStaticClass();
}
struct Z_Construct_UClass_AAuracronGameMode_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "HideCategories", "Info Rendering MovementReplication Replication Actor Input Movement Collision Rendering HLOD WorldPartition DataLayers Transformation" },
		{ "IncludePath", "AuracronGameMode.h" },
		{ "ModuleRelativePath", "Public/AuracronGameMode.h" },
		{ "ShowCategories", "Input|MouseInput Input|TouchInput" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnablePlanicieRadiante_MetaData[] = {
		{ "Category", "Auracron|Layers" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Sistema de Tr\xc3\xaas Camadas\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronGameMode.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sistema de Tr\xc3\xaas Camadas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableFirmamentoZephyr_MetaData[] = {
		{ "Category", "Auracron|Layers" },
		{ "ModuleRelativePath", "Public/AuracronGameMode.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableAbismoUmbral_MetaData[] = {
		{ "Category", "Auracron|Layers" },
		{ "ModuleRelativePath", "Public/AuracronGameMode.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EarlyGameDuration_MetaData[] = {
		{ "Category", "Auracron|GamePhases" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Sistema de Fases de Jogo\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronGameMode.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sistema de Fases de Jogo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MidGameDuration_MetaData[] = {
		{ "Category", "Auracron|GamePhases" },
		{ "ModuleRelativePath", "Public/AuracronGameMode.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LateGameStart_MetaData[] = {
		{ "Category", "Auracron|GamePhases" },
		{ "ModuleRelativePath", "Public/AuracronGameMode.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bEnablePlanicieRadiante_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnablePlanicieRadiante;
	static void NewProp_bEnableFirmamentoZephyr_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableFirmamentoZephyr;
	static void NewProp_bEnableAbismoUmbral_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableAbismoUmbral;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EarlyGameDuration;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MidGameDuration;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LateGameStart;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_AAuracronGameMode_GetCurrentGamePhase, "GetCurrentGamePhase" }, // 1563661580
		{ &Z_Construct_UFunction_AAuracronGameMode_InitializeMultilayerSystem, "InitializeMultilayerSystem" }, // 3499621923
		{ &Z_Construct_UFunction_AAuracronGameMode_TransitionToNextPhase, "TransitionToNextPhase" }, // 4049109240
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<AAuracronGameMode>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
void Z_Construct_UClass_AAuracronGameMode_Statics::NewProp_bEnablePlanicieRadiante_SetBit(void* Obj)
{
	((AAuracronGameMode*)Obj)->bEnablePlanicieRadiante = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAuracronGameMode_Statics::NewProp_bEnablePlanicieRadiante = { "bEnablePlanicieRadiante", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAuracronGameMode), &Z_Construct_UClass_AAuracronGameMode_Statics::NewProp_bEnablePlanicieRadiante_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnablePlanicieRadiante_MetaData), NewProp_bEnablePlanicieRadiante_MetaData) };
void Z_Construct_UClass_AAuracronGameMode_Statics::NewProp_bEnableFirmamentoZephyr_SetBit(void* Obj)
{
	((AAuracronGameMode*)Obj)->bEnableFirmamentoZephyr = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAuracronGameMode_Statics::NewProp_bEnableFirmamentoZephyr = { "bEnableFirmamentoZephyr", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAuracronGameMode), &Z_Construct_UClass_AAuracronGameMode_Statics::NewProp_bEnableFirmamentoZephyr_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableFirmamentoZephyr_MetaData), NewProp_bEnableFirmamentoZephyr_MetaData) };
void Z_Construct_UClass_AAuracronGameMode_Statics::NewProp_bEnableAbismoUmbral_SetBit(void* Obj)
{
	((AAuracronGameMode*)Obj)->bEnableAbismoUmbral = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAuracronGameMode_Statics::NewProp_bEnableAbismoUmbral = { "bEnableAbismoUmbral", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAuracronGameMode), &Z_Construct_UClass_AAuracronGameMode_Statics::NewProp_bEnableAbismoUmbral_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableAbismoUmbral_MetaData), NewProp_bEnableAbismoUmbral_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAuracronGameMode_Statics::NewProp_EarlyGameDuration = { "EarlyGameDuration", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronGameMode, EarlyGameDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EarlyGameDuration_MetaData), NewProp_EarlyGameDuration_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAuracronGameMode_Statics::NewProp_MidGameDuration = { "MidGameDuration", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronGameMode, MidGameDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MidGameDuration_MetaData), NewProp_MidGameDuration_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAuracronGameMode_Statics::NewProp_LateGameStart = { "LateGameStart", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronGameMode, LateGameStart), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LateGameStart_MetaData), NewProp_LateGameStart_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_AAuracronGameMode_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronGameMode_Statics::NewProp_bEnablePlanicieRadiante,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronGameMode_Statics::NewProp_bEnableFirmamentoZephyr,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronGameMode_Statics::NewProp_bEnableAbismoUmbral,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronGameMode_Statics::NewProp_EarlyGameDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronGameMode_Statics::NewProp_MidGameDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronGameMode_Statics::NewProp_LateGameStart,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AAuracronGameMode_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_AAuracronGameMode_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_AGameModeBase,
	(UObject* (*)())Z_Construct_UPackage__Script_Auracron,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AAuracronGameMode_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_AAuracronGameMode_Statics::ClassParams = {
	&AAuracronGameMode::StaticClass,
	"Game",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_AAuracronGameMode_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_AAuracronGameMode_Statics::PropPointers),
	0,
	0x009003ACu,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_AAuracronGameMode_Statics::Class_MetaDataParams), Z_Construct_UClass_AAuracronGameMode_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_AAuracronGameMode()
{
	if (!Z_Registration_Info_UClass_AAuracronGameMode.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_AAuracronGameMode.OuterSingleton, Z_Construct_UClass_AAuracronGameMode_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_AAuracronGameMode.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(AAuracronGameMode);
AAuracronGameMode::~AAuracronGameMode() {}
// ********** End Class AAuracronGameMode **********************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Game_Auracron_Source_Auracron_Public_AuracronGameMode_h__Script_Auracron_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_AAuracronGameMode, AAuracronGameMode::StaticClass, TEXT("AAuracronGameMode"), &Z_Registration_Info_UClass_AAuracronGameMode, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(AAuracronGameMode), 597830112U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Game_Auracron_Source_Auracron_Public_AuracronGameMode_h__Script_Auracron_3848890739(TEXT("/Script/Auracron"),
	Z_CompiledInDeferFile_FID_Game_Auracron_Source_Auracron_Public_AuracronGameMode_h__Script_Auracron_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Game_Auracron_Source_Auracron_Public_AuracronGameMode_h__Script_Auracron_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
