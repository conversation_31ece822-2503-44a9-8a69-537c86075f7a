"""
World Partition Tools for Unreal MCP.

This module provides tools for managing World Partition multilayer systems in Unreal Engine.
"""

import logging
from typing import Dict, List, Any
from mcp.server.fastmcp import FastMCP, Context

# Get logger
logger = logging.getLogger("UnrealMCP")

def register_world_partition_tools(mcp: FastMCP):
    """Register World Partition tools with the MCP server."""
    
    @mcp.tool()
    def create_world_partition_level(
        ctx: Context,
        level_name: str,
        layer_type: str = "Firmamento",
        grid_size: int = 25600,
        cell_size: int = 1600
    ) -> Dict[str, Any]:
        """
        Create a new level with World Partition configured for multilayers.
        
        Args:
            level_name: Name of the level to be created
            layer_type: Type of layer (Firmamento, Planicie, Abismo)
            grid_size: Total grid size in Unreal units
            cell_size: Size of each streaming cell
        
        Returns:
            Operation result
        """
        # Import inside function to avoid circular imports
        from unreal_mcp_server import get_unreal_connection
        
        try:
            # Validate layer_type against C++ constants
            valid_layer_types = ["Firmamento", "Planicie", "Abismo"]
            if layer_type not in valid_layer_types:
                logger.error(f"Invalid layer_type: must be one of {valid_layer_types}")
                return {"success": False, "message": f"Invalid layer_type: must be one of {valid_layer_types}"}
            
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            params = {
                "level_name": level_name,
                "layer_type": layer_type,
                "grid_size": grid_size,
                "cell_size": cell_size
            }
            
            logger.info(f"Creating World Partition level with params: {params}")
            response = unreal.send_command("create_world_partition_level", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"World Partition level creation response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error creating World Partition level: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}

    @mcp.tool()
    def configure_streaming_cell(
        ctx: Context,
        level_name: str,
        cell_x: int,
        cell_y: int,
        layer_type: str,
        streaming_distance: float = 5000.0,
        priority: int = 1
    ) -> Dict[str, Any]:
        """
        Configure a specific streaming cell.
        
        Args:
            level_name: Name of the level
            cell_x: X coordinate of the cell
            cell_y: Y coordinate of the cell
            layer_type: Type of layer
            streaming_distance: Streaming distance
            priority: Loading priority (1-10)
        
        Returns:
            Operation result
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            # Validate layer_type against C++ constants
            valid_layer_types = ["Firmamento", "Planicie", "Abismo"]
            if layer_type not in valid_layer_types:
                logger.error(f"Invalid layer_type: must be one of {valid_layer_types}")
                return {"success": False, "message": f"Invalid layer_type: must be one of {valid_layer_types}"}
            
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            params = {
                "level_name": level_name,
                "cell_x": cell_x,
                "cell_y": cell_y,
                "layer_type": layer_type,
                "streaming_distance": float(streaming_distance),
                "priority": priority
            }
            
            logger.info(f"Configuring streaming cell with params: {params}")
            response = unreal.send_command("configure_streaming_cell", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Streaming cell configuration response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error configuring streaming cell: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}

    @mcp.tool()
    def create_layer_hierarchy(
        ctx: Context,
        parent_layer: str,
        child_layer: str,
        transition_type: str = "Portal"
    ) -> Dict[str, Any]:
        """
        Create hierarchy between layers for transitions.
        
        Args:
            parent_layer: Parent layer
            child_layer: Child layer
            transition_type: Type of transition (Portal, Seamless, Teleport)
        
        Returns:
            Operation result
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            # Validate transition_type against C++ constants
            valid_transition_types = ["Portal", "Seamless", "Teleport"]
            if transition_type not in valid_transition_types:
                logger.error(f"Invalid transition_type: must be one of {valid_transition_types}")
                return {"success": False, "message": f"Invalid transition_type: must be one of {valid_transition_types}"}
            
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            params = {
                "parent_layer": parent_layer,
                "child_layer": child_layer,
                "transition_type": transition_type
            }
            
            logger.info(f"Creating layer hierarchy with params: {params}")
            response = unreal.send_command("create_layer_hierarchy", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Layer hierarchy creation response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error creating layer hierarchy: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}

    @mcp.tool()
    def set_spatial_division_rules(
        ctx: Context,
        layer_type: str,
        division_algorithm: str = "Quadtree",
        max_depth: int = 8,
        min_objects_per_cell: int = 10
    ) -> Dict[str, Any]:
        """
        Set spatial division rules for a layer.
        
        Args:
            layer_type: Type of layer
            division_algorithm: Division algorithm (Quadtree, Octree, Grid)
            max_depth: Maximum tree depth
            min_objects_per_cell: Minimum objects per cell
        
        Returns:
            Operation result
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            # Validate layer_type against C++ constants
            valid_layer_types = ["Firmamento", "Planicie", "Abismo"]
            if layer_type not in valid_layer_types:
                logger.error(f"Invalid layer_type: must be one of {valid_layer_types}")
                return {"success": False, "message": f"Invalid layer_type: must be one of {valid_layer_types}"}
            
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            params = {
                "layer_type": layer_type,
                "division_algorithm": division_algorithm,
                "max_depth": max_depth,
                "min_objects_per_cell": min_objects_per_cell
            }
            
            logger.info(f"Setting spatial division rules with params: {params}")
            response = unreal.send_command("set_spatial_division_rules", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Spatial division rules response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error setting spatial division rules: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}

    @mcp.tool()
    def configure_layer_streaming_manager(
        ctx: Context,
        layer_type: str,
        streaming_policy: str = "Distance",
        memory_budget_mb: int = 512
    ) -> Dict[str, Any]:
        """
        Configure the streaming manager for a layer.
        
        Args:
            layer_type: Type of layer
            streaming_policy: Streaming policy (Distance, Priority, Memory)
            memory_budget_mb: Memory budget in MB
        
        Returns:
            Operation result
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            # Validate layer_type against C++ constants
            valid_layer_types = ["Firmamento", "Planicie", "Abismo"]
            if layer_type not in valid_layer_types:
                logger.error(f"Invalid layer_type: must be one of {valid_layer_types}")
                return {"success": False, "message": f"Invalid layer_type: must be one of {valid_layer_types}"}
            
            # Validate streaming_policy
            valid_policies = ["Distance", "Priority", "Memory"]
            if streaming_policy not in valid_policies:
                error_msg = f"Invalid streaming_policy: {streaming_policy}. Valid options: {valid_policies}"
                logger.error(error_msg)
                return {"success": False, "message": error_msg}
            
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            params = {
                "layer_type": layer_type,
                "streaming_policy": streaming_policy,
                "memory_budget_mb": memory_budget_mb
            }
            
            logger.info(f"Configuring layer streaming manager with params: {params}")
            response = unreal.send_command("configure_layer_streaming_manager", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Layer streaming manager configuration response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error configuring layer streaming manager: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}

    @mcp.tool()
    def get_world_partition_status(
        ctx: Context,
        level_name: str
    ) -> Dict[str, Any]:
        """
        Get the current World Partition status of a level.
        
        Args:
            level_name: Name of the level
        
        Returns:
            World Partition status
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            # Validate optimization_type against C++ implementation
            valid_optimization_types = ["Performance", "Memory", "Quality"]
            if optimization_type not in valid_optimization_types:
                error_msg = f"Invalid optimization_type: {optimization_type}. Valid options: {valid_optimization_types}"
                logger.error(error_msg)
                return {"success": False, "message": error_msg}
            
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            params = {
                "level_name": level_name
            }
            
            logger.info(f"Getting World Partition status with params: {params}")
            response = unreal.send_command("get_world_partition_status", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"World Partition status response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error getting World Partition status: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}

    @mcp.tool()
    def optimize_streaming_cells(
        ctx: Context,
        level_name: str,
        optimization_type: str = "Performance"
    ) -> Dict[str, Any]:
        """
        Optimize the streaming cells of a level.
        
        Args:
            level_name: Name of the level
            optimization_type: Type of optimization (Performance, Memory, Quality)
        
        Returns:
            Optimization result
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            params = {
                "level_name": level_name,
                "optimization_type": optimization_type
            }
            
            logger.info(f"Optimizing streaming cells with params: {params}")
            response = unreal.send_command("optimize_streaming_cells", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Streaming cells optimization response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error optimizing streaming cells: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    # Auracron-specific streaming tools
    @mcp.tool()
    def stream_in_layer(
        ctx: Context,
        layer_type: str,
        streaming_radius: float = 4000.0,
        priority: int = 1,
        force_load: bool = False
    ) -> Dict[str, Any]:
        """
        Stream in a specific layer type for Auracron architecture.
        
        Args:
            layer_type: Type of layer (Firmamento, Planicie, Abismo)
            streaming_radius: Custom streaming radius in Unreal units
            priority: Streaming priority (1-10, higher = more important)
            force_load: Force immediate loading vs. activation
            
        Returns:
            Operation result
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            # Validate layer_type against C++ constants
            valid_layer_types = ["Firmamento", "Planicie", "Abismo"]
            if layer_type not in valid_layer_types:
                logger.error(f"Invalid layer_type: must be one of {valid_layer_types}")
                return {"success": False, "message": f"Invalid layer_type: must be one of {valid_layer_types}"}
            
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            params = {
                "layer_type": layer_type,
                "streaming_radius": float(streaming_radius),
                "priority": priority,
                "force_load": force_load
            }
            
            logger.info(f"Streaming in layer with params: {params}")
            response = unreal.send_command("stream_in_layer", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Stream in layer response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error streaming in layer: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}

    @mcp.tool()
    def stream_out_layer(
        ctx: Context,
        layer_type: str,
        force_unload: bool = False
    ) -> Dict[str, Any]:
        """
        Stream out a specific layer type for Auracron architecture.
        
        Args:
            layer_type: Type of layer (Firmamento, Planicie, Abismo)
            force_unload: Force immediate unloading
            
        Returns:
            Operation result
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            # Validate layer_type against C++ constants
            valid_layer_types = ["Firmamento", "Planicie", "Abismo"]
            if layer_type not in valid_layer_types:
                logger.error(f"Invalid layer_type: must be one of {valid_layer_types}")
                return {"success": False, "message": f"Invalid layer_type: must be one of {valid_layer_types}"}
            
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            params = {
                "layer_type": layer_type,
                "force_unload": force_unload
            }
            
            logger.info(f"Streaming out layer with params: {params}")
            response = unreal.send_command("stream_out_layer", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Stream out layer response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error streaming out layer: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}

    @mcp.tool()
    def preload_vertical_connectors(
        ctx: Context,
        preload_radius: float = 6000.0,
        source_layer: str = "",
        target_layer: str = ""
    ) -> Dict[str, Any]:
        """
        Preload vertical connectors between layers for Auracron architecture.
        
        Args:
            preload_radius: Radius for preloading connectors in Unreal units
            source_layer: Source layer for specific connector (optional)
            target_layer: Target layer for specific connector (optional)
            
        Returns:
            Operation result
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            params = {
                "preload_radius": float(preload_radius)
            }
            
            if source_layer:
                params["source_layer"] = source_layer
            if target_layer:
                params["target_layer"] = target_layer
            
            logger.info(f"Preloading vertical connectors with params: {params}")
            response = unreal.send_command("preload_vertical_connectors", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Preload vertical connectors response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error preloading vertical connectors: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}

    @mcp.tool()
    def get_layer_load_states(
        ctx: Context,
        layer_filter: str = ""
    ) -> Dict[str, Any]:
        """
        Get current load states for all layers in Auracron architecture.
        
        Args:
            layer_filter: Optional filter to limit results to specific layer names
            
        Returns:
            Layer load states
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            params = {}
            if layer_filter:
                params["layer_filter"] = layer_filter
            
            logger.info(f"Getting layer load states with params: {params}")
            response = unreal.send_command("get_layer_load_states", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Layer load states response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error getting layer load states: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    logger.info("World Partition tools registered successfully")