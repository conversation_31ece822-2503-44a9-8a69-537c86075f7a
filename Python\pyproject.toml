[project]
name = "unreal-mcp"
version = "0.1.0"
description = "Unreal MCP Server: A server for Unreal Engine integration via the Model Context Protocol (MCP)."
readme = "README.md"
requires-python = ">=3.10"
dependencies = [
  "mcp[cli]>=1.4.1",
  "fastmcp>=2.11.0",
  "uvicorn",
  "fastapi",
  "pydantic>=2.6.1",
  "requests",
]

[build-system]
requires = ["setuptools>=42", "wheel"]
build-backend = "setuptools.build_meta"

[tool.setuptools]
# The main server script is a single-file module
py-modules = ["unreal_mcp_server"] 
