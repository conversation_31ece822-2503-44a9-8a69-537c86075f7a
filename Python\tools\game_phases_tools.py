"""Game Phases Tools for Unreal MCP.

This module provides tools for creating and managing game phases
including Early Game, Mid Game, and Late Game with temporal milestones.
"""

import logging
from typing import Dict, List, Any, Optional
from mcp.server.fastmcp import FastMCP, Context

# Get logger
logger = logging.getLogger("UnrealMCP")

def register_game_phases_tools(mcp: FastMCP):
    """Register Game Phases tools with the MCP server."""
    
    @mcp.tool()
    def create_early_game_phase(
        ctx: Context,
        early_game_config: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Create the Early Game phase (0-15 minutes).
        
        Args:
            ctx: MCP Context
            early_game_config: Early game configuration as JSON object containing:
                - duration_minutes: Duration of early game in minutes (default: 15)
                - enable_laning_focus: Enable laning phase focus (default: True)
                - enable_jungle_establishment: Enable jungle establishment (default: True)
        
        Returns:
            Dict[str, Any]: Operation result
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            # Default configuration for Early Game
            default_config = {
                "phase_name": "early_game",
                "duration_minutes": 15,
                "start_time": 0,
                "end_time": 15,
                "primary_objectives": [
                    "Estabelecer vantagem de CS",
                    "Controlar posicionamento de waves",
                    "Evitar ganks enquanto pressiona",
                    "Estabelecer controle de visão",
                    "Primeiro clear da jungle",
                    "Primeiros recalls estratégicos"
                ],
                "key_mechanics": {
                    "laning_phase": {
                        "enabled": True,
                        "focus": "individual_skill_expression",
                        "mechanics": [
                            "last_hitting_minions",
                            "trading_stance",
                            "wave_management",
                            "back_timing",
                            "mana_management"
                        ]
                    },
                    "jungle_establishment": {
                        "enabled": True,
                        "focus": "clear_routes_and_ganks",
                        "mechanics": [
                            "optimal_clear_paths",
                            "gank_timing",
                            "objective_control",
                            "counter_jungling"
                        ]
                    },
                    "vision_control": {
                        "enabled": True,
                        "focus": "basic_warding",
                        "mechanics": [
                            "trinket_ward_placement",
                            "river_control",
                            "jungle_entrances",
                            "lane_bushes"
                        ]
                    }
                },
                "milestone_events": {
                    "first_blood": {
                        "typical_time": "2-5 minutes",
                        "impact": "psychological_advantage"
                    },
                    "first_tower": {
                        "typical_time": "8-12 minutes",
                        "impact": "map_control_shift"
                    },
                    "first_dragon": {
                        "typical_time": "6-10 minutes",
                        "impact": "team_buff_advantage"
                    },
                    "jungle_item_completion": {
                        "typical_time": "6-8 minutes",
                        "impact": "jungle_power_spike"
                    }
                },
                "transition_conditions": {
                    "to_mid_game": [
                        "multiple_towers_destroyed",
                        "team_grouping_begins",
                        "objective_contests_increase",
                        "roaming_frequency_increases"
                    ]
                }
            }
            
            # Merge provided config with defaults
            if early_game_config:
                default_config.update(early_game_config)
            
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            logger.info(f"Creating Early Game phase with config: {default_config}")
            response = unreal.send_command("create_early_game_phase", default_config)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Early Game phase creation response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error creating Early Game phase: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def create_mid_game_phase(
        ctx: Context,
        mid_game_config: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Create the Mid Game phase (15-30 minutes).
        
        Args:
            ctx: MCP Context
            mid_game_config: Mid game configuration as JSON object containing:
                - duration_minutes: Duration of mid game in minutes (default: 15)
                - enable_teamfight_focus: Enable teamfight focus (default: True)
                - enable_objective_control: Enable objective control (default: True)
        
        Returns:
            Dict[str, Any]: Operation result
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            # Default configuration for Mid Game
            default_config = {
                "phase_name": "mid_game",
                "duration_minutes": 15,
                "start_time": 15,
                "end_time": 30,
                "primary_objectives": [
                    "Transição para teamfights",
                    "Controle de objetivos neutros",
                    "Roaming e pressão no mapa",
                    "Estabelecer vantagens de itens",
                    "Controle de território",
                    "Preparação para late game"
                ],
                "key_mechanics": {
                    "teamfight_focus": {
                        "enabled": True,
                        "focus": "coordinated_team_play",
                        "mechanics": [
                            "team_positioning",
                            "engage_timing",
                            "target_prioritization",
                            "ability_coordination",
                            "peel_mechanics"
                        ]
                    },
                    "objective_control": {
                        "enabled": True,
                        "focus": "neutral_objectives",
                        "mechanics": [
                            "dragon_control",
                            "herald_utilization",
                            "tower_sieging",
                            "jungle_control",
                            "vision_warfare"
                        ]
                    },
                    "map_pressure": {
                        "enabled": True,
                        "focus": "territorial_control",
                        "mechanics": [
                            "split_pushing",
                            "roam_timing",
                            "wave_manipulation",
                            "cross_map_plays",
                            "vertical_navigation_usage"
                        ]
                    }
                },
                "milestone_events": {
                    "first_teamfight": {
                        "typical_time": "15-18 minutes",
                        "impact": "team_coordination_test"
                    },
                    "multiple_dragons": {
                        "typical_time": "18-25 minutes",
                        "impact": "significant_team_buffs"
                    },
                    "baron_spawn": {
                        "typical_time": "20 minutes",
                        "impact": "major_objective_available"
                    },
                    "core_items_completed": {
                        "typical_time": "20-25 minutes",
                        "impact": "champion_power_spikes"
                    }
                },
                "transition_conditions": {
                    "to_late_game": [
                        "multiple_inhibitors_threatened",
                        "baron_contests_frequent",
                        "full_item_builds_approaching",
                        "death_timers_significant"
                    ]
                }
            }
            
            # Merge provided config with defaults
            if mid_game_config:
                default_config.update(mid_game_config)
            
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            logger.info(f"Creating Mid Game phase with config: {default_config}")
            response = unreal.send_command("create_mid_game_phase", default_config)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Mid Game phase creation response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error creating Mid Game phase: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}

    @mcp.tool()
    def create_late_game_phase(
        ctx: Context,
        late_game_config: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Create the Late Game phase (30+ minutes).

        Args:
            ctx: MCP Context
            late_game_config: Late game configuration as JSON object containing:
                - start_time: Start time in minutes (default: 30)
                - enable_high_stakes: Enable high stakes mechanics (default: True)
                - enable_elder_dragon: Enable Elder Dragon (default: True)

        Returns:
            Dict[str, Any]: Operation result
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            # Default configuration for Late Game
            default_config = {
                "phase_name": "late_game",
                "start_time": 30,
                "end_time": "game_end",
                "primary_objectives": [
                    "Posicionamento em teamfights",
                    "Controle de objetivos épicos",
                    "Pressão de split push",
                    "Execução de end game",
                    "Controle total do mapa vertical",
                    "Finalização da partida"
                ],
                "key_mechanics": {
                    "high_stakes_teamfights": {
                        "enabled": True,
                        "focus": "decisive_engagements",
                        "mechanics": [
                            "perfect_positioning_required",
                            "instant_death_potential",
                            "long_death_timers",
                            "game_ending_fights",
                            "ultimate_coordination"
                        ]
                    },
                    "epic_objectives": {
                        "enabled": True,
                        "focus": "baron_and_elder_dragon",
                        "mechanics": [
                            "baron_power_plays",
                            "elder_dragon_executes",
                            "objective_steals",
                            "vision_control_critical",
                            "team_coordination_essential"
                        ]
                    },
                    "end_game_execution": {
                        "enabled": True,
                        "focus": "closing_out_games",
                        "mechanics": [
                            "inhibitor_control",
                            "super_minion_waves",
                            "nexus_rushes",
                            "backdoor_attempts",
                            "base_races"
                        ]
                    }
                },
                "milestone_events": {
                    "elder_dragon_spawn": {
                        "typical_time": "35+ minutes",
                        "impact": "execute_threshold_damage"
                    },
                    "full_item_builds": {
                        "typical_time": "35-40 minutes",
                        "impact": "maximum_champion_power"
                    },
                    "multiple_inhibitors_down": {
                        "typical_time": "35+ minutes",
                        "impact": "super_minion_pressure"
                    },
                    "death_timers_60_plus": {
                        "typical_time": "40+ minutes",
                        "impact": "single_fight_game_enders"
                    }
                },
                "win_conditions": {
                    "nexus_destruction": "primary_win_condition",
                    "enemy_surrender": "alternative_win_condition",
                    "typical_methods": [
                        "teamfight_victory_push",
                        "baron_empowered_siege",
                        "split_push_pressure",
                        "backdoor_attempt",
                        "elder_dragon_execute_fight"
                    ]
                }
            }

            # Merge provided config with defaults
            if late_game_config:
                default_config.update(late_game_config)

            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            logger.info(f"Creating Late Game phase with config: {default_config}")
            response = unreal.send_command("create_late_game_phase", default_config)

            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}

            logger.info(f"Late Game phase creation response: {response}")
            return response

        except Exception as e:
            error_msg = f"Error creating Late Game phase: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}

    @mcp.tool()
    def setup_complete_game_phases_system(
        ctx: Context,
        system_config: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Setup the complete game phases system with all phases.

        Args:
            ctx: MCP Context
            system_config: System configuration as JSON object containing:
                - enable_all_phases: Enable all game phases (default: True)
                - enable_dynamic_transitions: Enable dynamic phase transitions (default: True)

        Returns:
            Dict[str, Any]: Operation result
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            # Default system configuration
            default_config = {
                "system_name": "complete_game_phases_system",
                "enable_all_phases": True,
                "enable_dynamic_transitions": True,
                "phase_transition_system": {
                    "enabled": True,
                    "transition_triggers": [
                        "time_based",
                        "objective_based",
                        "item_completion_based",
                        "team_behavior_based"
                    ],
                    "transition_announcements": True,
                    "phase_specific_mechanics": True
                }
            }

            # Merge provided config with defaults
            if system_config:
                default_config.update(system_config)

            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            logger.info(f"Setting up complete game phases system with config: {default_config}")
            response = unreal.send_command("setup_complete_game_phases_system", default_config)

            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}

            logger.info(f"Complete game phases system setup response: {response}")
            return response

        except Exception as e:
            error_msg = f"Error setting up complete game phases system: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
