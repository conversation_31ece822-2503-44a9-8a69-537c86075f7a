// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "Dom/JsonObject.h"

/**
 * Classe responsável por gerenciar comandos de Mecânicas de Combate.
 * 
 * Esta classe fornece funcionalidades para:
 * - <PERSON><PERSON><PERSON> <PERSON>ste<PERSON> (Physical, Magic, True, Mixed)
 * - Criar sistema de Crowd Control (Hard CC: stun, root, suppress, knockup, fear, charm, taunt)
 * - Criar sistema de Soft CC (slow, blind, silence, disarm, cripple)
 * - <PERSON><PERSON>r siste<PERSON> de C<PERSON> (regeneration, lifesteal, spell vamp, omnivamp)
 * - <PERSON><PERSON>r siste<PERSON> de Tenacidade (CC reduction, stacking mechanics)
 */
class UNREALMCP_API FUnrealMCPCombatMechanicsCommands
{
public:
    FUnrealMCPCombatMechanicsCommands();
    ~FUnrealMCPCombatMechanicsCommands();

    // Método principal para processar comandos
    TSharedPtr<FJsonObject> HandleCommand(const FString& CommandType, const TSharedPtr<FJsonObject>& Params);
    
    // ========================================================================
    // Métodos Principais de Mecânicas de Combate
    // ========================================================================

    /**
     * Cria sistema de Dano.
     */
    TSharedPtr<FJsonObject> HandleCreateDamageSystem(const TSharedPtr<FJsonObject>& CommandData);

    /**
     * Cria sistema de Crowd Control.
     */
    TSharedPtr<FJsonObject> HandleCreateCrowdControlSystem(const TSharedPtr<FJsonObject>& CommandData);

    /**
     * Cria sistema de Cura.
     */
    TSharedPtr<FJsonObject> HandleCreateHealingSystem(const TSharedPtr<FJsonObject>& CommandData);

    /**
     * Cria sistema de Tenacidade.
     */
    TSharedPtr<FJsonObject> HandleCreateTenacitySystem(const TSharedPtr<FJsonObject>& CommandData);

    // ========================================================================
    // Constantes Públicas
    // ========================================================================

    // Tipos de resposta
    static const FString RESPONSE_SUCCESS;
    static const FString RESPONSE_ERROR;
    static const FString RESPONSE_WARNING;
    static const FString RESPONSE_INFO;

private:
    // ========================================================================
    // Funções Auxiliares
    // ========================================================================

    /**
     * Cria resposta de erro.
     */
    TSharedPtr<FJsonObject> CreateErrorResponse(const FString& ErrorMessage, const FString& ErrorCode = TEXT("COMBAT_MECHANICS_ERROR"));

    /**
     * Cria resposta de sucesso.
     */
    TSharedPtr<FJsonObject> CreateSuccessResponse(const TSharedPtr<FJsonObject>& Data, const FString& Message = TEXT("Operation completed successfully"));

    // ========================================================================
    // Variáveis Privadas
    // ========================================================================

    /** Cache de configurações de combate */
    TMap<FString, TSharedPtr<FJsonObject>> CombatConfigCache;

    /** Estado atual dos sistemas de combate */
    TMap<FString, TSharedPtr<FJsonObject>> CombatStates;

    /** Flag para indicar se o sistema está inicializado */
    bool bIsInitialized;

    /** Timestamp da última atualização */
    FDateTime LastUpdateTime;
};
