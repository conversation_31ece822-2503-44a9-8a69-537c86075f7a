// Copyright Epic Games, Inc. All Rights Reserved.

#include "Commands/UnrealMCPJungleSystemCommands.h"

// ========================================================================
// Constantes
// ========================================================================

// Tipos de resposta
const FString FUnrealMCPJungleSystemCommands::RESPONSE_SUCCESS = TEXT("success");
const FString FUnrealMCPJungleSystemCommands::RESPONSE_ERROR = TEXT("error");
const FString FUnrealMCPJungleSystemCommands::RESPONSE_WARNING = TEXT("warning");
const FString FUnrealMCPJungleSystemCommands::RESPONSE_INFO = TEXT("info");

// Nomes dos jungles
const FString FUnrealMCPJungleSystemCommands::JUNGLE_PLANICIE_RADIANTE = TEXT("planicie_radiante_jungle");
const FString FUnrealMCPJungleSystemCommands::JUNGLE_FIRMAMENTO_ZEPHYR = TEXT("firmamento_zephyr_jungle");
const FString FUnrealMCPJungleSystemCommands::JUNGLE_ABISMO_UMBRAL = TEXT("abismo_umbral_jungle");

// Tipos de jungle
const FString FUnrealMCPJungleSystemCommands::JUNGLE_TYPE_TERRESTRIAL = TEXT("terrestrial");
const FString FUnrealMCPJungleSystemCommands::JUNGLE_TYPE_AERIAL = TEXT("aerial");
const FString FUnrealMCPJungleSystemCommands::JUNGLE_TYPE_UNDERGROUND = TEXT("underground");

// ========================================================================
// Construtor e Destrutor
// ========================================================================

FUnrealMCPJungleSystemCommands::FUnrealMCPJungleSystemCommands()
    : bIsInitialized(false)
    , LastUpdateTime(FDateTime::Now())
{
    bIsInitialized = true;
    UE_LOG(LogTemp, Log, TEXT("FUnrealMCPJungleSystemCommands: Sistema de Jungle inicializado"));
}

FUnrealMCPJungleSystemCommands::~FUnrealMCPJungleSystemCommands()
{
    // Limpar caches
    JungleConfigCache.Empty();
    JungleStates.Empty();
    
    UE_LOG(LogTemp, Log, TEXT("FUnrealMCPJungleSystemCommands: Sistema de Jungle finalizado"));
}

// ========================================================================
// Método Principal de Comando
// ========================================================================

TSharedPtr<FJsonObject> FUnrealMCPJungleSystemCommands::HandleCommand(const FString& CommandType, const TSharedPtr<FJsonObject>& Params)
{
    if (CommandType == TEXT("create_planicie_radiante_jungle"))
    {
        return HandleCreatePlanicieRadianteJungle(Params);
    }
    else if (CommandType == TEXT("create_firmamento_zephyr_jungle"))
    {
        return HandleCreateFirmamentoZephyrJungle(Params);
    }
    else if (CommandType == TEXT("create_abismo_umbral_jungle"))
    {
        return HandleCreateAbismoUmbralJungle(Params);
    }
    else if (CommandType == TEXT("create_scuttle_crabs_system"))
    {
        return HandleCreateScuttleCrabsSystem(Params);
    }
    else if (CommandType == TEXT("setup_jungle_clear_routes"))
    {
        return HandleSetupJungleClearRoutes(Params);
    }
    else
    {
        return CreateErrorResponse(FString::Printf(TEXT("Comando não reconhecido: %s"), *CommandType), TEXT("UNKNOWN_COMMAND"));
    }
}

// ========================================================================
// Implementações dos Comandos
// ========================================================================

TSharedPtr<FJsonObject> FUnrealMCPJungleSystemCommands::HandleCreatePlanicieRadianteJungle(const TSharedPtr<FJsonObject>& CommandData)
{
    if (!CommandData.IsValid())
    {
        return CreateErrorResponse(TEXT("Dados do comando inválidos"), TEXT("INVALID_COMMAND_DATA"));
    }
    
    UE_LOG(LogTemp, Log, TEXT("Criando jungle da Planície Radiante"));
    
    // Extrair configurações do jungle terrestre
    FString JungleName = JUNGLE_PLANICIE_RADIANTE;
    FString JungleType = JUNGLE_TYPE_TERRESTRIAL;
    int32 CampsCount = 24;
    bool bEnableRedBuff = true;
    bool bEnableBlueBuff = true;
    bool bEnableWolves = true;
    bool bEnableGromp = true;
    bool bEnableKrugs = true;
    bool bEnableRaptors = true;
    
    // Ler configurações do JSON se fornecidas
    CommandData->TryGetStringField(TEXT("jungle_name"), JungleName);
    CommandData->TryGetStringField(TEXT("jungle_type"), JungleType);
    CommandData->TryGetNumberField(TEXT("camps_count"), CampsCount);
    CommandData->TryGetBoolField(TEXT("enable_red_buff"), bEnableRedBuff);
    CommandData->TryGetBoolField(TEXT("enable_blue_buff"), bEnableBlueBuff);
    CommandData->TryGetBoolField(TEXT("enable_wolves"), bEnableWolves);
    CommandData->TryGetBoolField(TEXT("enable_gromp"), bEnableGromp);
    CommandData->TryGetBoolField(TEXT("enable_krugs"), bEnableKrugs);
    CommandData->TryGetBoolField(TEXT("enable_raptors"), bEnableRaptors);
    
    // Criar configuração do jungle
    TSharedPtr<FJsonObject> JungleConfig = MakeShared<FJsonObject>();
    JungleConfig->SetStringField(TEXT("jungle_name"), JungleName);
    JungleConfig->SetStringField(TEXT("jungle_type"), JungleType);
    JungleConfig->SetNumberField(TEXT("camps_count"), CampsCount);
    
    // Configurar monstros
    TSharedPtr<FJsonObject> Monsters = MakeShared<FJsonObject>();
    
    // Red Buff - Guardião Carmesim
    if (bEnableRedBuff)
    {
        TSharedPtr<FJsonObject> RedBuff = MakeShared<FJsonObject>();
        RedBuff->SetStringField(TEXT("name"), TEXT("Guardião Carmesim"));
        RedBuff->SetBoolField(TEXT("enabled"), true);
        RedBuff->SetNumberField(TEXT("health"), 1800);
        RedBuff->SetNumberField(TEXT("health_per_minute"), 180);
        RedBuff->SetNumberField(TEXT("damage"), 78);
        RedBuff->SetNumberField(TEXT("damage_per_minute"), 6);
        RedBuff->SetNumberField(TEXT("buff_duration"), 90);
        RedBuff->SetNumberField(TEXT("respawn_time"), 300);
        
        TArray<TSharedPtr<FJsonValue>> BuffEffects;
        BuffEffects.Add(MakeShared<FJsonValueString>(TEXT("+20% Slow em ataques")));
        BuffEffects.Add(MakeShared<FJsonValueString>(TEXT("+5 HP/s regeneração")));
        RedBuff->SetArrayField(TEXT("buff_effects"), BuffEffects);
        
        Monsters->SetObjectField(TEXT("red_buff"), RedBuff);
    }
    
    // Blue Buff - Sentinela Azul
    if (bEnableBlueBuff)
    {
        TSharedPtr<FJsonObject> BlueBuff = MakeShared<FJsonObject>();
        BlueBuff->SetStringField(TEXT("name"), TEXT("Sentinela Azul"));
        BlueBuff->SetBoolField(TEXT("enabled"), true);
        BlueBuff->SetNumberField(TEXT("health"), 1700);
        BlueBuff->SetNumberField(TEXT("health_per_minute"), 170);
        BlueBuff->SetNumberField(TEXT("damage"), 72);
        BlueBuff->SetNumberField(TEXT("damage_per_minute"), 5);
        BlueBuff->SetNumberField(TEXT("buff_duration"), 90);
        BlueBuff->SetNumberField(TEXT("respawn_time"), 300);
        
        TArray<TSharedPtr<FJsonValue>> BuffEffects;
        BuffEffects.Add(MakeShared<FJsonValueString>(TEXT("+10% CDR")));
        BuffEffects.Add(MakeShared<FJsonValueString>(TEXT("+5 MP/s regeneração")));
        BlueBuff->SetArrayField(TEXT("buff_effects"), BuffEffects);
        
        Monsters->SetObjectField(TEXT("blue_buff"), BlueBuff);
    }
    
    // Wolves - Matilha Sombria
    if (bEnableWolves)
    {
        TSharedPtr<FJsonObject> Wolves = MakeShared<FJsonObject>();
        Wolves->SetStringField(TEXT("name"), TEXT("Matilha Sombria"));
        Wolves->SetBoolField(TEXT("enabled"), true);
        Wolves->SetNumberField(TEXT("large_wolf_health"), 1200);
        Wolves->SetNumberField(TEXT("large_wolf_health_per_minute"), 120);
        Wolves->SetNumberField(TEXT("small_wolves_health"), 400);
        Wolves->SetNumberField(TEXT("small_wolves_health_per_minute"), 40);
        Wolves->SetNumberField(TEXT("small_wolves_count"), 2);
        Wolves->SetNumberField(TEXT("total_gold"), 95);
        Wolves->SetNumberField(TEXT("respawn_time"), 120);
        
        Monsters->SetObjectField(TEXT("wolves"), Wolves);
    }
    
    // Gromp - Sapo Gigante
    if (bEnableGromp)
    {
        TSharedPtr<FJsonObject> Gromp = MakeShared<FJsonObject>();
        Gromp->SetStringField(TEXT("name"), TEXT("Sapo Gigante"));
        Gromp->SetBoolField(TEXT("enabled"), true);
        Gromp->SetNumberField(TEXT("health"), 1500);
        Gromp->SetNumberField(TEXT("health_per_minute"), 150);
        Gromp->SetNumberField(TEXT("damage"), 84);
        Gromp->SetNumberField(TEXT("damage_per_minute"), 7);
        Gromp->SetStringField(TEXT("passive"), TEXT("Poison que causa dano ao longo do tempo"));
        Gromp->SetNumberField(TEXT("gold"), 105);
        Gromp->SetNumberField(TEXT("respawn_time"), 120);
        
        Monsters->SetObjectField(TEXT("gromp"), Gromp);
    }
    
    // Krugs - Golems de Pedra
    if (bEnableKrugs)
    {
        TSharedPtr<FJsonObject> Krugs = MakeShared<FJsonObject>();
        Krugs->SetStringField(TEXT("name"), TEXT("Golems de Pedra"));
        Krugs->SetBoolField(TEXT("enabled"), true);
        Krugs->SetNumberField(TEXT("large_krug_health"), 1000);
        Krugs->SetNumberField(TEXT("large_krug_health_per_minute"), 100);
        Krugs->SetNumberField(TEXT("medium_krug_health"), 600);
        Krugs->SetNumberField(TEXT("medium_krug_health_per_minute"), 60);
        Krugs->SetNumberField(TEXT("small_krugs_health"), 200);
        Krugs->SetNumberField(TEXT("small_krugs_health_per_minute"), 20);
        Krugs->SetNumberField(TEXT("small_krugs_count"), 4);
        Krugs->SetNumberField(TEXT("total_gold"), 84);
        Krugs->SetNumberField(TEXT("respawn_time"), 135);
        
        Monsters->SetObjectField(TEXT("krugs"), Krugs);
    }
    
    // Raptors - Aves Predadoras
    if (bEnableRaptors)
    {
        TSharedPtr<FJsonObject> Raptors = MakeShared<FJsonObject>();
        Raptors->SetStringField(TEXT("name"), TEXT("Aves Predadoras"));
        Raptors->SetBoolField(TEXT("enabled"), true);
        Raptors->SetNumberField(TEXT("large_raptor_health"), 800);
        Raptors->SetNumberField(TEXT("large_raptor_health_per_minute"), 80);
        Raptors->SetNumberField(TEXT("small_raptors_health"), 250);
        Raptors->SetNumberField(TEXT("small_raptors_health_per_minute"), 25);
        Raptors->SetNumberField(TEXT("small_raptors_count"), 5);
        Raptors->SetNumberField(TEXT("total_gold"), 100);
        Raptors->SetNumberField(TEXT("respawn_time"), 120);
        
        Monsters->SetObjectField(TEXT("raptors"), Raptors);
    }
    
    JungleConfig->SetObjectField(TEXT("monsters"), Monsters);
    
    // Salvar configuração no cache
    JungleConfigCache.Add(JungleName, JungleConfig);
    
    // Criar estado do jungle
    TSharedPtr<FJsonObject> JungleState = MakeShared<FJsonObject>();
    JungleState->SetStringField(TEXT("status"), TEXT("created"));
    JungleState->SetStringField(TEXT("creation_time"), FDateTime::Now().ToString());
    JungleState->SetBoolField(TEXT("active"), true);
    JungleStates.Add(JungleName, JungleState);
    
    // Criar resposta de sucesso
    TSharedPtr<FJsonObject> ResponseData = MakeShared<FJsonObject>();
    ResponseData->SetObjectField(TEXT("jungle_config"), JungleConfig);
    ResponseData->SetObjectField(TEXT("jungle_state"), JungleState);
    ResponseData->SetStringField(TEXT("jungle_description"), TEXT("Jungle terrestre com monstros tradicionais e buffs"));
    
    UE_LOG(LogTemp, Log, TEXT("Jungle da Planície Radiante criado com sucesso"));
    return CreateSuccessResponse(ResponseData, TEXT("Jungle da Planície Radiante criado com sucesso"));
}

TSharedPtr<FJsonObject> FUnrealMCPJungleSystemCommands::HandleCreateFirmamentoZephyrJungle(const TSharedPtr<FJsonObject>& CommandData)
{
    if (!CommandData.IsValid())
    {
        return CreateErrorResponse(TEXT("Dados do comando inválidos"), TEXT("INVALID_COMMAND_DATA"));
    }

    UE_LOG(LogTemp, Log, TEXT("Criando jungle do Firmamento Zephyr"));

    // Extrair configurações do jungle aéreo
    FString JungleName = JUNGLE_FIRMAMENTO_ZEPHYR;
    FString JungleType = JUNGLE_TYPE_AERIAL;
    bool bEnableStormElemental = true;
    bool bEnableWindSpirits = true;
    bool bEnableCloudDrakes = true;

    // Ler configurações do JSON se fornecidas
    CommandData->TryGetStringField(TEXT("jungle_name"), JungleName);
    CommandData->TryGetStringField(TEXT("jungle_type"), JungleType);
    CommandData->TryGetBoolField(TEXT("enable_storm_elemental"), bEnableStormElemental);
    CommandData->TryGetBoolField(TEXT("enable_wind_spirits"), bEnableWindSpirits);
    CommandData->TryGetBoolField(TEXT("enable_cloud_drakes"), bEnableCloudDrakes);

    // Criar configuração do jungle
    TSharedPtr<FJsonObject> JungleConfig = MakeShared<FJsonObject>();
    JungleConfig->SetStringField(TEXT("jungle_name"), JungleName);
    JungleConfig->SetStringField(TEXT("jungle_type"), JungleType);

    // Configurar monstros aéreos
    TSharedPtr<FJsonObject> Monsters = MakeShared<FJsonObject>();

    // Storm Elemental - Elemental da Tempestade
    if (bEnableStormElemental)
    {
        TSharedPtr<FJsonObject> StormElemental = MakeShared<FJsonObject>();
        StormElemental->SetStringField(TEXT("name"), TEXT("Elemental da Tempestade"));
        StormElemental->SetBoolField(TEXT("enabled"), true);
        StormElemental->SetNumberField(TEXT("health"), 2200);
        StormElemental->SetNumberField(TEXT("health_per_minute"), 200);
        StormElemental->SetNumberField(TEXT("buff_duration"), 120);
        StormElemental->SetNumberField(TEXT("respawn_time"), 360);

        TArray<TSharedPtr<FJsonValue>> Abilities;
        Abilities.Add(MakeShared<FJsonValueString>(TEXT("Raios que saltam entre alvos")));
        StormElemental->SetArrayField(TEXT("abilities"), Abilities);

        TArray<TSharedPtr<FJsonValue>> BuffEffects;
        BuffEffects.Add(MakeShared<FJsonValueString>(TEXT("+15% Velocidade de Movimento")));
        BuffEffects.Add(MakeShared<FJsonValueString>(TEXT("imunidade a slows")));
        StormElemental->SetArrayField(TEXT("buff_effects"), BuffEffects);

        Monsters->SetObjectField(TEXT("storm_elemental"), StormElemental);
    }

    // Wind Spirits - Espíritos do Vento
    if (bEnableWindSpirits)
    {
        TSharedPtr<FJsonObject> WindSpirits = MakeShared<FJsonObject>();
        WindSpirits->SetStringField(TEXT("name"), TEXT("Espíritos do Vento"));
        WindSpirits->SetBoolField(TEXT("enabled"), true);
        WindSpirits->SetNumberField(TEXT("large_spirit_health"), 900);
        WindSpirits->SetNumberField(TEXT("large_spirit_health_per_minute"), 90);
        WindSpirits->SetNumberField(TEXT("small_spirits_health"), 300);
        WindSpirits->SetNumberField(TEXT("small_spirits_health_per_minute"), 30);
        WindSpirits->SetNumberField(TEXT("small_spirits_count"), 2);
        WindSpirits->SetStringField(TEXT("passive"), TEXT("Empurram inimigos ao morrer"));
        WindSpirits->SetNumberField(TEXT("total_gold"), 110);
        WindSpirits->SetNumberField(TEXT("respawn_time"), 150);

        Monsters->SetObjectField(TEXT("wind_spirits"), WindSpirits);
    }

    // Cloud Drakes - Dragões das Nuvens
    if (bEnableCloudDrakes)
    {
        TSharedPtr<FJsonObject> CloudDrakes = MakeShared<FJsonObject>();
        CloudDrakes->SetStringField(TEXT("name"), TEXT("Dragões das Nuvens"));
        CloudDrakes->SetBoolField(TEXT("enabled"), true);
        CloudDrakes->SetNumberField(TEXT("alpha_drake_health"), 1100);
        CloudDrakes->SetNumberField(TEXT("alpha_drake_health_per_minute"), 110);
        CloudDrakes->SetNumberField(TEXT("beta_drakes_health"), 400);
        CloudDrakes->SetNumberField(TEXT("beta_drakes_health_per_minute"), 40);
        CloudDrakes->SetNumberField(TEXT("beta_drakes_count"), 2);
        CloudDrakes->SetStringField(TEXT("ability"), TEXT("Voo curto que evita ataques terrestres"));
        CloudDrakes->SetNumberField(TEXT("total_gold"), 95);
        CloudDrakes->SetNumberField(TEXT("respawn_time"), 135);

        Monsters->SetObjectField(TEXT("cloud_drakes"), CloudDrakes);
    }

    JungleConfig->SetObjectField(TEXT("monsters"), Monsters);

    // Salvar configuração no cache
    JungleConfigCache.Add(JungleName, JungleConfig);

    // Criar estado do jungle
    TSharedPtr<FJsonObject> JungleState = MakeShared<FJsonObject>();
    JungleState->SetStringField(TEXT("status"), TEXT("created"));
    JungleState->SetStringField(TEXT("creation_time"), FDateTime::Now().ToString());
    JungleState->SetBoolField(TEXT("active"), true);
    JungleStates.Add(JungleName, JungleState);

    // Criar resposta de sucesso
    TSharedPtr<FJsonObject> ResponseData = MakeShared<FJsonObject>();
    ResponseData->SetObjectField(TEXT("jungle_config"), JungleConfig);
    ResponseData->SetObjectField(TEXT("jungle_state"), JungleState);
    ResponseData->SetStringField(TEXT("jungle_description"), TEXT("Jungle aéreo com elementais e criaturas voadoras"));

    UE_LOG(LogTemp, Log, TEXT("Jungle do Firmamento Zephyr criado com sucesso"));
    return CreateSuccessResponse(ResponseData, TEXT("Jungle do Firmamento Zephyr criado com sucesso"));
}

TSharedPtr<FJsonObject> FUnrealMCPJungleSystemCommands::HandleCreateAbismoUmbralJungle(const TSharedPtr<FJsonObject>& CommandData)
{
    if (!CommandData.IsValid())
    {
        return CreateErrorResponse(TEXT("Dados do comando inválidos"), TEXT("INVALID_COMMAND_DATA"));
    }

    UE_LOG(LogTemp, Log, TEXT("Criando jungle do Abismo Umbral"));

    // Extrair configurações do jungle subterrâneo
    FString JungleName = JUNGLE_ABISMO_UMBRAL;
    FString JungleType = JUNGLE_TYPE_UNDERGROUND;
    bool bEnableShadowWraith = true;
    bool bEnableVoidSpiders = true;
    bool bEnableBoneCollectors = true;

    // Ler configurações do JSON se fornecidas
    CommandData->TryGetStringField(TEXT("jungle_name"), JungleName);
    CommandData->TryGetStringField(TEXT("jungle_type"), JungleType);
    CommandData->TryGetBoolField(TEXT("enable_shadow_wraith"), bEnableShadowWraith);
    CommandData->TryGetBoolField(TEXT("enable_void_spiders"), bEnableVoidSpiders);
    CommandData->TryGetBoolField(TEXT("enable_bone_collectors"), bEnableBoneCollectors);

    // Criar configuração do jungle
    TSharedPtr<FJsonObject> JungleConfig = MakeShared<FJsonObject>();
    JungleConfig->SetStringField(TEXT("jungle_name"), JungleName);
    JungleConfig->SetStringField(TEXT("jungle_type"), JungleType);

    // Configurar monstros subterrâneos
    TSharedPtr<FJsonObject> Monsters = MakeShared<FJsonObject>();

    // Shadow Wraith - Espectro Sombrio
    if (bEnableShadowWraith)
    {
        TSharedPtr<FJsonObject> ShadowWraith = MakeShared<FJsonObject>();
        ShadowWraith->SetStringField(TEXT("name"), TEXT("Espectro Sombrio"));
        ShadowWraith->SetBoolField(TEXT("enabled"), true);
        ShadowWraith->SetNumberField(TEXT("health"), 2000);
        ShadowWraith->SetNumberField(TEXT("health_per_minute"), 180);
        ShadowWraith->SetNumberField(TEXT("buff_duration"), 100);
        ShadowWraith->SetNumberField(TEXT("respawn_time"), 330);

        TArray<TSharedPtr<FJsonValue>> Abilities;
        Abilities.Add(MakeShared<FJsonValueString>(TEXT("Invisibilidade temporária")));
        ShadowWraith->SetArrayField(TEXT("abilities"), Abilities);

        TArray<TSharedPtr<FJsonValue>> BuffEffects;
        BuffEffects.Add(MakeShared<FJsonValueString>(TEXT("+20% Lethality")));
        BuffEffects.Add(MakeShared<FJsonValueString>(TEXT("+10% Spell Vamp")));
        ShadowWraith->SetArrayField(TEXT("buff_effects"), BuffEffects);

        Monsters->SetObjectField(TEXT("shadow_wraith"), ShadowWraith);
    }

    // Void Spiders - Aranhas do Vazio
    if (bEnableVoidSpiders)
    {
        TSharedPtr<FJsonObject> VoidSpiders = MakeShared<FJsonObject>();
        VoidSpiders->SetStringField(TEXT("name"), TEXT("Aranhas do Vazio"));
        VoidSpiders->SetBoolField(TEXT("enabled"), true);
        VoidSpiders->SetNumberField(TEXT("queen_spider_health"), 1000);
        VoidSpiders->SetNumberField(TEXT("queen_spider_health_per_minute"), 100);
        VoidSpiders->SetNumberField(TEXT("spiderlings_health"), 200);
        VoidSpiders->SetNumberField(TEXT("spiderlings_health_per_minute"), 20);
        VoidSpiders->SetNumberField(TEXT("spiderlings_count"), 4);
        VoidSpiders->SetStringField(TEXT("passive"), TEXT("Spawnam filhotes ao morrer"));
        VoidSpiders->SetNumberField(TEXT("total_gold"), 105);
        VoidSpiders->SetNumberField(TEXT("respawn_time"), 165);

        Monsters->SetObjectField(TEXT("void_spiders"), VoidSpiders);
    }

    // Bone Collectors - Coletores de Ossos
    if (bEnableBoneCollectors)
    {
        TSharedPtr<FJsonObject> BoneCollectors = MakeShared<FJsonObject>();
        BoneCollectors->SetStringField(TEXT("name"), TEXT("Coletores de Ossos"));
        BoneCollectors->SetBoolField(TEXT("enabled"), true);
        BoneCollectors->SetNumberField(TEXT("large_collector_health"), 850);
        BoneCollectors->SetNumberField(TEXT("large_collector_health_per_minute"), 85);
        BoneCollectors->SetNumberField(TEXT("small_collectors_health"), 350);
        BoneCollectors->SetNumberField(TEXT("small_collectors_health_per_minute"), 35);
        BoneCollectors->SetNumberField(TEXT("small_collectors_count"), 2);
        BoneCollectors->SetStringField(TEXT("ability"), TEXT("Ressuscitam uma vez com 50% da vida"));
        BoneCollectors->SetNumberField(TEXT("total_gold"), 90);
        BoneCollectors->SetNumberField(TEXT("respawn_time"), 150);

        Monsters->SetObjectField(TEXT("bone_collectors"), BoneCollectors);
    }

    JungleConfig->SetObjectField(TEXT("monsters"), Monsters);

    // Salvar configuração no cache
    JungleConfigCache.Add(JungleName, JungleConfig);

    // Criar estado do jungle
    TSharedPtr<FJsonObject> JungleState = MakeShared<FJsonObject>();
    JungleState->SetStringField(TEXT("status"), TEXT("created"));
    JungleState->SetStringField(TEXT("creation_time"), FDateTime::Now().ToString());
    JungleState->SetBoolField(TEXT("active"), true);
    JungleStates.Add(JungleName, JungleState);

    // Criar resposta de sucesso
    TSharedPtr<FJsonObject> ResponseData = MakeShared<FJsonObject>();
    ResponseData->SetObjectField(TEXT("jungle_config"), JungleConfig);
    ResponseData->SetObjectField(TEXT("jungle_state"), JungleState);
    ResponseData->SetStringField(TEXT("jungle_description"), TEXT("Jungle subterrâneo com criaturas sombrias e espectrais"));

    UE_LOG(LogTemp, Log, TEXT("Jungle do Abismo Umbral criado com sucesso"));
    return CreateSuccessResponse(ResponseData, TEXT("Jungle do Abismo Umbral criado com sucesso"));
}

TSharedPtr<FJsonObject> FUnrealMCPJungleSystemCommands::HandleCreateScuttleCrabsSystem(const TSharedPtr<FJsonObject>& CommandData)
{
    if (!CommandData.IsValid())
    {
        return CreateErrorResponse(TEXT("Dados do comando inválidos"), TEXT("INVALID_COMMAND_DATA"));
    }

    UE_LOG(LogTemp, Log, TEXT("Criando sistema de Scuttle Crabs"));

    // Configurações padrão
    bool bEnableTerrestrial = true;
    bool bEnableEthereal = true;
    bool bEnableShadow = true;

    // Ler configurações do JSON se fornecidas
    CommandData->TryGetBoolField(TEXT("enable_terrestrial"), bEnableTerrestrial);
    CommandData->TryGetBoolField(TEXT("enable_ethereal"), bEnableEthereal);
    CommandData->TryGetBoolField(TEXT("enable_shadow"), bEnableShadow);

    TSharedPtr<FJsonObject> SystemConfig = MakeShared<FJsonObject>();
    SystemConfig->SetStringField(TEXT("system_name"), TEXT("scuttle_crabs_system"));

    TSharedPtr<FJsonObject> ScuttleCrabs = MakeShared<FJsonObject>();

    // Scuttle Crab Terrestre
    if (bEnableTerrestrial)
    {
        TSharedPtr<FJsonObject> Terrestrial = MakeShared<FJsonObject>();
        Terrestrial->SetStringField(TEXT("name"), TEXT("Scuttle Crab Terrestre"));
        Terrestrial->SetBoolField(TEXT("enabled"), true);
        Terrestrial->SetStringField(TEXT("location"), TEXT("Rio da Planície Radiante"));
        Terrestrial->SetNumberField(TEXT("health"), 1000);
        Terrestrial->SetNumberField(TEXT("health_per_minute"), 35);
        Terrestrial->SetNumberField(TEXT("gold_min"), 70);
        Terrestrial->SetNumberField(TEXT("gold_max"), 140);
        Terrestrial->SetNumberField(TEXT("respawn_time"), 150);

        TArray<TSharedPtr<FJsonValue>> BuffEffects;
        BuffEffects.Add(MakeShared<FJsonValueString>(TEXT("Visão")));
        BuffEffects.Add(MakeShared<FJsonValueString>(TEXT("velocidade em rio")));
        Terrestrial->SetArrayField(TEXT("buff_effects"), BuffEffects);

        ScuttleCrabs->SetObjectField(TEXT("terrestrial"), Terrestrial);
    }

    // Scuttle Crab Etérea
    if (bEnableEthereal)
    {
        TSharedPtr<FJsonObject> Ethereal = MakeShared<FJsonObject>();
        Ethereal->SetStringField(TEXT("name"), TEXT("Scuttle Crab Etérea"));
        Ethereal->SetBoolField(TEXT("enabled"), true);
        Ethereal->SetStringField(TEXT("location"), TEXT("Correntes de ar do Firmamento"));
        Ethereal->SetNumberField(TEXT("health"), 1200);
        Ethereal->SetNumberField(TEXT("health_per_minute"), 40);
        Ethereal->SetNumberField(TEXT("gold_min"), 80);
        Ethereal->SetNumberField(TEXT("gold_max"), 150);
        Ethereal->SetNumberField(TEXT("respawn_time"), 180);

        TArray<TSharedPtr<FJsonValue>> BuffEffects;
        BuffEffects.Add(MakeShared<FJsonValueString>(TEXT("Visão")));
        BuffEffects.Add(MakeShared<FJsonValueString>(TEXT("velocidade de movimento aéreo")));
        Ethereal->SetArrayField(TEXT("buff_effects"), BuffEffects);

        ScuttleCrabs->SetObjectField(TEXT("ethereal"), Ethereal);
    }

    // Scuttle Crab Sombria
    if (bEnableShadow)
    {
        TSharedPtr<FJsonObject> Shadow = MakeShared<FJsonObject>();
        Shadow->SetStringField(TEXT("name"), TEXT("Scuttle Crab Sombria"));
        Shadow->SetBoolField(TEXT("enabled"), true);
        Shadow->SetStringField(TEXT("location"), TEXT("Túneis do Abismo"));
        Shadow->SetNumberField(TEXT("health"), 900);
        Shadow->SetNumberField(TEXT("health_per_minute"), 30);
        Shadow->SetNumberField(TEXT("gold_min"), 75);
        Shadow->SetNumberField(TEXT("gold_max"), 145);
        Shadow->SetNumberField(TEXT("respawn_time"), 165);

        TArray<TSharedPtr<FJsonValue>> BuffEffects;
        BuffEffects.Add(MakeShared<FJsonValueString>(TEXT("Visão")));
        BuffEffects.Add(MakeShared<FJsonValueString>(TEXT("detecção de invisibilidade")));
        Shadow->SetArrayField(TEXT("buff_effects"), BuffEffects);

        ScuttleCrabs->SetObjectField(TEXT("shadow"), Shadow);
    }

    SystemConfig->SetObjectField(TEXT("scuttle_crabs"), ScuttleCrabs);

    // Criar resposta de sucesso
    TSharedPtr<FJsonObject> ResponseData = MakeShared<FJsonObject>();
    ResponseData->SetObjectField(TEXT("system_config"), SystemConfig);
    ResponseData->SetStringField(TEXT("system_description"), TEXT("Sistema de Scuttle Crabs especiais para todas as camadas"));

    UE_LOG(LogTemp, Log, TEXT("Sistema de Scuttle Crabs criado com sucesso"));
    return CreateSuccessResponse(ResponseData, TEXT("Sistema de Scuttle Crabs criado com sucesso"));
}

TSharedPtr<FJsonObject> FUnrealMCPJungleSystemCommands::HandleSetupJungleClearRoutes(const TSharedPtr<FJsonObject>& CommandData)
{
    if (!CommandData.IsValid())
    {
        return CreateErrorResponse(TEXT("Dados do comando inválidos"), TEXT("INVALID_COMMAND_DATA"));
    }

    UE_LOG(LogTemp, Log, TEXT("Configurando rotas de clear da jungle"));

    // Configurações padrão
    bool bEnableRedStart = true;
    bool bEnableBlueStart = true;
    bool bEnableVerticalClear = true;

    // Ler configurações do JSON se fornecidas
    CommandData->TryGetBoolField(TEXT("enable_red_start"), bEnableRedStart);
    CommandData->TryGetBoolField(TEXT("enable_blue_start"), bEnableBlueStart);
    CommandData->TryGetBoolField(TEXT("enable_vertical_clear"), bEnableVerticalClear);

    TSharedPtr<FJsonObject> SystemConfig = MakeShared<FJsonObject>();
    SystemConfig->SetStringField(TEXT("system_name"), TEXT("jungle_clear_routes"));

    TSharedPtr<FJsonObject> Routes = MakeShared<FJsonObject>();

    // Red Start Route
    if (bEnableRedStart)
    {
        TSharedPtr<FJsonObject> RedStart = MakeShared<FJsonObject>();
        RedStart->SetStringField(TEXT("name"), TEXT("Clear Iniciando no Red"));
        RedStart->SetBoolField(TEXT("enabled"), true);
        RedStart->SetNumberField(TEXT("total_time_minutes"), 3.5);
        RedStart->SetNumberField(TEXT("level_reached"), 4);
        RedStart->SetNumberField(TEXT("health_remaining_percent"), 60);

        TArray<TSharedPtr<FJsonValue>> Sequence;
        Sequence.Add(MakeShared<FJsonValueString>(TEXT("Red Buff")));
        Sequence.Add(MakeShared<FJsonValueString>(TEXT("Krugs")));
        Sequence.Add(MakeShared<FJsonValueString>(TEXT("Raptors")));
        Sequence.Add(MakeShared<FJsonValueString>(TEXT("Wolves")));
        Sequence.Add(MakeShared<FJsonValueString>(TEXT("Blue Buff")));
        Sequence.Add(MakeShared<FJsonValueString>(TEXT("Gromp")));
        RedStart->SetArrayField(TEXT("sequence"), Sequence);

        Routes->SetObjectField(TEXT("red_start"), RedStart);
    }

    // Blue Start Route
    if (bEnableBlueStart)
    {
        TSharedPtr<FJsonObject> BlueStart = MakeShared<FJsonObject>();
        BlueStart->SetStringField(TEXT("name"), TEXT("Clear Iniciando no Blue"));
        BlueStart->SetBoolField(TEXT("enabled"), true);
        BlueStart->SetNumberField(TEXT("total_time_minutes"), 3.75);
        BlueStart->SetNumberField(TEXT("level_reached"), 4);
        BlueStart->SetNumberField(TEXT("health_remaining_percent"), 55);

        TArray<TSharedPtr<FJsonValue>> Sequence;
        Sequence.Add(MakeShared<FJsonValueString>(TEXT("Blue Buff")));
        Sequence.Add(MakeShared<FJsonValueString>(TEXT("Gromp")));
        Sequence.Add(MakeShared<FJsonValueString>(TEXT("Wolves")));
        Sequence.Add(MakeShared<FJsonValueString>(TEXT("Raptors")));
        Sequence.Add(MakeShared<FJsonValueString>(TEXT("Red Buff")));
        Sequence.Add(MakeShared<FJsonValueString>(TEXT("Krugs")));
        BlueStart->SetArrayField(TEXT("sequence"), Sequence);

        Routes->SetObjectField(TEXT("blue_start"), BlueStart);
    }

    // Vertical Clear Route
    if (bEnableVerticalClear)
    {
        TSharedPtr<FJsonObject> VerticalClear = MakeShared<FJsonObject>();
        VerticalClear->SetStringField(TEXT("name"), TEXT("Clear Vertical (Cross-Realm)"));
        VerticalClear->SetBoolField(TEXT("enabled"), true);
        VerticalClear->SetNumberField(TEXT("total_time_minutes"), 4.25);
        VerticalClear->SetNumberField(TEXT("level_reached"), 4);
        VerticalClear->SetNumberField(TEXT("unique_buffs"), 3);

        TArray<TSharedPtr<FJsonValue>> Sequence;
        Sequence.Add(MakeShared<FJsonValueString>(TEXT("Red Terrestre")));
        Sequence.Add(MakeShared<FJsonValueString>(TEXT("Storm Elemental")));
        Sequence.Add(MakeShared<FJsonValueString>(TEXT("Shadow Wraith")));
        Sequence.Add(MakeShared<FJsonValueString>(TEXT("Blue Terrestre")));
        VerticalClear->SetArrayField(TEXT("sequence"), Sequence);

        Routes->SetObjectField(TEXT("vertical_clear"), VerticalClear);
    }

    SystemConfig->SetObjectField(TEXT("routes"), Routes);

    // Criar resposta de sucesso
    TSharedPtr<FJsonObject> ResponseData = MakeShared<FJsonObject>();
    ResponseData->SetObjectField(TEXT("system_config"), SystemConfig);
    ResponseData->SetStringField(TEXT("system_description"), TEXT("Rotas de clear otimizadas para jungle multicamada"));

    UE_LOG(LogTemp, Log, TEXT("Rotas de clear da jungle configuradas com sucesso"));
    return CreateSuccessResponse(ResponseData, TEXT("Rotas de clear da jungle configuradas com sucesso"));
}

// ========================================================================
// Funções Auxiliares
// ========================================================================

TSharedPtr<FJsonObject> FUnrealMCPJungleSystemCommands::CreateErrorResponse(const FString& ErrorMessage, const FString& ErrorCode)
{
    TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
    Response->SetStringField(TEXT("status"), RESPONSE_ERROR);
    Response->SetStringField(TEXT("message"), ErrorMessage);
    Response->SetStringField(TEXT("error_code"), ErrorCode);
    Response->SetStringField(TEXT("timestamp"), FDateTime::Now().ToString());

    UE_LOG(LogTemp, Error, TEXT("JungleSystem Error [%s]: %s"), *ErrorCode, *ErrorMessage);
    return Response;
}

TSharedPtr<FJsonObject> FUnrealMCPJungleSystemCommands::CreateSuccessResponse(const TSharedPtr<FJsonObject>& Data, const FString& Message)
{
    TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
    Response->SetStringField(TEXT("status"), RESPONSE_SUCCESS);
    Response->SetStringField(TEXT("message"), Message);
    Response->SetObjectField(TEXT("data"), Data);
    Response->SetStringField(TEXT("timestamp"), FDateTime::Now().ToString());

    return Response;
}

bool FUnrealMCPJungleSystemCommands::ValidateJungleConfig(const TSharedPtr<FJsonObject>& JungleConfig, FString& ErrorMessage)
{
    if (!JungleConfig.IsValid())
    {
        ErrorMessage = TEXT("Configuração de jungle inválida");
        return false;
    }

    // Validações básicas podem ser adicionadas aqui
    return true;
}
