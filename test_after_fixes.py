import sys
sys.path.append('Python')
from unreal_mcp_server import get_unreal_connection
import json

print('🔧 TESTANDO APÓS CORREÇÕES DE ENCODING')
print('=' * 50)

conn = get_unreal_connection()
if not conn:
    print('❌ ERRO: Unreal Engine não está rodando')
    exit(1)

print('✅ Conexão TCP estabelecida!')
print()

# Testar comandos que estavam retornando 0 bytes
problem_commands = [
    ('get_engine_version', {}),
    ('list_available_commands', {}),
    ('get_world_info', {}),
    ('get_level_info', {}),
    ('spawn_actor', {'type': 'StaticMeshActor', 'name': 'TestActor_Fix_001', 'location': [400, 0, 100]}),
]

print('🧪 Testando comandos que estavam com problema de 0 bytes...')
print()

successful = 0
failed = 0

for cmd_name, cmd_params in problem_commands:
    print(f'🎯 {cmd_name}...', end=' ')
    try:
        response = conn.send_command(cmd_name, cmd_params)
        if response and response.get('status') == 'success':
            print('✅ SUCESSO!')
            successful += 1
            
            # Mostrar alguns detalhes
            result = response.get('result', {})
            if cmd_name == 'get_engine_version':
                version = result.get('version', 'Unknown')
                print(f'    📊 Versão: {version}')
            elif cmd_name == 'list_available_commands':
                commands = result.get('commands', [])
                print(f'    📊 {len(commands)} comandos disponíveis')
            elif cmd_name == 'get_world_info':
                world_name = result.get('world_name', 'Unknown')
                world_type = result.get('world_type', 'Unknown')
                print(f'    📊 Mundo: {world_name} ({world_type})')
            elif cmd_name == 'get_level_info':
                level_name = result.get('level_name', 'Unknown')
                actor_count = result.get('actor_count', 0)
                print(f'    📊 Nível: {level_name} com {actor_count} atores')
            elif cmd_name == 'spawn_actor':
                actor_name = result.get('name', 'Unknown')
                print(f'    📊 Actor criado: {actor_name}')
        else:
            print('❌ ERRO')
            failed += 1
            if response:
                error = response.get('error', 'Unknown error')
                print(f'    ⚠️  {error}')
            else:
                print('    ⚠️  Nenhuma resposta (timeout)')
    except Exception as e:
        print('❌ EXCEPTION')
        failed += 1
        print(f'    ⚠️  {str(e)[:60]}...')
    print()

print('=' * 50)
print(f'📊 RESULTADO FINAL:')
print(f'✅ Sucessos: {successful}/{len(problem_commands)}')
print(f'❌ Falhas: {failed}/{len(problem_commands)}')

success_rate = (successful / len(problem_commands)) * 100
print(f'📈 Taxa de sucesso: {success_rate:.1f}%')

if success_rate == 100:
    print('🎉 PERFEITO! Todas as correções funcionaram!')
elif success_rate >= 80:
    print('👍 MUITO BOM! Maioria das correções funcionou!')
elif success_rate >= 50:
    print('⚠️  PARCIAL: Algumas correções funcionaram')
else:
    print('🚨 PROBLEMA: Correções não resolveram os problemas')

print()
print('💡 NOTA: Para aplicar as correções de Blueprint, é necessário')
print('   recompilar o projeto (desabilite Live Coding primeiro)')
