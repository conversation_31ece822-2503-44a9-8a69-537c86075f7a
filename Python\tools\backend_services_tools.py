"""
Backend Services Tools for Unreal Engine MCP Server

This module provides comprehensive Backend Services tools that are 100% compatible with the 
C++ implementations in UnrealMCPBackendServicesCommands.cpp. Based on UE 5.6 official documentation.
"""

import logging
import json
from typing import Dict, List, Any, Optional
from mcp.server.fastmcp import FastMCP

# Set up logging
logger = logging.getLogger("UnrealMCP")

def register_backend_services_tools(mcp: FastMCP):
    """Register Backend Services tools with the MCP server - 100% compatible with C++ implementations."""

    @mcp.tool()
    def setup_firebase_integration(
        project_id: str,
        api_key: str = "",
        database_url: str = "",
        enable_firestore: bool = True,
        enable_realtime_db: bool = True,
        enable_auth: bool = True,
        enable_analytics: bool = True
    ) -> Dict[str, Any]:
        """
        Setup Firebase integration for backend services.
        Compatible with HandleSetupFirebaseIntegration in C++.
        
        Args:
            project_id: Firebase project ID
            api_key: Firebase API key
            database_url: Firebase Realtime Database URL
            enable_firestore: Enable Firestore database
            enable_realtime_db: Enable Realtime Database
            enable_auth: Enable Firebase Authentication
            enable_analytics: Enable Firebase Analytics
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "project_id": project_id,
                "api_key": api_key,
                "database_url": database_url,
                "enable_firestore": enable_firestore,
                "enable_realtime_db": enable_realtime_db,
                "enable_auth": enable_auth,
                "enable_analytics": enable_analytics
            }

            response = unreal.send_command("setup_firebase_integration", params)
            return response or {"success": True, "message": "Firebase integration setup successfully"}

        except Exception as e:
            logger.error(f"Error setting up Firebase integration: {e}")
            return {"success": False, "message": f"Error setting up Firebase integration: {e}"}

    # configure_epic_online_services moved to cloud_services_tools.py to avoid duplication

    @mcp.tool()
    def setup_aws_gamelift(
        region: str = "us-east-1",
        fleet_id: str = "",
        alias_id: str = "",
        enable_auto_scaling: bool = True,
        min_instances: int = 1,
        max_instances: int = 10,
        target_capacity: int = 70
    ) -> Dict[str, Any]:
        """
        Setup AWS GameLift for server hosting.
        Compatible with HandleSetupAWSGameLift in C++.
        
        Args:
            region: AWS region for GameLift
            fleet_id: GameLift fleet ID
            alias_id: GameLift alias ID
            enable_auto_scaling: Enable auto-scaling
            min_instances: Minimum number of instances
            max_instances: Maximum number of instances
            target_capacity: Target capacity percentage
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "region": region,
                "fleet_id": fleet_id,
                "alias_id": alias_id,
                "enable_auto_scaling": enable_auto_scaling,
                "min_instances": min_instances,
                "max_instances": max_instances,
                "target_capacity": target_capacity
            }

            response = unreal.send_command("setup_aws_gamelift", params)
            return response or {"success": True, "message": "AWS GameLift setup successfully"}

        except Exception as e:
            logger.error(f"Error setting up AWS GameLift: {e}")
            return {"success": False, "message": f"Error setting up AWS GameLift: {e}"}

    @mcp.tool()
    def configure_cloudflare_services(
        zone_id: str,
        api_token: str = "",
        enable_cdn: bool = True,
        enable_ddos_protection: bool = True,
        enable_ssl: bool = True,
        cache_level: str = "Standard",
        security_level: str = "Medium"
    ) -> Dict[str, Any]:
        """
        Configure CloudFlare services for CDN and DDoS protection.
        Compatible with HandleConfigureCloudFlareServices in C++.
        
        Args:
            zone_id: CloudFlare Zone ID
            api_token: CloudFlare API token
            enable_cdn: Enable CDN services
            enable_ddos_protection: Enable DDoS protection
            enable_ssl: Enable SSL/TLS encryption
            cache_level: Cache level (Off, Basic, Standard, Aggressive)
            security_level: Security level (Off, Low, Medium, High, I'm Under Attack)
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "zone_id": zone_id,
                "api_token": api_token,
                "enable_cdn": enable_cdn,
                "enable_ddos_protection": enable_ddos_protection,
                "enable_ssl": enable_ssl,
                "cache_level": cache_level,
                "security_level": security_level
            }

            response = unreal.send_command("configure_cloudflare_services", params)
            return response or {"success": True, "message": "CloudFlare services configured successfully"}

        except Exception as e:
            logger.error(f"Error configuring CloudFlare services: {e}")
            return {"success": False, "message": f"Error configuring CloudFlare services: {e}"}

    @mcp.tool()
    def manage_cloud_saves(
        action: str = "sync",
        user_id: str = "",
        save_data: Dict[str, Any] = None,
        compression: bool = True,
        encryption: bool = True
    ) -> Dict[str, Any]:
        """
        Manage cloud save functionality.
        Compatible with HandleManageCloudSaves in C++.
        
        Args:
            action: Action to perform (sync, upload, download, delete)
            user_id: User ID for save data
            save_data: Save data to upload
            compression: Enable compression
            encryption: Enable encryption
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "action": action,
                "user_id": user_id,
                "save_data": save_data or {},
                "compression": compression,
                "encryption": encryption
            }

            response = unreal.send_command("manage_cloud_saves", params)
            return response or {"success": True, "message": f"Cloud save {action} completed successfully"}

        except Exception as e:
            logger.error(f"Error managing cloud saves: {e}")
            return {"success": False, "message": f"Error managing cloud saves: {e}"}

    # setup_matchmaking_service moved to cloud_services_tools.py to avoid duplication

    @mcp.tool()
    def manage_user_authentication(
        auth_provider: str = "EOS",
        user_credentials: Dict[str, str] = None,
        enable_auto_login: bool = True,
        enable_guest_login: bool = False,
        session_timeout: int = 3600
    ) -> Dict[str, Any]:
        """
        Manage user authentication across different providers.
        Compatible with HandleManageUserAuthentication in C++.

        Args:
            auth_provider: Authentication provider (EOS, Steam, Firebase, Custom)
            user_credentials: User credentials dictionary
            enable_auto_login: Enable automatic login
            enable_guest_login: Enable guest login
            session_timeout: Session timeout in seconds
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "auth_provider": auth_provider,
                "user_credentials": user_credentials or {},
                "enable_auto_login": enable_auto_login,
                "enable_guest_login": enable_guest_login,
                "session_timeout": session_timeout
            }

            response = unreal.send_command("manage_user_authentication", params)
            return response or {"success": True, "message": "User authentication managed successfully"}

        except Exception as e:
            logger.error(f"Error managing user authentication: {e}")
            return {"success": False, "message": f"Error managing user authentication: {e}"}

    @mcp.tool()
    def configure_social_features(
        enable_friends_list: bool = True,
        enable_presence: bool = True,
        enable_voice_chat: bool = True,
        enable_text_chat: bool = True,
        enable_party_system: bool = True,
        max_friends: int = 100,
        max_party_size: int = 4
    ) -> Dict[str, Any]:
        """
        Configure social features and systems.
        Compatible with HandleConfigureSocialFeatures in C++.

        Args:
            enable_friends_list: Enable friends list functionality
            enable_presence: Enable presence status
            enable_voice_chat: Enable voice chat
            enable_text_chat: Enable text chat
            enable_party_system: Enable party/group system
            max_friends: Maximum number of friends
            max_party_size: Maximum party size
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "enable_friends_list": enable_friends_list,
                "enable_presence": enable_presence,
                "enable_voice_chat": enable_voice_chat,
                "enable_text_chat": enable_text_chat,
                "enable_party_system": enable_party_system,
                "max_friends": max_friends,
                "max_party_size": max_party_size
            }

            response = unreal.send_command("configure_social_features", params)
            return response or {"success": True, "message": "Social features configured successfully"}

        except Exception as e:
            logger.error(f"Error configuring social features: {e}")
            return {"success": False, "message": f"Error configuring social features: {e}"}

    @mcp.tool()
    def setup_leaderboards_system(
        leaderboard_name: str,
        stat_name: str,
        sort_method: str = "descending",
        update_type: str = "best",
        enable_friends_only: bool = False,
        reset_frequency: str = "never"
    ) -> Dict[str, Any]:
        """
        Setup leaderboards system configuration.
        Compatible with HandleSetupLeaderboardsSystem in C++.

        Args:
            leaderboard_name: Name of the leaderboard
            stat_name: Statistic name to track
            sort_method: Sort method (ascending, descending)
            update_type: Update type (best, latest, sum)
            enable_friends_only: Enable friends-only leaderboard
            reset_frequency: Reset frequency (never, daily, weekly, monthly)
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "leaderboard_name": leaderboard_name,
                "stat_name": stat_name,
                "sort_method": sort_method,
                "update_type": update_type,
                "enable_friends_only": enable_friends_only,
                "reset_frequency": reset_frequency
            }

            response = unreal.send_command("setup_leaderboards_system", params)
            return response or {"success": True, "message": "Leaderboards system setup successfully"}

        except Exception as e:
            logger.error(f"Error setting up leaderboards system: {e}")
            return {"success": False, "message": f"Error setting up leaderboards system: {e}"}

    @mcp.tool()
    def manage_achievements_system(
        achievement_id: str = "",
        action: str = "unlock",
        progress_value: float = 0.0,
        enable_notifications: bool = True,
        enable_rewards: bool = True
    ) -> Dict[str, Any]:
        """
        Manage achievements system operations.
        Compatible with HandleManageAchievementsSystem in C++.

        Args:
            achievement_id: Achievement ID to manage
            action: Action to perform (unlock, progress, reset, query)
            progress_value: Progress value for incremental achievements
            enable_notifications: Enable achievement notifications
            enable_rewards: Enable achievement rewards
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "achievement_id": achievement_id,
                "action": action,
                "progress_value": progress_value,
                "enable_notifications": enable_notifications,
                "enable_rewards": enable_rewards
            }

            response = unreal.send_command("manage_achievements_system", params)
            return response or {"success": True, "message": f"Achievement {action} completed successfully"}

        except Exception as e:
            logger.error(f"Error managing achievements system: {e}")
            return {"success": False, "message": f"Error managing achievements system: {e}"}

    @mcp.tool()
    def analyze_backend_performance(
        service_type: str = "all",
        metrics_to_collect: List[str] = None,
        time_range: str = "1h",
        enable_real_time: bool = True
    ) -> Dict[str, Any]:
        """
        Analyze backend services performance and metrics.
        Compatible with HandleAnalyzeBackendPerformance in C++.

        Args:
            service_type: Service type to analyze (all, firebase, eos, aws, cloudflare)
            metrics_to_collect: List of metrics to collect
            time_range: Time range for analysis (1h, 24h, 7d, 30d)
            enable_real_time: Enable real-time monitoring
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "service_type": service_type,
                "metrics_to_collect": metrics_to_collect or ["latency", "throughput", "errors", "availability"],
                "time_range": time_range,
                "enable_real_time": enable_real_time
            }

            response = unreal.send_command("analyze_backend_performance", params)
            return response or {"success": True, "message": "Backend performance analysis completed successfully"}

        except Exception as e:
            logger.error(f"Error analyzing backend performance: {e}")
            return {"success": False, "message": f"Error analyzing backend performance: {e}"}
