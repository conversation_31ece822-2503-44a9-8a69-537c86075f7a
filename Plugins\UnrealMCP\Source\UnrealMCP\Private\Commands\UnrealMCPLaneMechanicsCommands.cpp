// Copyright Epic Games, Inc. All Rights Reserved.

#include "Commands/UnrealMCPLaneMechanicsCommands.h"

// ========================================================================
// Constantes
// ========================================================================

// Tipos de resposta
const FString FUnrealMCPLaneMechanicsCommands::RESPONSE_SUCCESS = TEXT("success");
const FString FUnrealMCPLaneMechanicsCommands::RESPONSE_ERROR = TEXT("error");
const FString FUnrealMCPLaneMechanicsCommands::RESPONSE_WARNING = TEXT("warning");
const FString FUnrealMCPLaneMechanicsCommands::RESPONSE_INFO = TEXT("info");

// Nomes das lanes
const FString FUnrealMCPLaneMechanicsCommands::LANE_TOP = TEXT("top_lane");
const FString FUnrealMCPLaneMechanicsCommands::LANE_MID = TEXT("mid_lane");
const FString FUnrealMCPLaneMechanicsCommands::LANE_BOT = TEXT("bot_lane");

// Tipos de lane
const FString FUnrealMCPLaneMechanicsCommands::LANE_TYPE_SOLO = TEXT("solo_lane");
const FString FUnrealMCPLaneMechanicsCommands::LANE_TYPE_DUO = TEXT("duo_lane");

// ========================================================================
// Construtor e Destrutor
// ========================================================================

FUnrealMCPLaneMechanicsCommands::FUnrealMCPLaneMechanicsCommands()
    : bIsInitialized(false)
    , LastUpdateTime(FDateTime::Now())
{
    bIsInitialized = true;
    UE_LOG(LogTemp, Log, TEXT("FUnrealMCPLaneMechanicsCommands: Sistema de Mecânicas de Lane inicializado"));
}

FUnrealMCPLaneMechanicsCommands::~FUnrealMCPLaneMechanicsCommands()
{
    // Limpar caches
    LaneConfigCache.Empty();
    LaneStates.Empty();
    
    UE_LOG(LogTemp, Log, TEXT("FUnrealMCPLaneMechanicsCommands: Sistema de Mecânicas de Lane finalizado"));
}

// ========================================================================
// Método Principal de Comando
// ========================================================================

TSharedPtr<FJsonObject> FUnrealMCPLaneMechanicsCommands::HandleCommand(const FString& CommandType, const TSharedPtr<FJsonObject>& Params)
{
    if (CommandType == TEXT("create_top_lane_mechanics"))
    {
        return HandleCreateTopLaneMechanics(Params);
    }
    else if (CommandType == TEXT("create_mid_lane_mechanics"))
    {
        return HandleCreateMidLaneMechanics(Params);
    }
    else if (CommandType == TEXT("create_bot_lane_mechanics"))
    {
        return HandleCreateBotLaneMechanics(Params);
    }
    else if (CommandType == TEXT("setup_lane_phases_system"))
    {
        return HandleSetupLanePhasesSystem(Params);
    }
    else
    {
        return CreateErrorResponse(FString::Printf(TEXT("Comando não reconhecido: %s"), *CommandType), TEXT("UNKNOWN_COMMAND"));
    }
}

// ========================================================================
// Implementações dos Comandos
// ========================================================================

TSharedPtr<FJsonObject> FUnrealMCPLaneMechanicsCommands::HandleCreateTopLaneMechanics(const TSharedPtr<FJsonObject>& CommandData)
{
    if (!CommandData.IsValid())
    {
        return CreateErrorResponse(TEXT("Dados do comando inválidos"), TEXT("INVALID_COMMAND_DATA"));
    }
    
    UE_LOG(LogTemp, Log, TEXT("Criando mecânicas da Top Lane"));
    
    // Extrair configurações da Top Lane
    FString LaneName = LANE_TOP;
    FString LaneType = LANE_TYPE_SOLO;
    float LengthMultiplier = 1.2f;
    float IsolationLevel = 0.8f;
    float TeleportImportance = 0.9f;
    bool bFreezeZoneEnabled = true;
    int32 GankPathsCount = 3;
    int32 TpFlankPositions = 4;
    
    // Ler configurações do JSON se fornecidas
    CommandData->TryGetStringField(TEXT("lane_name"), LaneName);
    CommandData->TryGetStringField(TEXT("lane_type"), LaneType);
    CommandData->TryGetNumberField(TEXT("length_multiplier"), LengthMultiplier);
    CommandData->TryGetNumberField(TEXT("isolation_level"), IsolationLevel);
    CommandData->TryGetNumberField(TEXT("teleport_importance"), TeleportImportance);
    CommandData->TryGetBoolField(TEXT("freeze_zone_enabled"), bFreezeZoneEnabled);
    CommandData->TryGetNumberField(TEXT("gank_paths_count"), GankPathsCount);
    CommandData->TryGetNumberField(TEXT("tp_flank_positions"), TpFlankPositions);
    
    // Criar configuração da Top Lane
    TSharedPtr<FJsonObject> LaneConfig = MakeShared<FJsonObject>();
    LaneConfig->SetStringField(TEXT("lane_name"), LaneName);
    LaneConfig->SetStringField(TEXT("lane_type"), LaneType);
    LaneConfig->SetNumberField(TEXT("length_multiplier"), LengthMultiplier);
    LaneConfig->SetNumberField(TEXT("isolation_level"), IsolationLevel);
    LaneConfig->SetNumberField(TEXT("teleport_importance"), TeleportImportance);
    LaneConfig->SetBoolField(TEXT("freeze_zone_enabled"), bFreezeZoneEnabled);
    LaneConfig->SetNumberField(TEXT("gank_paths_count"), GankPathsCount);
    LaneConfig->SetNumberField(TEXT("tp_flank_positions"), TpFlankPositions);
    
    // Adicionar tipos de champions
    TArray<TSharedPtr<FJsonValue>> ChampionTypes;
    ChampionTypes.Add(MakeShared<FJsonValueString>(TEXT("Tanks")));
    ChampionTypes.Add(MakeShared<FJsonValueString>(TEXT("Bruisers")));
    ChampionTypes.Add(MakeShared<FJsonValueString>(TEXT("Split Pushers")));
    LaneConfig->SetArrayField(TEXT("champion_types"), ChampionTypes);
    
    // Adicionar objetivos próximos
    TArray<TSharedPtr<FJsonValue>> NearbyObjectives;
    NearbyObjectives.Add(MakeShared<FJsonValueString>(TEXT("Herald")));
    NearbyObjectives.Add(MakeShared<FJsonValueString>(TEXT("Scuttle Superior")));
    LaneConfig->SetArrayField(TEXT("nearby_objectives"), NearbyObjectives);
    
    // Adicionar mecânicas especiais
    TArray<TSharedPtr<FJsonValue>> SpecialMechanics;
    SpecialMechanics.Add(MakeShared<FJsonValueString>(TEXT("extended_trades_favored")));
    SpecialMechanics.Add(MakeShared<FJsonValueString>(TEXT("1v1_focus")));
    SpecialMechanics.Add(MakeShared<FJsonValueString>(TEXT("teleport_crucial")));
    if (bFreezeZoneEnabled)
    {
        SpecialMechanics.Add(MakeShared<FJsonValueString>(TEXT("freeze_zone_near_tower")));
    }
    SpecialMechanics.Add(MakeShared<FJsonValueString>(TEXT("multiple_gank_paths")));
    SpecialMechanics.Add(MakeShared<FJsonValueString>(TEXT("strategic_tp_flanks")));
    LaneConfig->SetArrayField(TEXT("special_mechanics"), SpecialMechanics);
    
    // Salvar configuração no cache
    LaneConfigCache.Add(LaneName, LaneConfig);
    
    // Criar estado da lane
    TSharedPtr<FJsonObject> LaneState = MakeShared<FJsonObject>();
    LaneState->SetStringField(TEXT("status"), TEXT("created"));
    LaneState->SetStringField(TEXT("creation_time"), FDateTime::Now().ToString());
    LaneState->SetBoolField(TEXT("active"), true);
    LaneStates.Add(LaneName, LaneState);
    
    // Criar resposta de sucesso
    TSharedPtr<FJsonObject> ResponseData = MakeShared<FJsonObject>();
    ResponseData->SetObjectField(TEXT("lane_config"), LaneConfig);
    ResponseData->SetObjectField(TEXT("lane_state"), LaneState);
    ResponseData->SetStringField(TEXT("lane_description"), TEXT("Lane isolada com foco em 1v1 e teleport crucial"));
    
    UE_LOG(LogTemp, Log, TEXT("Mecânicas da Top Lane criadas com sucesso"));
    return CreateSuccessResponse(ResponseData, TEXT("Mecânicas da Top Lane criadas com sucesso"));
}

TSharedPtr<FJsonObject> FUnrealMCPLaneMechanicsCommands::HandleCreateMidLaneMechanics(const TSharedPtr<FJsonObject>& CommandData)
{
    if (!CommandData.IsValid())
    {
        return CreateErrorResponse(TEXT("Dados do comando inválidos"), TEXT("INVALID_COMMAND_DATA"));
    }
    
    UE_LOG(LogTemp, Log, TEXT("Criando mecânicas da Mid Lane"));
    
    // Extrair configurações da Mid Lane
    FString LaneName = LANE_MID;
    FString LaneType = LANE_TYPE_SOLO;
    float LengthMultiplier = 0.8f;
    float CentralityFactor = 1.0f;
    float RoamAccessibility = 0.9f;
    float RiverControlImportance = 0.8f;
    float WaveClearPriority = 0.9f;
    
    // Ler configurações do JSON se fornecidas
    CommandData->TryGetStringField(TEXT("lane_name"), LaneName);
    CommandData->TryGetStringField(TEXT("lane_type"), LaneType);
    CommandData->TryGetNumberField(TEXT("length_multiplier"), LengthMultiplier);
    CommandData->TryGetNumberField(TEXT("centrality_factor"), CentralityFactor);
    CommandData->TryGetNumberField(TEXT("roam_accessibility"), RoamAccessibility);
    CommandData->TryGetNumberField(TEXT("river_control_importance"), RiverControlImportance);
    CommandData->TryGetNumberField(TEXT("wave_clear_priority"), WaveClearPriority);
    
    // Criar configuração da Mid Lane
    TSharedPtr<FJsonObject> LaneConfig = MakeShared<FJsonObject>();
    LaneConfig->SetStringField(TEXT("lane_name"), LaneName);
    LaneConfig->SetStringField(TEXT("lane_type"), LaneType);
    LaneConfig->SetNumberField(TEXT("length_multiplier"), LengthMultiplier);
    LaneConfig->SetNumberField(TEXT("centrality_factor"), CentralityFactor);
    LaneConfig->SetNumberField(TEXT("roam_accessibility"), RoamAccessibility);
    LaneConfig->SetNumberField(TEXT("river_control_importance"), RiverControlImportance);
    LaneConfig->SetNumberField(TEXT("wave_clear_priority"), WaveClearPriority);
    
    // Adicionar tipos de champions
    TArray<TSharedPtr<FJsonValue>> ChampionTypes;
    ChampionTypes.Add(MakeShared<FJsonValueString>(TEXT("Mages")));
    ChampionTypes.Add(MakeShared<FJsonValueString>(TEXT("Assassinos")));
    ChampionTypes.Add(MakeShared<FJsonValueString>(TEXT("Roamers")));
    LaneConfig->SetArrayField(TEXT("champion_types"), ChampionTypes);
    
    // Adicionar objetivos próximos
    TArray<TSharedPtr<FJsonValue>> NearbyObjectives;
    NearbyObjectives.Add(MakeShared<FJsonValueString>(TEXT("Ambos Scuttles")));
    NearbyObjectives.Add(MakeShared<FJsonValueString>(TEXT("Controle de Rio")));
    LaneConfig->SetArrayField(TEXT("nearby_objectives"), NearbyObjectives);
    
    // Adicionar mecânicas especiais
    TArray<TSharedPtr<FJsonValue>> SpecialMechanics;
    SpecialMechanics.Add(MakeShared<FJsonValueString>(TEXT("shortest_lane_fast_trades")));
    SpecialMechanics.Add(MakeShared<FJsonValueString>(TEXT("central_map_access")));
    SpecialMechanics.Add(MakeShared<FJsonValueString>(TEXT("roam_windows")));
    SpecialMechanics.Add(MakeShared<FJsonValueString>(TEXT("river_control")));
    SpecialMechanics.Add(MakeShared<FJsonValueString>(TEXT("wave_clear_priority")));
    SpecialMechanics.Add(MakeShared<FJsonValueString>(TEXT("both_sides_access")));
    LaneConfig->SetArrayField(TEXT("special_mechanics"), SpecialMechanics);
    
    // Salvar configuração no cache
    LaneConfigCache.Add(LaneName, LaneConfig);
    
    // Criar estado da lane
    TSharedPtr<FJsonObject> LaneState = MakeShared<FJsonObject>();
    LaneState->SetStringField(TEXT("status"), TEXT("created"));
    LaneState->SetStringField(TEXT("creation_time"), FDateTime::Now().ToString());
    LaneState->SetBoolField(TEXT("active"), true);
    LaneStates.Add(LaneName, LaneState);
    
    // Criar resposta de sucesso
    TSharedPtr<FJsonObject> ResponseData = MakeShared<FJsonObject>();
    ResponseData->SetObjectField(TEXT("lane_config"), LaneConfig);
    ResponseData->SetObjectField(TEXT("lane_state"), LaneState);
    ResponseData->SetStringField(TEXT("lane_description"), TEXT("Lane central com foco em roaming e controle de rio"));
    
    UE_LOG(LogTemp, Log, TEXT("Mecânicas da Mid Lane criadas com sucesso"));
    return CreateSuccessResponse(ResponseData, TEXT("Mecânicas da Mid Lane criadas com sucesso"));
}

TSharedPtr<FJsonObject> FUnrealMCPLaneMechanicsCommands::HandleCreateBotLaneMechanics(const TSharedPtr<FJsonObject>& CommandData)
{
    if (!CommandData.IsValid())
    {
        return CreateErrorResponse(TEXT("Dados do comando inválidos"), TEXT("INVALID_COMMAND_DATA"));
    }

    UE_LOG(LogTemp, Log, TEXT("Criando mecânicas da Bot Lane"));

    // Extrair configurações da Bot Lane
    FString LaneName = LANE_BOT;
    FString LaneType = LANE_TYPE_DUO;
    float LengthMultiplier = 1.0f;
    float DuoLaneSynergy = 0.9f;
    float DragonControlImportance = 0.8f;
    float AllInPotential = 0.7f;

    // Ler configurações do JSON se fornecidas
    CommandData->TryGetStringField(TEXT("lane_name"), LaneName);
    CommandData->TryGetStringField(TEXT("lane_type"), LaneType);
    CommandData->TryGetNumberField(TEXT("length_multiplier"), LengthMultiplier);
    CommandData->TryGetNumberField(TEXT("duo_lane_synergy"), DuoLaneSynergy);
    CommandData->TryGetNumberField(TEXT("dragon_control_importance"), DragonControlImportance);
    CommandData->TryGetNumberField(TEXT("all_in_potential"), AllInPotential);

    // Criar configuração da Bot Lane
    TSharedPtr<FJsonObject> LaneConfig = MakeShared<FJsonObject>();
    LaneConfig->SetStringField(TEXT("lane_name"), LaneName);
    LaneConfig->SetStringField(TEXT("lane_type"), LaneType);
    LaneConfig->SetNumberField(TEXT("length_multiplier"), LengthMultiplier);
    LaneConfig->SetNumberField(TEXT("duo_lane_synergy"), DuoLaneSynergy);
    LaneConfig->SetNumberField(TEXT("dragon_control_importance"), DragonControlImportance);
    LaneConfig->SetNumberField(TEXT("all_in_potential"), AllInPotential);

    // Adicionar tipos de champions
    TArray<TSharedPtr<FJsonValue>> ChampionTypes;
    ChampionTypes.Add(MakeShared<FJsonValueString>(TEXT("ADC")));
    ChampionTypes.Add(MakeShared<FJsonValueString>(TEXT("Support")));
    LaneConfig->SetArrayField(TEXT("champion_types"), ChampionTypes);

    // Adicionar objetivos próximos
    TArray<TSharedPtr<FJsonValue>> NearbyObjectives;
    NearbyObjectives.Add(MakeShared<FJsonValueString>(TEXT("Dragão")));
    NearbyObjectives.Add(MakeShared<FJsonValueString>(TEXT("Scuttle Inferior")));
    LaneConfig->SetArrayField(TEXT("nearby_objectives"), NearbyObjectives);

    // Adicionar mecânicas especiais
    TArray<TSharedPtr<FJsonValue>> SpecialMechanics;
    SpecialMechanics.Add(MakeShared<FJsonValueString>(TEXT("standard_length_2v2_focus")));
    SpecialMechanics.Add(MakeShared<FJsonValueString>(TEXT("duo_synergy_crucial")));
    SpecialMechanics.Add(MakeShared<FJsonValueString>(TEXT("all_in_windows")));
    SpecialMechanics.Add(MakeShared<FJsonValueString>(TEXT("dragon_control")));
    SpecialMechanics.Add(MakeShared<FJsonValueString>(TEXT("bot_lane_synergy_combos")));
    SpecialMechanics.Add(MakeShared<FJsonValueString>(TEXT("epic_objectives_access")));
    LaneConfig->SetArrayField(TEXT("special_mechanics"), SpecialMechanics);

    // Salvar configuração no cache
    LaneConfigCache.Add(LaneName, LaneConfig);

    // Criar estado da lane
    TSharedPtr<FJsonObject> LaneState = MakeShared<FJsonObject>();
    LaneState->SetStringField(TEXT("status"), TEXT("created"));
    LaneState->SetStringField(TEXT("creation_time"), FDateTime::Now().ToString());
    LaneState->SetBoolField(TEXT("active"), true);
    LaneStates.Add(LaneName, LaneState);

    // Criar resposta de sucesso
    TSharedPtr<FJsonObject> ResponseData = MakeShared<FJsonObject>();
    ResponseData->SetObjectField(TEXT("lane_config"), LaneConfig);
    ResponseData->SetObjectField(TEXT("lane_state"), LaneState);
    ResponseData->SetStringField(TEXT("lane_description"), TEXT("Lane duo com foco em sinergia e controle de dragão"));

    UE_LOG(LogTemp, Log, TEXT("Mecânicas da Bot Lane criadas com sucesso"));
    return CreateSuccessResponse(ResponseData, TEXT("Mecânicas da Bot Lane criadas com sucesso"));
}

TSharedPtr<FJsonObject> FUnrealMCPLaneMechanicsCommands::HandleSetupLanePhasesSystem(const TSharedPtr<FJsonObject>& CommandData)
{
    if (!CommandData.IsValid())
    {
        return CreateErrorResponse(TEXT("Dados do comando inválidos"), TEXT("INVALID_COMMAND_DATA"));
    }

    UE_LOG(LogTemp, Log, TEXT("Configurando sistema de fases de lane"));

    // Configurações padrão
    int32 EarlyLaningDuration = 10;
    int32 MidLaningDuration = 10;
    int32 LateLaningStart = 20;
    bool bEnablePhaseTransitions = true;

    // Ler configurações do JSON se fornecidas
    CommandData->TryGetNumberField(TEXT("early_laning_duration"), EarlyLaningDuration);
    CommandData->TryGetNumberField(TEXT("mid_laning_duration"), MidLaningDuration);
    CommandData->TryGetNumberField(TEXT("late_laning_start"), LateLaningStart);
    CommandData->TryGetBoolField(TEXT("enable_phase_transitions"), bEnablePhaseTransitions);

    TSharedPtr<FJsonObject> SystemConfig = MakeShared<FJsonObject>();
    SystemConfig->SetStringField(TEXT("system_name"), TEXT("lane_phases_system"));
    SystemConfig->SetNumberField(TEXT("early_laning_duration"), EarlyLaningDuration);
    SystemConfig->SetNumberField(TEXT("mid_laning_duration"), MidLaningDuration);
    SystemConfig->SetNumberField(TEXT("late_laning_start"), LateLaningStart);
    SystemConfig->SetBoolField(TEXT("enable_phase_transitions"), bEnablePhaseTransitions);

    // Configurar fases detalhadas
    TSharedPtr<FJsonObject> Phases = MakeShared<FJsonObject>();

    // Early Laning Phase
    TSharedPtr<FJsonObject> EarlyLaning = MakeShared<FJsonObject>();
    EarlyLaning->SetNumberField(TEXT("duration_minutes"), EarlyLaningDuration);
    TArray<TSharedPtr<FJsonValue>> EarlyObjectives;
    EarlyObjectives.Add(MakeShared<FJsonValueString>(TEXT("establish_cs_advantage")));
    EarlyObjectives.Add(MakeShared<FJsonValueString>(TEXT("control_wave_positioning")));
    EarlyObjectives.Add(MakeShared<FJsonValueString>(TEXT("avoid_ganks_while_pressuring")));
    EarlyObjectives.Add(MakeShared<FJsonValueString>(TEXT("establish_vision_control")));
    EarlyLaning->SetArrayField(TEXT("objectives"), EarlyObjectives);
    Phases->SetObjectField(TEXT("early_laning"), EarlyLaning);

    // Mid Laning Phase
    TSharedPtr<FJsonObject> MidLaning = MakeShared<FJsonObject>();
    MidLaning->SetNumberField(TEXT("duration_minutes"), MidLaningDuration);
    TArray<TSharedPtr<FJsonValue>> MidObjectives;
    MidObjectives.Add(MakeShared<FJsonValueString>(TEXT("transition_to_teamfights")));
    MidObjectives.Add(MakeShared<FJsonValueString>(TEXT("control_neutral_objectives")));
    MidObjectives.Add(MakeShared<FJsonValueString>(TEXT("roaming_and_map_pressure")));
    MidObjectives.Add(MakeShared<FJsonValueString>(TEXT("establish_item_advantages")));
    MidLaning->SetArrayField(TEXT("objectives"), MidObjectives);
    Phases->SetObjectField(TEXT("mid_laning"), MidLaning);

    // Late Laning Phase
    TSharedPtr<FJsonObject> LateLaning = MakeShared<FJsonObject>();
    LateLaning->SetNumberField(TEXT("start_minute"), LateLaningStart);
    TArray<TSharedPtr<FJsonValue>> LateObjectives;
    LateObjectives.Add(MakeShared<FJsonValueString>(TEXT("teamfight_positioning")));
    LateObjectives.Add(MakeShared<FJsonValueString>(TEXT("objective_control")));
    LateObjectives.Add(MakeShared<FJsonValueString>(TEXT("split_push_pressure")));
    LateObjectives.Add(MakeShared<FJsonValueString>(TEXT("end_game_execution")));
    LateLaning->SetArrayField(TEXT("objectives"), LateObjectives);
    Phases->SetObjectField(TEXT("late_laning"), LateLaning);

    SystemConfig->SetObjectField(TEXT("phases"), Phases);

    // Criar resposta de sucesso
    TSharedPtr<FJsonObject> ResponseData = MakeShared<FJsonObject>();
    ResponseData->SetObjectField(TEXT("system_config"), SystemConfig);
    ResponseData->SetStringField(TEXT("system_description"), TEXT("Sistema completo de fases de lane configurado"));

    UE_LOG(LogTemp, Log, TEXT("Sistema de fases de lane configurado com sucesso"));
    return CreateSuccessResponse(ResponseData, TEXT("Sistema de fases de lane configurado com sucesso"));
}

// ========================================================================
// Funções Auxiliares
// ========================================================================

TSharedPtr<FJsonObject> FUnrealMCPLaneMechanicsCommands::CreateErrorResponse(const FString& ErrorMessage, const FString& ErrorCode)
{
    TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
    Response->SetStringField(TEXT("status"), RESPONSE_ERROR);
    Response->SetStringField(TEXT("message"), ErrorMessage);
    Response->SetStringField(TEXT("error_code"), ErrorCode);
    Response->SetStringField(TEXT("timestamp"), FDateTime::Now().ToString());

    UE_LOG(LogTemp, Error, TEXT("LaneMechanics Error [%s]: %s"), *ErrorCode, *ErrorMessage);
    return Response;
}

TSharedPtr<FJsonObject> FUnrealMCPLaneMechanicsCommands::CreateSuccessResponse(const TSharedPtr<FJsonObject>& Data, const FString& Message)
{
    TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
    Response->SetStringField(TEXT("status"), RESPONSE_SUCCESS);
    Response->SetStringField(TEXT("message"), Message);
    Response->SetObjectField(TEXT("data"), Data);
    Response->SetStringField(TEXT("timestamp"), FDateTime::Now().ToString());

    return Response;
}

bool FUnrealMCPLaneMechanicsCommands::ValidateLaneConfig(const TSharedPtr<FJsonObject>& LaneConfig, FString& ErrorMessage)
{
    if (!LaneConfig.IsValid())
    {
        ErrorMessage = TEXT("Configuração de lane inválida");
        return false;
    }

    // Validações básicas podem ser adicionadas aqui
    return true;
}
