"""
Network Tools for Unreal MCP.

This module provides tools for creating and managing network systems in Unreal Engine.
"""

import logging
from typing import Dict, List, Any
from mcp.server.fastmcp import FastMC<PERSON>, Context

# Get logger
logger = logging.getLogger("UnrealMCP")

def register_network_tools(mcp: FastMCP):
    """Register Network tools with the MCP server."""
    
    @mcp.tool()
    def create_multilayer_network_system(
        ctx: Context,
        layer_configs: List[Dict[str, Any]],
        global_settings: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        Create a multilayer network system.
        
        Args:
            layer_configs: List of layer configuration dictionaries
            global_settings: Global settings for the network system
            
        Returns:
            Dict containing success status and system information
        """
        # Import inside function to avoid circular imports
        from unreal_mcp_server import get_unreal_connection
        
        if global_settings is None:
            global_settings = {}
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
                
            response = unreal.send_command("create_multilayer_network_system", {
                "layer_configs": layer_configs,
                "global_settings": global_settings
            })
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Multilayer network system creation response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error creating multilayer network system: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def configure_object_replication(
        ctx: Context,
        layer_name: str,
        replication_configs: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        Configure object replication for a network layer.
        
        Args:
            layer_name: Name of the target network layer
            replication_configs: List of replication configuration dictionaries
            
        Returns:
            Dict containing success status and configuration results
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            params = {
                "layer_name": layer_name,
                "replication_configs": replication_configs
            }
            
            logger.info(f"Configuring object replication with params: {params}")
            response = unreal.send_command("configure_object_replication", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Object replication configuration response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error configuring object replication: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def setup_client_prediction(
        ctx: Context,
        layer_name: str,
        prediction_configs: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        Setup client prediction system.
        
        Args:
            layer_name: Name of the target network layer
            prediction_configs: List of prediction configuration dictionaries
            
        Returns:
            Dict containing success status and prediction setup results
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            params = {
                "layer_name": layer_name,
                "prediction_configs": prediction_configs
            }
            
            logger.info(f"Setting up client prediction with params: {params}")
            response = unreal.send_command("setup_client_prediction", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Client prediction setup response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error setting up client prediction: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def configure_network_synchronization(
        ctx: Context,
        layer_name: str,
        sync_configs: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        Configure network synchronization.
        
        Args:
            layer_name: Name of the target network layer
            sync_configs: List of synchronization configuration dictionaries
            
        Returns:
            Dict containing success status and synchronization results
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            params = {
                "layer_name": layer_name,
                "sync_configs": sync_configs
            }
            
            logger.info(f"Configuring network synchronization with params: {params}")
            response = unreal.send_command("configure_network_synchronization", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Network synchronization configuration response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error configuring network synchronization: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def setup_lag_compensation(
        ctx: Context,
        layer_name: str,
        compensation_settings: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Setup lag compensation system.
        
        Args:
            layer_name: Name of the target network layer
            compensation_settings: Dictionary containing lag compensation settings
            
        Returns:
            Dict containing success status and lag compensation setup results
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            params = {
                "layer_name": layer_name,
                "compensation_settings": compensation_settings
            }
            
            logger.info(f"Setting up lag compensation with params: {params}")
            response = unreal.send_command("setup_lag_compensation", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Lag compensation setup response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error setting up lag compensation: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def configure_bandwidth_optimization(
        ctx: Context,
        layer_name: str,
        optimization_configs: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        Configure bandwidth optimization.
        
        Args:
            layer_name: Name of the target network layer
            optimization_configs: List of bandwidth optimization configuration dictionaries
            
        Returns:
            Dict containing success status and optimization results
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            params = {
                "layer_name": layer_name,
                "optimization_configs": optimization_configs
            }
            
            logger.info(f"Configuring bandwidth optimization with params: {params}")
            response = unreal.send_command("configure_bandwidth_optimization", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Bandwidth optimization configuration response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error configuring bandwidth optimization: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def debug_network_performance(
        ctx: Context,
        layer_name: str,
        debug_configs: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        Debug network performance.
        
        Args:
            layer_name: Name of the target network layer
            debug_configs: List of debug configuration dictionaries
            
        Returns:
            Dict containing success status and performance debug results
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            params = {
                "layer_name": layer_name,
                "debug_configs": debug_configs
            }
            
            logger.info(f"Debugging network performance with params: {params}")
            response = unreal.send_command("debug_network_performance", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Network performance debug response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error debugging network performance: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def validate_network_setup(
        ctx: Context,
        layer_name: str,
        validation_configs: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        Validate network setup.
        
        Args:
            layer_name: Name of the target network layer
            
        Returns:
            Dict containing success status and validation results
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            params = {
                "layer_name": layer_name,
                "validation_configs": validation_configs
            }
            
            logger.info(f"Validating network setup for layer: {layer_name}")
            response = unreal.send_command("validate_network_setup", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Network setup validation response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error validating network setup: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def get_network_system_status(
        ctx: Context,
        layer_name: str
    ) -> Dict[str, Any]:
        """
        Get network system status.
        
        Args:
            layer_name: Name of the target network layer
            
        Returns:
            Dict containing success status and system status information
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            params = {
                "layer_name": layer_name
            }
            
            logger.info(f"Getting network system status for layer: {layer_name}")
            response = unreal.send_command("get_network_system_status", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Network system status response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error getting network system status: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    logger.info("Network tools registered successfully")