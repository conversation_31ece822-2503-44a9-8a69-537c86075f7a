"""
Analytics & Telemetry Tools for Unreal Engine MCP Server

This module provides comprehensive Analytics & Telemetry tools that are 100% compatible with the 
C++ implementations in UnrealMCPAnalyticsTelemetryCommands.cpp. Based on UE 5.6 official documentation.
"""

import logging
import json
from typing import Dict, List, Any, Optional
from mcp.server.fastmcp import FastMCP

# Set up logging
logger = logging.getLogger("UnrealMCP")

def register_analytics_telemetry_tools(mcp: FastMCP):
    """Register Analytics & Telemetry tools with the MCP server - 100% compatible with C++ implementations."""

    @mcp.tool()
    def setup_gameanalytics_integration(
        game_key: str,
        secret_key: str,
        build_version: str = "1.0.0",
        enable_debug_mode: bool = False,
        enable_automatic_events: bool = True,
        custom_dimensions: List[str] = None
    ) -> Dict[str, Any]:
        """
        Setup GameAnalytics integration for comprehensive game analytics.
        Compatible with HandleSetupGameAnalyticsIntegration in C++.
        
        Args:
            game_key: GameAnalytics game key
            secret_key: GameAnalytics secret key
            build_version: Game build version
            enable_debug_mode: Enable debug mode for testing
            enable_automatic_events: Enable automatic event tracking
            custom_dimensions: List of custom dimensions
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "game_key": game_key,
                "secret_key": secret_key,
                "build_version": build_version,
                "enable_debug_mode": enable_debug_mode,
                "enable_automatic_events": enable_automatic_events,
                "custom_dimensions": custom_dimensions or []
            }

            response = unreal.send_command("setup_gameanalytics_integration", params)
            return response or {"success": True, "message": "GameAnalytics integration setup successfully"}

        except Exception as e:
            logger.error(f"Error setting up GameAnalytics integration: {e}")
            return {"success": False, "message": f"Error setting up GameAnalytics integration: {e}"}

    @mcp.tool()
    def configure_crashlytics_system(
        api_key: str = "",
        enable_crash_reporting: bool = True,
        enable_performance_monitoring: bool = True,
        enable_custom_logs: bool = True,
        max_log_size: int = 1024,
        upload_on_wifi_only: bool = False
    ) -> Dict[str, Any]:
        """
        Configure Crashlytics system for crash reporting and performance monitoring.
        Compatible with HandleConfigureCrashlyticsSystem in C++.
        
        Args:
            api_key: Crashlytics API key
            enable_crash_reporting: Enable crash reporting
            enable_performance_monitoring: Enable performance monitoring
            enable_custom_logs: Enable custom log collection
            max_log_size: Maximum log size in KB
            upload_on_wifi_only: Upload only on WiFi connection
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "api_key": api_key,
                "enable_crash_reporting": enable_crash_reporting,
                "enable_performance_monitoring": enable_performance_monitoring,
                "enable_custom_logs": enable_custom_logs,
                "max_log_size": max_log_size,
                "upload_on_wifi_only": upload_on_wifi_only
            }

            response = unreal.send_command("configure_crashlytics_system", params)
            return response or {"success": True, "message": "Crashlytics system configured successfully"}

        except Exception as e:
            logger.error(f"Error configuring Crashlytics system: {e}")
            return {"success": False, "message": f"Error configuring Crashlytics system: {e}"}

    @mcp.tool()
    def setup_custom_metrics_system(
        metrics_endpoint: str = "",
        api_key: str = "",
        batch_size: int = 100,
        flush_interval_seconds: int = 30,
        enable_real_time_metrics: bool = True,
        compression_enabled: bool = True
    ) -> Dict[str, Any]:
        """
        Setup custom metrics system for detailed game analytics.
        Compatible with HandleSetupCustomMetricsSystem in C++.
        
        Args:
            metrics_endpoint: Custom metrics endpoint URL
            api_key: API key for metrics service
            batch_size: Batch size for metric uploads
            flush_interval_seconds: Flush interval in seconds
            enable_real_time_metrics: Enable real-time metrics
            compression_enabled: Enable data compression
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "metrics_endpoint": metrics_endpoint,
                "api_key": api_key,
                "batch_size": batch_size,
                "flush_interval_seconds": flush_interval_seconds,
                "enable_real_time_metrics": enable_real_time_metrics,
                "compression_enabled": compression_enabled
            }

            response = unreal.send_command("setup_custom_metrics_system", params)
            return response or {"success": True, "message": "Custom metrics system setup successfully"}

        except Exception as e:
            logger.error(f"Error setting up custom metrics system: {e}")
            return {"success": False, "message": f"Error setting up custom metrics system: {e}"}

    @mcp.tool()
    def configure_hardware_telemetry(
        enable_cpu_monitoring: bool = True,
        enable_gpu_monitoring: bool = True,
        enable_memory_monitoring: bool = True,
        enable_network_monitoring: bool = True,
        sampling_interval_seconds: int = 60,
        enable_detailed_profiling: bool = False
    ) -> Dict[str, Any]:
        """
        Configure hardware telemetry for performance monitoring.
        Compatible with HandleConfigureHardwareTelemetry in C++.

        Args:
            enable_cpu_monitoring: Enable CPU monitoring
            enable_gpu_monitoring: Enable GPU monitoring
            enable_memory_monitoring: Enable memory monitoring
            enable_network_monitoring: Enable network monitoring
            sampling_interval_seconds: Sampling interval in seconds
            enable_detailed_profiling: Enable detailed profiling
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "enable_cpu_monitoring": enable_cpu_monitoring,
                "enable_gpu_monitoring": enable_gpu_monitoring,
                "enable_memory_monitoring": enable_memory_monitoring,
                "enable_network_monitoring": enable_network_monitoring,
                "sampling_interval_seconds": sampling_interval_seconds,
                "enable_detailed_profiling": enable_detailed_profiling
            }

            response = unreal.send_command("configure_hardware_telemetry", params)
            return response or {"success": True, "message": "Hardware telemetry configured successfully"}

        except Exception as e:
            logger.error(f"Error configuring hardware telemetry: {e}")
            return {"success": False, "message": f"Error configuring hardware telemetry: {e}"}

    @mcp.tool()
    def analyze_retention_metrics(
        time_period: str = "30d",
        cohort_analysis: bool = True,
        segment_by_platform: bool = True,
        segment_by_region: bool = True,
        include_churn_analysis: bool = True,
        export_format: str = "json"
    ) -> Dict[str, Any]:
        """
        Analyze player retention metrics and patterns.
        Compatible with HandleAnalyzeRetentionMetrics in C++.
        
        Args:
            time_period: Time period for analysis (7d, 30d, 90d, 1y)
            cohort_analysis: Enable cohort analysis
            segment_by_platform: Segment analysis by platform
            segment_by_region: Segment analysis by region
            include_churn_analysis: Include churn analysis
            export_format: Export format (json, csv, xlsx)
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "time_period": time_period,
                "cohort_analysis": cohort_analysis,
                "segment_by_platform": segment_by_platform,
                "segment_by_region": segment_by_region,
                "include_churn_analysis": include_churn_analysis,
                "export_format": export_format
            }

            response = unreal.send_command("analyze_retention_metrics", params)
            return response or {"success": True, "message": "Retention metrics analysis completed successfully"}

        except Exception as e:
            logger.error(f"Error analyzing retention metrics: {e}")
            return {"success": False, "message": f"Error analyzing retention metrics: {e}"}

    @mcp.tool()
    def track_conversion_events(
        event_name: str,
        conversion_funnel: List[str] = None,
        value: float = 0.0,
        currency: str = "USD",
        custom_parameters: Dict[str, Any] = None,
        enable_attribution: bool = True
    ) -> Dict[str, Any]:
        """
        Track conversion events and funnel analysis.
        Compatible with HandleTrackConversionEvents in C++.
        
        Args:
            event_name: Name of the conversion event
            conversion_funnel: List of funnel steps
            value: Monetary value of conversion
            currency: Currency code (USD, EUR, etc.)
            custom_parameters: Custom event parameters
            enable_attribution: Enable attribution tracking
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "event_name": event_name,
                "conversion_funnel": conversion_funnel or [],
                "value": value,
                "currency": currency,
                "custom_parameters": custom_parameters or {},
                "enable_attribution": enable_attribution
            }

            response = unreal.send_command("track_conversion_events", params)
            return response or {"success": True, "message": "Conversion event tracked successfully"}

        except Exception as e:
            logger.error(f"Error tracking conversion events: {e}")
            return {"success": False, "message": f"Error tracking conversion events: {e}"}

    @mcp.tool()
    def setup_realtime_alerts_system(
        alert_types: List[str] = None,
        threshold_configs: Dict[str, float] = None,
        notification_channels: List[str] = None,
        escalation_rules: Dict[str, Any] = None,
        enable_smart_alerts: bool = True
    ) -> Dict[str, Any]:
        """
        Setup real-time alerts system for critical metrics monitoring.
        Compatible with HandleSetupRealtimeAlertsSystem in C++.

        Args:
            alert_types: Types of alerts to monitor
            threshold_configs: Threshold configurations for alerts
            notification_channels: Notification channels (email, slack, webhook)
            escalation_rules: Escalation rules configuration
            enable_smart_alerts: Enable smart alert filtering
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "alert_types": alert_types or ["crash_rate", "performance_degradation", "user_drop", "error_spike"],
                "threshold_configs": threshold_configs or {"crash_rate": 0.05, "performance_degradation": 0.2},
                "notification_channels": notification_channels or ["email"],
                "escalation_rules": escalation_rules or {},
                "enable_smart_alerts": enable_smart_alerts
            }

            response = unreal.send_command("setup_realtime_alerts_system", params)
            return response or {"success": True, "message": "Real-time alerts system setup successfully"}

        except Exception as e:
            logger.error(f"Error setting up real-time alerts system: {e}")
            return {"success": False, "message": f"Error setting up real-time alerts system: {e}"}

    # monitor_performance_metrics moved to performance_tools.py to avoid duplication

    @mcp.tool()
    def generate_analytics_dashboard(
        dashboard_type: str = "comprehensive",
        time_range: str = "7d",
        include_real_time_data: bool = True,
        custom_widgets: List[str] = None,
        export_options: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        Generate comprehensive analytics dashboard.
        Compatible with HandleGenerateAnalyticsDashboard in C++.

        Args:
            dashboard_type: Type of dashboard (summary, detailed, comprehensive, custom)
            time_range: Time range for dashboard data
            include_real_time_data: Include real-time data streams
            custom_widgets: List of custom widgets to include
            export_options: Export options configuration
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "dashboard_type": dashboard_type,
                "time_range": time_range,
                "include_real_time_data": include_real_time_data,
                "custom_widgets": custom_widgets or [],
                "export_options": export_options or {"format": "html", "include_data": True}
            }

            response = unreal.send_command("generate_analytics_dashboard", params)
            return response or {"success": True, "message": "Analytics dashboard generated successfully"}

        except Exception as e:
            logger.error(f"Error generating analytics dashboard: {e}")
            return {"success": False, "message": f"Error generating analytics dashboard: {e}"}

    @mcp.tool()
    def configure_data_pipeline(
        pipeline_name: str,
        data_sources: List[str] = None,
        transformations: List[Dict[str, Any]] = None,
        destinations: List[str] = None,
        schedule: str = "real-time",
        enable_data_validation: bool = True
    ) -> Dict[str, Any]:
        """
        Configure analytics data pipeline for automated data processing.
        Compatible with HandleConfigureDataPipeline in C++.

        Args:
            pipeline_name: Name of the data pipeline
            data_sources: List of data sources
            transformations: List of data transformations
            destinations: List of data destinations
            schedule: Pipeline schedule (real-time, hourly, daily)
            enable_data_validation: Enable data validation
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "pipeline_name": pipeline_name,
                "data_sources": data_sources or ["game_events", "performance_metrics", "user_actions"],
                "transformations": transformations or [],
                "destinations": destinations or ["analytics_db", "data_warehouse"],
                "schedule": schedule,
                "enable_data_validation": enable_data_validation
            }

            response = unreal.send_command("configure_data_pipeline", params)
            return response or {"success": True, "message": "Data pipeline configured successfully"}

        except Exception as e:
            logger.error(f"Error configuring data pipeline: {e}")
            return {"success": False, "message": f"Error configuring data pipeline: {e}"}

    @mcp.tool()
    def export_analytics_report(
        report_type: str = "comprehensive",
        time_period: str = "30d",
        include_charts: bool = True,
        include_raw_data: bool = False,
        format: str = "pdf",
        recipients: List[str] = None
    ) -> Dict[str, Any]:
        """
        Export comprehensive analytics report.
        Compatible with HandleExportAnalyticsReport in C++.

        Args:
            report_type: Type of report (summary, detailed, comprehensive, custom)
            time_period: Time period for report data
            include_charts: Include charts and visualizations
            include_raw_data: Include raw data tables
            format: Export format (pdf, html, xlsx, json)
            recipients: List of email recipients
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "report_type": report_type,
                "time_period": time_period,
                "include_charts": include_charts,
                "include_raw_data": include_raw_data,
                "format": format,
                "recipients": recipients or []
            }

            response = unreal.send_command("export_analytics_report", params)
            return response or {"success": True, "message": "Analytics report exported successfully"}

        except Exception as e:
            logger.error(f"Error exporting analytics report: {e}")
            return {"success": False, "message": f"Error exporting analytics report: {e}"}
