#pragma once

#include "CoreMinimal.h"
#include "GameFramework/GameModeBase.h"
#include "AuracronGameMode.generated.h"

UCLASS()
class AURACRON_API AAuracronGameMode : public AGameModeBase
{
    GENERATED_BODY()

public:
    AAuracronGameMode();

protected:
    virtual void BeginPlay() override;

    // Sistema de Três Camadas
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Auracron|Layers")
    bool bEnablePlanicieRadiante = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Auracron|Layers")
    bool bEnableFirmamentoZephyr = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Auracron|Layers")
    bool bEnableAbismoUmbral = true;

    // Sistema de Fases de Jogo
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Auracron|GamePhases")
    float EarlyGameDuration = 15.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Auracron|GamePhases")
    float MidGameDuration = 15.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Auracron|GamePhases")
    float LateGameStart = 30.0f;

public:
    // Funções Blueprint
    UFUNCTION(BlueprintCallable, Category = "Auracron|Layers")
    void InitializeMultilayerSystem();

    UFUNCTION(BlueprintCallable, Category = "Auracron|GamePhases")
    void TransitionToNextPhase();

    UFUNCTION(BlueprintPure, Category = "Auracron|GamePhases")
    FString GetCurrentGamePhase() const;

private:
    float GameStartTime;
    FString CurrentPhase;
};
