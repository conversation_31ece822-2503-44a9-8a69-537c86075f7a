#include "MultilayerManager.h"
#include "Components/StaticMeshComponent.h"
#include "Components/SceneComponent.h"
#include "Engine/StaticMesh.h"
#include "UObject/ConstructorHelpers.h"

AMultilayerManager::AMultilayerManager()
{
    PrimaryActorTick.bCanEverTick = true;

    // Criar componente raiz
    RootSceneComponent = CreateDefaultSubobject<USceneComponent>(TEXT("RootSceneComponent"));
    RootComponent = RootSceneComponent;

    // Criar mesh da Planície Radiante (Z: 0-2000)
    PlanicieFloorMesh = CreateDefaultSubobject<UStaticMeshComponent>(TEXT("PlanicieFloorMesh"));
    PlanicieFloorMesh->SetupAttachment(RootComponent);
    PlanicieFloorMesh->SetRelativeLocation(FVector(0, 0, 1000)); // Centro da camada

    // Criar mesh do Firmamento Zephyr (Z: 2000-4000)
    FirmamentoPlatformMesh = CreateDefaultSubobject<UStaticMeshComponent>(TEXT("FirmamentoPlatformMesh"));
    FirmamentoPlatformMesh->SetupAttachment(RootComponent);
    FirmamentoPlatformMesh->SetRelativeLocation(FVector(0, 0, 3000)); // Centro da camada

    // Criar mesh do Abismo Umbral (Z: 4000-6000)
    AbismoCeilingMesh = CreateDefaultSubobject<UStaticMeshComponent>(TEXT("AbismoCeilingMesh"));
    AbismoCeilingMesh->SetupAttachment(RootComponent);
    AbismoCeilingMesh->SetRelativeLocation(FVector(0, 0, 5000)); // Centro da camada

    // Tentar carregar mesh básica
    static ConstructorHelpers::FObjectFinder<UStaticMesh> CubeMeshAsset(TEXT("/Engine/BasicShapes/Cube"));
    if (CubeMeshAsset.Succeeded())
    {
        PlanicieFloorMesh->SetStaticMesh(CubeMeshAsset.Object);
        FirmamentoPlatformMesh->SetStaticMesh(CubeMeshAsset.Object);
        AbismoCeilingMesh->SetStaticMesh(CubeMeshAsset.Object);
    }
}

void AMultilayerManager::BeginPlay()
{
    Super::BeginPlay();
    
    InitializeLayers();
    
    UE_LOG(LogTemp, Warning, TEXT("AURACRON Multilayer Manager - Sistema de 3 Camadas Inicializado"));
}

void AMultilayerManager::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);
}

void AMultilayerManager::InitializeLayers()
{
    // Configurar tamanhos das camadas
    PlanicieFloorMesh->SetRelativeScale3D(PlanicieSize / 100.0f);
    FirmamentoPlatformMesh->SetRelativeScale3D(FirmamentoSize / 100.0f);
    AbismoCeilingMesh->SetRelativeScale3D(AbismoSize / 100.0f);

    UE_LOG(LogTemp, Log, TEXT("Planície Radiante inicializada - Tamanho: %s"), *PlanicieSize.ToString());
    UE_LOG(LogTemp, Log, TEXT("Firmamento Zephyr inicializado - Tamanho: %s"), *FirmamentoSize.ToString());
    UE_LOG(LogTemp, Log, TEXT("Abismo Umbral inicializado - Tamanho: %s"), *AbismoSize.ToString());
}

void AMultilayerManager::SetLayerVisibility(const FString& LayerName, bool bVisible)
{
    if (LayerName == TEXT("Planicie"))
    {
        PlanicieFloorMesh->SetVisibility(bVisible);
    }
    else if (LayerName == TEXT("Firmamento"))
    {
        FirmamentoPlatformMesh->SetVisibility(bVisible);
    }
    else if (LayerName == TEXT("Abismo"))
    {
        AbismoCeilingMesh->SetVisibility(bVisible);
    }
}

FVector AMultilayerManager::GetLayerPosition(const FString& LayerName) const
{
    if (LayerName == TEXT("Planicie"))
    {
        return FVector(0, 0, 1000);
    }
    else if (LayerName == TEXT("Firmamento"))
    {
        return FVector(0, 0, 3000);
    }
    else if (LayerName == TEXT("Abismo"))
    {
        return FVector(0, 0, 5000);
    }
    
    return FVector::ZeroVector;
}
