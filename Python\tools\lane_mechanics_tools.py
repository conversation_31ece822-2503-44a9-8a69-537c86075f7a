"""Lane Mechanics Tools for Unreal MCP.

This module provides tools for creating and managing lane-specific mechanics
for AURACRON's three-lane system (Top, Mid, Bot) with their unique characteristics.
"""

import logging
from typing import Dict, List, Any, Optional
from mcp.server.fastmcp import FastMCP, Context

# Get logger
logger = logging.getLogger("UnrealMCP")

def register_lane_mechanics_tools(mcp: FastMCP):
    """Register Lane Mechanics tools with the MCP server."""
    
    @mcp.tool()
    def create_top_lane_mechanics(
        ctx: Context,
        lane_config: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Create Top Lane mechanics with its unique characteristics.
        
        Args:
            ctx: MCP Context
            lane_config: Lane configuration as JSON object containing:
                - length_multiplier: Lane length multiplier compared to standard (default: 1.2)
                - isolation_level: Level of isolation from other lanes (default: 0.8)
                - teleport_importance: Importance of teleport for this lane (default: 0.9)
                - freeze_zone_enabled: Enable freeze zone near tower (default: True)
                - gank_paths_count: Number of gank paths through jungle (default: 3)
                - tp_flank_positions: Number of strategic teleport positions (default: 4)
                - champion_types: Typical champion types for this lane
        
        Returns:
            Dict[str, Any]: Operation result
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            # Default configuration for Top Lane
            default_config = {
                "lane_name": "top_lane",
                "lane_type": "solo_lane",
                "length_multiplier": 1.2,
                "isolation_level": 0.8,
                "teleport_importance": 0.9,
                "freeze_zone_enabled": True,
                "gank_paths_count": 3,
                "tp_flank_positions": 4,
                "champion_types": ["Tanks", "Bruisers", "Split Pushers"],
                "nearby_objectives": ["Herald", "Scuttle Superior"],
                "special_mechanics": [
                    "extended_trades_favored",
                    "1v1_focus",
                    "teleport_crucial",
                    "freeze_zone_near_tower",
                    "multiple_gank_paths",
                    "strategic_tp_flanks"
                ]
            }
            
            # Merge provided config with defaults
            if lane_config:
                default_config.update(lane_config)
            
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            logger.info(f"Creating Top Lane mechanics with config: {default_config}")
            response = unreal.send_command("create_top_lane_mechanics", default_config)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Top Lane mechanics creation response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error creating Top Lane mechanics: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def create_mid_lane_mechanics(
        ctx: Context,
        lane_config: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Create Mid Lane mechanics with its unique characteristics.
        
        Args:
            ctx: MCP Context
            lane_config: Lane configuration as JSON object containing:
                - length_multiplier: Lane length multiplier compared to standard (default: 0.8)
                - centrality_factor: How central the lane is to map control (default: 1.0)
                - roam_accessibility: Accessibility for roaming to other lanes (default: 0.9)
                - river_control_importance: Importance of river control (default: 0.8)
                - wave_clear_priority: Priority for wave clearing (default: 0.9)
                - champion_types: Typical champion types for this lane
        
        Returns:
            Dict[str, Any]: Operation result
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            # Default configuration for Mid Lane
            default_config = {
                "lane_name": "mid_lane",
                "lane_type": "solo_lane",
                "length_multiplier": 0.8,
                "centrality_factor": 1.0,
                "roam_accessibility": 0.9,
                "river_control_importance": 0.8,
                "wave_clear_priority": 0.9,
                "champion_types": ["Mages", "Assassinos", "Roamers"],
                "nearby_objectives": ["Ambos Scuttles", "Controle de Rio"],
                "special_mechanics": [
                    "shortest_lane_fast_trades",
                    "central_map_access",
                    "roam_windows",
                    "river_control",
                    "wave_clear_priority",
                    "both_sides_access"
                ]
            }
            
            # Merge provided config with defaults
            if lane_config:
                default_config.update(lane_config)
            
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            logger.info(f"Creating Mid Lane mechanics with config: {default_config}")
            response = unreal.send_command("create_mid_lane_mechanics", default_config)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Mid Lane mechanics creation response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error creating Mid Lane mechanics: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def create_bot_lane_mechanics(
        ctx: Context,
        lane_config: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Create Bot Lane mechanics with its unique characteristics.
        
        Args:
            ctx: MCP Context
            lane_config: Lane configuration as JSON object containing:
                - length_multiplier: Lane length multiplier compared to standard (default: 1.0)
                - duo_lane_synergy: Importance of duo synergy (default: 0.9)
                - dragon_control_importance: Importance of dragon control (default: 0.8)
                - all_in_potential: Potential for all-in engagements (default: 0.7)
                - champion_types: Typical champion types for this lane
        
        Returns:
            Dict[str, Any]: Operation result
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            # Default configuration for Bot Lane
            default_config = {
                "lane_name": "bot_lane",
                "lane_type": "duo_lane",
                "length_multiplier": 1.0,
                "duo_lane_synergy": 0.9,
                "dragon_control_importance": 0.8,
                "all_in_potential": 0.7,
                "champion_types": ["ADC", "Support"],
                "nearby_objectives": ["Dragão", "Scuttle Inferior"],
                "special_mechanics": [
                    "standard_length_2v2_focus",
                    "duo_synergy_crucial",
                    "all_in_windows",
                    "dragon_control",
                    "bot_lane_synergy_combos",
                    "epic_objectives_access"
                ]
            }
            
            # Merge provided config with defaults
            if lane_config:
                default_config.update(lane_config)
            
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            logger.info(f"Creating Bot Lane mechanics with config: {default_config}")
            response = unreal.send_command("create_bot_lane_mechanics", default_config)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Bot Lane mechanics creation response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error creating Bot Lane mechanics: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def setup_lane_phases_system(
        ctx: Context,
        phases_config: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Setup the lane phases system (Early, Mid, Late Laning).
        
        Args:
            ctx: MCP Context
            phases_config: Phases configuration as JSON object containing:
                - early_laning_duration: Duration of early laning phase in minutes (default: 10)
                - mid_laning_duration: Duration of mid laning phase in minutes (default: 10)
                - late_laning_start: When late laning starts in minutes (default: 20)
                - enable_phase_transitions: Enable automatic phase transitions (default: True)
        
        Returns:
            Dict[str, Any]: Operation result
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            # Default configuration for Lane Phases
            default_config = {
                "system_name": "lane_phases_system",
                "early_laning_duration": 10,
                "mid_laning_duration": 10,
                "late_laning_start": 20,
                "enable_phase_transitions": True,
                "phases": {
                    "early_laning": {
                        "duration_minutes": 10,
                        "objectives": [
                            "establish_cs_advantage",
                            "control_wave_positioning",
                            "avoid_ganks_while_pressuring",
                            "establish_vision_control"
                        ],
                        "key_mechanics": [
                            "last_hitting",
                            "trading_stance",
                            "wave_management",
                            "back_timing"
                        ]
                    },
                    "mid_laning": {
                        "duration_minutes": 10,
                        "objectives": [
                            "transition_to_teamfights",
                            "control_neutral_objectives",
                            "roaming_and_map_pressure",
                            "establish_item_advantages"
                        ],
                        "key_mechanics": [
                            "roam_timing",
                            "objective_setup",
                            "vision_control",
                            "power_spikes"
                        ]
                    },
                    "late_laning": {
                        "start_minute": 20,
                        "objectives": [
                            "teamfight_positioning",
                            "objective_control",
                            "split_push_pressure",
                            "end_game_execution"
                        ],
                        "key_mechanics": [
                            "teamfight_execution",
                            "objective_prioritization",
                            "split_push_management",
                            "end_game_calls"
                        ]
                    }
                }
            }
            
            # Merge provided config with defaults
            if phases_config:
                default_config.update(phases_config)
            
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            logger.info(f"Setting up lane phases system with config: {default_config}")
            response = unreal.send_command("setup_lane_phases_system", default_config)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Lane phases system setup response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error setting up lane phases system: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
