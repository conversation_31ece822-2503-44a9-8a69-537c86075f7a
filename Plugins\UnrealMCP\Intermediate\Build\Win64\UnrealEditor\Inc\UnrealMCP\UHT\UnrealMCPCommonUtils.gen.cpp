// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "Commands/UnrealMCPCommonUtils.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeUnrealMCPCommonUtils() {}

// ********** Begin Cross Module References ********************************************************
UNREALMCP_API UScriptStruct* Z_Construct_UScriptStruct_FMCPActorCreationParams();
UPackage* Z_Construct_UPackage__Script_UnrealMCP();
// ********** End Cross Module References **********************************************************

// ********** Begin ScriptStruct FMCPActorCreationParams *******************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FMCPActorCreationParams;
class UScriptStruct* FMCPActorCreationParams::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FMCPActorCreationParams.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FMCPActorCreationParams.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FMCPActorCreationParams, (UObject*)Z_Construct_UPackage__Script_UnrealMCP(), TEXT("MCPActorCreationParams"));
	}
	return Z_Registration_Info_UScriptStruct_FMCPActorCreationParams.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FMCPActorCreationParams_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * MCP Actor creation parameters structure\n */" },
#endif
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPCommonUtils.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "MCP Actor creation parameters structure" },
#endif
	};
#endif // WITH_METADATA
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FMCPActorCreationParams>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FMCPActorCreationParams_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_UnrealMCP,
	nullptr,
	&NewStructOps,
	"MCPActorCreationParams",
	nullptr,
	0,
	sizeof(FMCPActorCreationParams),
	alignof(FMCPActorCreationParams),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FMCPActorCreationParams_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FMCPActorCreationParams_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FMCPActorCreationParams()
{
	if (!Z_Registration_Info_UScriptStruct_FMCPActorCreationParams.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FMCPActorCreationParams.InnerSingleton, Z_Construct_UScriptStruct_FMCPActorCreationParams_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FMCPActorCreationParams.InnerSingleton;
}
// ********** End ScriptStruct FMCPActorCreationParams *********************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Game_Auracron_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPCommonUtils_h__Script_UnrealMCP_Statics
{
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FMCPActorCreationParams::StaticStruct, Z_Construct_UScriptStruct_FMCPActorCreationParams_Statics::NewStructOps, TEXT("MCPActorCreationParams"), &Z_Registration_Info_UScriptStruct_FMCPActorCreationParams, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FMCPActorCreationParams), 877998705U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Game_Auracron_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPCommonUtils_h__Script_UnrealMCP_2686520486(TEXT("/Script/UnrealMCP"),
	nullptr, 0,
	Z_CompiledInDeferFile_FID_Game_Auracron_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPCommonUtils_h__Script_UnrealMCP_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Game_Auracron_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPCommonUtils_h__Script_UnrealMCP_Statics::ScriptStructInfo),
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
