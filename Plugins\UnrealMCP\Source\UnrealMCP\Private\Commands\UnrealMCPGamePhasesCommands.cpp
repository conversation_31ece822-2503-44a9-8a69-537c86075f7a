// Copyright Epic Games, Inc. All Rights Reserved.

#include "Commands/UnrealMCPGamePhasesCommands.h"

// ========================================================================
// Constantes
// ========================================================================

const FString FUnrealMCPGamePhasesCommands::RESPONSE_SUCCESS = TEXT("success");
const FString FUnrealMCPGamePhasesCommands::RESPONSE_ERROR = TEXT("error");
const FString FUnrealMCPGamePhasesCommands::RESPONSE_WARNING = TEXT("warning");
const FString FUnrealMCPGamePhasesCommands::RESPONSE_INFO = TEXT("info");

// ========================================================================
// Construtor e Destrutor
// ========================================================================

FUnrealMCPGamePhasesCommands::FUnrealMCPGamePhasesCommands()
    : bIsInitialized(false)
    , LastUpdateTime(FDateTime::Now())
{
    bIsInitialized = true;
    UE_LOG(LogTemp, Log, TEXT("FUnrealMCPGamePhasesCommands: Sistema de Fases da Partida inicializado"));
}

FUnrealMCPGamePhasesCommands::~FUnrealMCPGamePhasesCommands()
{
    PhaseConfigCache.Empty();
    PhaseStates.Empty();
    UE_LOG(LogTemp, Log, TEXT("FUnrealMCPGamePhasesCommands: Sistema de Fases da Partida finalizado"));
}

// ========================================================================
// Método Principal de Comando
// ========================================================================

TSharedPtr<FJsonObject> FUnrealMCPGamePhasesCommands::HandleCommand(const FString& CommandType, const TSharedPtr<FJsonObject>& Params)
{
    if (CommandType == TEXT("create_early_game_phase"))
    {
        return HandleCreateEarlyGamePhase(Params);
    }
    else if (CommandType == TEXT("create_mid_game_phase"))
    {
        return HandleCreateMidGamePhase(Params);
    }
    else if (CommandType == TEXT("create_late_game_phase"))
    {
        return HandleCreateLateGamePhase(Params);
    }
    else if (CommandType == TEXT("setup_complete_game_phases_system"))
    {
        return HandleSetupCompleteGamePhasesSystem(Params);
    }
    else
    {
        return CreateErrorResponse(FString::Printf(TEXT("Comando não reconhecido: %s"), *CommandType), TEXT("UNKNOWN_COMMAND"));
    }
}

// ========================================================================
// Implementações dos Comandos
// ========================================================================

TSharedPtr<FJsonObject> FUnrealMCPGamePhasesCommands::HandleCreateEarlyGamePhase(const TSharedPtr<FJsonObject>& CommandData)
{
    if (!CommandData.IsValid())
    {
        return CreateErrorResponse(TEXT("Dados do comando inválidos"), TEXT("INVALID_COMMAND_DATA"));
    }
    
    UE_LOG(LogTemp, Log, TEXT("Criando fase Early Game"));
    
    // Configurações padrão
    int32 DurationMinutes = 15;
    bool bEnableLaningFocus = true;
    bool bEnableJungleEstablishment = true;
    
    // Ler configurações do JSON se fornecidas
    CommandData->TryGetNumberField(TEXT("duration_minutes"), DurationMinutes);
    CommandData->TryGetBoolField(TEXT("enable_laning_focus"), bEnableLaningFocus);
    CommandData->TryGetBoolField(TEXT("enable_jungle_establishment"), bEnableJungleEstablishment);
    
    // Criar configuração da fase
    TSharedPtr<FJsonObject> PhaseConfig = MakeShared<FJsonObject>();
    PhaseConfig->SetStringField(TEXT("phase_name"), TEXT("early_game"));
    PhaseConfig->SetNumberField(TEXT("duration_minutes"), DurationMinutes);
    PhaseConfig->SetNumberField(TEXT("start_time"), 0);
    PhaseConfig->SetNumberField(TEXT("end_time"), DurationMinutes);
    
    // Objetivos primários
    TArray<TSharedPtr<FJsonValue>> PrimaryObjectives;
    PrimaryObjectives.Add(MakeShared<FJsonValueString>(TEXT("Estabelecer vantagem de CS")));
    PrimaryObjectives.Add(MakeShared<FJsonValueString>(TEXT("Controlar posicionamento de waves")));
    PrimaryObjectives.Add(MakeShared<FJsonValueString>(TEXT("Evitar ganks enquanto pressiona")));
    PrimaryObjectives.Add(MakeShared<FJsonValueString>(TEXT("Estabelecer controle de visão")));
    PrimaryObjectives.Add(MakeShared<FJsonValueString>(TEXT("Primeiro clear da jungle")));
    PrimaryObjectives.Add(MakeShared<FJsonValueString>(TEXT("Primeiros recalls estratégicos")));
    PhaseConfig->SetArrayField(TEXT("primary_objectives"), PrimaryObjectives);
    
    // Mecânicas-chave
    TSharedPtr<FJsonObject> KeyMechanics = MakeShared<FJsonObject>();
    
    if (bEnableLaningFocus)
    {
        TSharedPtr<FJsonObject> LaningPhase = MakeShared<FJsonObject>();
        LaningPhase->SetBoolField(TEXT("enabled"), true);
        LaningPhase->SetStringField(TEXT("focus"), TEXT("individual_skill_expression"));
        
        TArray<TSharedPtr<FJsonValue>> LaningMechanics;
        LaningMechanics.Add(MakeShared<FJsonValueString>(TEXT("last_hitting_minions")));
        LaningMechanics.Add(MakeShared<FJsonValueString>(TEXT("trading_stance")));
        LaningMechanics.Add(MakeShared<FJsonValueString>(TEXT("wave_management")));
        LaningMechanics.Add(MakeShared<FJsonValueString>(TEXT("back_timing")));
        LaningMechanics.Add(MakeShared<FJsonValueString>(TEXT("mana_management")));
        LaningPhase->SetArrayField(TEXT("mechanics"), LaningMechanics);
        
        KeyMechanics->SetObjectField(TEXT("laning_phase"), LaningPhase);
    }
    
    if (bEnableJungleEstablishment)
    {
        TSharedPtr<FJsonObject> JungleEstablishment = MakeShared<FJsonObject>();
        JungleEstablishment->SetBoolField(TEXT("enabled"), true);
        JungleEstablishment->SetStringField(TEXT("focus"), TEXT("clear_routes_and_ganks"));
        
        TArray<TSharedPtr<FJsonValue>> JungleMechanics;
        JungleMechanics.Add(MakeShared<FJsonValueString>(TEXT("optimal_clear_paths")));
        JungleMechanics.Add(MakeShared<FJsonValueString>(TEXT("gank_timing")));
        JungleMechanics.Add(MakeShared<FJsonValueString>(TEXT("objective_control")));
        JungleMechanics.Add(MakeShared<FJsonValueString>(TEXT("counter_jungling")));
        JungleEstablishment->SetArrayField(TEXT("mechanics"), JungleMechanics);
        
        KeyMechanics->SetObjectField(TEXT("jungle_establishment"), JungleEstablishment);
    }
    
    PhaseConfig->SetObjectField(TEXT("key_mechanics"), KeyMechanics);
    
    // Marcos temporais
    TSharedPtr<FJsonObject> MilestoneEvents = MakeShared<FJsonObject>();
    
    TSharedPtr<FJsonObject> FirstBlood = MakeShared<FJsonObject>();
    FirstBlood->SetStringField(TEXT("typical_time"), TEXT("2-5 minutes"));
    FirstBlood->SetStringField(TEXT("impact"), TEXT("psychological_advantage"));
    MilestoneEvents->SetObjectField(TEXT("first_blood"), FirstBlood);
    
    TSharedPtr<FJsonObject> FirstTower = MakeShared<FJsonObject>();
    FirstTower->SetStringField(TEXT("typical_time"), TEXT("8-12 minutes"));
    FirstTower->SetStringField(TEXT("impact"), TEXT("map_control_shift"));
    MilestoneEvents->SetObjectField(TEXT("first_tower"), FirstTower);
    
    TSharedPtr<FJsonObject> FirstDragon = MakeShared<FJsonObject>();
    FirstDragon->SetStringField(TEXT("typical_time"), TEXT("6-10 minutes"));
    FirstDragon->SetStringField(TEXT("impact"), TEXT("team_buff_advantage"));
    MilestoneEvents->SetObjectField(TEXT("first_dragon"), FirstDragon);
    
    PhaseConfig->SetObjectField(TEXT("milestone_events"), MilestoneEvents);
    
    // Condições de transição
    TArray<TSharedPtr<FJsonValue>> TransitionConditions;
    TransitionConditions.Add(MakeShared<FJsonValueString>(TEXT("multiple_towers_destroyed")));
    TransitionConditions.Add(MakeShared<FJsonValueString>(TEXT("team_grouping_begins")));
    TransitionConditions.Add(MakeShared<FJsonValueString>(TEXT("objective_contests_increase")));
    TransitionConditions.Add(MakeShared<FJsonValueString>(TEXT("roaming_frequency_increases")));
    PhaseConfig->SetArrayField(TEXT("transition_conditions"), TransitionConditions);
    
    // Salvar configuração no cache
    PhaseConfigCache.Add(TEXT("early_game"), PhaseConfig);
    
    // Criar estado da fase
    TSharedPtr<FJsonObject> PhaseState = MakeShared<FJsonObject>();
    PhaseState->SetStringField(TEXT("status"), TEXT("created"));
    PhaseState->SetStringField(TEXT("creation_time"), FDateTime::Now().ToString());
    PhaseState->SetBoolField(TEXT("active"), true);
    PhaseStates.Add(TEXT("early_game"), PhaseState);
    
    // Criar resposta de sucesso
    TSharedPtr<FJsonObject> ResponseData = MakeShared<FJsonObject>();
    ResponseData->SetObjectField(TEXT("phase_config"), PhaseConfig);
    ResponseData->SetObjectField(TEXT("phase_state"), PhaseState);
    ResponseData->SetStringField(TEXT("phase_description"), TEXT("Fase inicial com foco em laning e estabelecimento"));
    
    UE_LOG(LogTemp, Log, TEXT("Fase Early Game criada com sucesso"));
    return CreateSuccessResponse(ResponseData, TEXT("Fase Early Game criada com sucesso"));
}

TSharedPtr<FJsonObject> FUnrealMCPGamePhasesCommands::HandleCreateMidGamePhase(const TSharedPtr<FJsonObject>& CommandData)
{
    if (!CommandData.IsValid())
    {
        return CreateErrorResponse(TEXT("Dados do comando inválidos"), TEXT("INVALID_COMMAND_DATA"));
    }
    
    UE_LOG(LogTemp, Log, TEXT("Criando fase Mid Game"));
    
    // Configurações padrão
    int32 DurationMinutes = 15;
    bool bEnableTeamfightFocus = true;
    bool bEnableObjectiveControl = true;
    
    // Ler configurações do JSON se fornecidas
    CommandData->TryGetNumberField(TEXT("duration_minutes"), DurationMinutes);
    CommandData->TryGetBoolField(TEXT("enable_teamfight_focus"), bEnableTeamfightFocus);
    CommandData->TryGetBoolField(TEXT("enable_objective_control"), bEnableObjectiveControl);
    
    // Criar configuração da fase
    TSharedPtr<FJsonObject> PhaseConfig = MakeShared<FJsonObject>();
    PhaseConfig->SetStringField(TEXT("phase_name"), TEXT("mid_game"));
    PhaseConfig->SetNumberField(TEXT("duration_minutes"), DurationMinutes);
    PhaseConfig->SetNumberField(TEXT("start_time"), 15);
    PhaseConfig->SetNumberField(TEXT("end_time"), 30);
    
    // Objetivos primários
    TArray<TSharedPtr<FJsonValue>> PrimaryObjectives;
    PrimaryObjectives.Add(MakeShared<FJsonValueString>(TEXT("Transição para teamfights")));
    PrimaryObjectives.Add(MakeShared<FJsonValueString>(TEXT("Controle de objetivos neutros")));
    PrimaryObjectives.Add(MakeShared<FJsonValueString>(TEXT("Roaming e pressão no mapa")));
    PrimaryObjectives.Add(MakeShared<FJsonValueString>(TEXT("Estabelecer vantagens de itens")));
    PrimaryObjectives.Add(MakeShared<FJsonValueString>(TEXT("Controle de território")));
    PrimaryObjectives.Add(MakeShared<FJsonValueString>(TEXT("Preparação para late game")));
    PhaseConfig->SetArrayField(TEXT("primary_objectives"), PrimaryObjectives);
    
    // Salvar configuração no cache
    PhaseConfigCache.Add(TEXT("mid_game"), PhaseConfig);
    
    // Criar resposta de sucesso
    TSharedPtr<FJsonObject> ResponseData = MakeShared<FJsonObject>();
    ResponseData->SetObjectField(TEXT("phase_config"), PhaseConfig);
    ResponseData->SetStringField(TEXT("phase_description"), TEXT("Fase intermediária com teamfights e objetivos"));
    
    UE_LOG(LogTemp, Log, TEXT("Fase Mid Game criada com sucesso"));
    return CreateSuccessResponse(ResponseData, TEXT("Fase Mid Game criada com sucesso"));
}

TSharedPtr<FJsonObject> FUnrealMCPGamePhasesCommands::HandleCreateLateGamePhase(const TSharedPtr<FJsonObject>& CommandData)
{
    if (!CommandData.IsValid())
    {
        return CreateErrorResponse(TEXT("Dados do comando inválidos"), TEXT("INVALID_COMMAND_DATA"));
    }
    
    UE_LOG(LogTemp, Log, TEXT("Criando fase Late Game"));
    
    // Configurações padrão
    int32 StartTime = 30;
    bool bEnableHighStakes = true;
    bool bEnableElderDragon = true;
    
    // Ler configurações do JSON se fornecidas
    CommandData->TryGetNumberField(TEXT("start_time"), StartTime);
    CommandData->TryGetBoolField(TEXT("enable_high_stakes"), bEnableHighStakes);
    CommandData->TryGetBoolField(TEXT("enable_elder_dragon"), bEnableElderDragon);
    
    // Criar configuração da fase
    TSharedPtr<FJsonObject> PhaseConfig = MakeShared<FJsonObject>();
    PhaseConfig->SetStringField(TEXT("phase_name"), TEXT("late_game"));
    PhaseConfig->SetNumberField(TEXT("start_time"), StartTime);
    PhaseConfig->SetStringField(TEXT("end_time"), TEXT("game_end"));
    
    // Objetivos primários
    TArray<TSharedPtr<FJsonValue>> PrimaryObjectives;
    PrimaryObjectives.Add(MakeShared<FJsonValueString>(TEXT("Posicionamento em teamfights")));
    PrimaryObjectives.Add(MakeShared<FJsonValueString>(TEXT("Controle de objetivos épicos")));
    PrimaryObjectives.Add(MakeShared<FJsonValueString>(TEXT("Pressão de split push")));
    PrimaryObjectives.Add(MakeShared<FJsonValueString>(TEXT("Execução de end game")));
    PrimaryObjectives.Add(MakeShared<FJsonValueString>(TEXT("Controle total do mapa vertical")));
    PrimaryObjectives.Add(MakeShared<FJsonValueString>(TEXT("Finalização da partida")));
    PhaseConfig->SetArrayField(TEXT("primary_objectives"), PrimaryObjectives);
    
    // Salvar configuração no cache
    PhaseConfigCache.Add(TEXT("late_game"), PhaseConfig);
    
    // Criar resposta de sucesso
    TSharedPtr<FJsonObject> ResponseData = MakeShared<FJsonObject>();
    ResponseData->SetObjectField(TEXT("phase_config"), PhaseConfig);
    ResponseData->SetStringField(TEXT("phase_description"), TEXT("Fase final com decisões críticas"));
    
    UE_LOG(LogTemp, Log, TEXT("Fase Late Game criada com sucesso"));
    return CreateSuccessResponse(ResponseData, TEXT("Fase Late Game criada com sucesso"));
}

TSharedPtr<FJsonObject> FUnrealMCPGamePhasesCommands::HandleSetupCompleteGamePhasesSystem(const TSharedPtr<FJsonObject>& CommandData)
{
    if (!CommandData.IsValid())
    {
        return CreateErrorResponse(TEXT("Dados do comando inválidos"), TEXT("INVALID_COMMAND_DATA"));
    }
    
    UE_LOG(LogTemp, Log, TEXT("Configurando sistema completo de fases da partida"));
    
    // Configurações padrão
    bool bEnableAllPhases = true;
    bool bEnableDynamicTransitions = true;
    
    // Ler configurações do JSON se fornecidas
    CommandData->TryGetBoolField(TEXT("enable_all_phases"), bEnableAllPhases);
    CommandData->TryGetBoolField(TEXT("enable_dynamic_transitions"), bEnableDynamicTransitions);
    
    TSharedPtr<FJsonObject> SystemConfig = MakeShared<FJsonObject>();
    SystemConfig->SetStringField(TEXT("system_name"), TEXT("complete_game_phases_system"));
    SystemConfig->SetBoolField(TEXT("enable_all_phases"), bEnableAllPhases);
    SystemConfig->SetBoolField(TEXT("enable_dynamic_transitions"), bEnableDynamicTransitions);
    
    // Criar resposta de sucesso
    TSharedPtr<FJsonObject> ResponseData = MakeShared<FJsonObject>();
    ResponseData->SetObjectField(TEXT("system_config"), SystemConfig);
    ResponseData->SetStringField(TEXT("system_description"), TEXT("Sistema completo de fases da partida configurado"));
    
    UE_LOG(LogTemp, Log, TEXT("Sistema completo de fases da partida configurado com sucesso"));
    return CreateSuccessResponse(ResponseData, TEXT("Sistema completo de fases da partida configurado com sucesso"));
}

// ========================================================================
// Funções Auxiliares
// ========================================================================

TSharedPtr<FJsonObject> FUnrealMCPGamePhasesCommands::CreateErrorResponse(const FString& ErrorMessage, const FString& ErrorCode)
{
    TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
    Response->SetStringField(TEXT("status"), RESPONSE_ERROR);
    Response->SetStringField(TEXT("message"), ErrorMessage);
    Response->SetStringField(TEXT("error_code"), ErrorCode);
    Response->SetStringField(TEXT("timestamp"), FDateTime::Now().ToString());
    
    UE_LOG(LogTemp, Error, TEXT("GamePhases Error [%s]: %s"), *ErrorCode, *ErrorMessage);
    return Response;
}

TSharedPtr<FJsonObject> FUnrealMCPGamePhasesCommands::CreateSuccessResponse(const TSharedPtr<FJsonObject>& Data, const FString& Message)
{
    TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
    Response->SetStringField(TEXT("status"), RESPONSE_SUCCESS);
    Response->SetStringField(TEXT("message"), Message);
    Response->SetObjectField(TEXT("data"), Data);
    Response->SetStringField(TEXT("timestamp"), FDateTime::Now().ToString());
    
    return Response;
}
