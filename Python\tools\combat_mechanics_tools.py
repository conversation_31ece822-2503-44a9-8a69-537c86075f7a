"""Combat Mechanics Tools for Unreal MCP.

This module provides tools for creating and managing combat mechanics
including damage system, crowd control, healing, and tenacity.
"""

import logging
from typing import Dict, List, Any, Optional
from mcp.server.fastmcp import FastMCP, Context

# Get logger
logger = logging.getLogger("UnrealMCP")

def register_combat_mechanics_tools(mcp: FastMCP):
    """Register Combat Mechanics tools with the MCP server."""
    
    @mcp.tool()
    def create_damage_system(
        ctx: Context,
        damage_config: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Create the comprehensive damage system with all damage types.
        
        Args:
            ctx: MCP Context
            damage_config: Damage configuration as JSON object containing:
                - enable_physical_damage: Enable physical damage calculations (default: True)
                - enable_magic_damage: Enable magic damage calculations (default: True)
                - enable_true_damage: Enable true damage calculations (default: True)
                - enable_mixed_damage: Enable mixed damage calculations (default: True)
        
        Returns:
            Dict[str, Any]: Operation result
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            # Default configuration for Damage System
            default_config = {
                "system_name": "comprehensive_damage_system",
                "damage_types": {
                    "physical_damage": {
                        "enabled": True,
                        "calculation": "base_damage * (100 / (100 + armor))",
                        "penetration_types": [
                            "flat_armor_penetration",
                            "percent_armor_penetration",
                            "lethality"
                        ],
                        "reduction_sources": ["armor", "damage_reduction"],
                        "amplification_sources": ["critical_strike", "damage_amplification"]
                    },
                    "magic_damage": {
                        "enabled": True,
                        "calculation": "base_damage * (100 / (100 + magic_resistance))",
                        "penetration_types": [
                            "flat_magic_penetration",
                            "percent_magic_penetration",
                            "magic_penetration"
                        ],
                        "reduction_sources": ["magic_resistance", "magic_damage_reduction"],
                        "amplification_sources": ["ability_power_scaling", "magic_amplification"]
                    },
                    "true_damage": {
                        "enabled": True,
                        "calculation": "base_damage",
                        "description": "Ignores all resistances and damage reduction",
                        "sources": ["specific_abilities", "items", "runes"],
                        "cannot_be_reduced": True
                    },
                    "mixed_damage": {
                        "enabled": True,
                        "calculation": "physical_portion + magic_portion",
                        "description": "Combination of physical and magic damage",
                        "split_ratios": {
                            "50_50": "Equal physical and magic",
                            "60_40": "Physical favored",
                            "40_60": "Magic favored",
                            "custom": "Ability-specific ratios"
                        }
                    }
                },
                "damage_modifiers": {
                    "critical_strike": {
                        "base_multiplier": 2.0,
                        "infinity_edge_multiplier": 2.35,
                        "crit_chance_cap": 1.0,
                        "applies_to": ["physical_damage", "some_abilities"]
                    },
                    "armor_penetration": {
                        "calculation_order": [
                            "percent_bonus_armor_penetration",
                            "flat_armor_penetration",
                            "percent_armor_penetration",
                            "lethality"
                        ]
                    },
                    "magic_penetration": {
                        "calculation_order": [
                            "percent_magic_penetration",
                            "flat_magic_penetration"
                        ]
                    }
                },
                "special_damage_mechanics": {
                    "execute_damage": {
                        "description": "Deals damage based on missing health",
                        "calculation": "base_damage + (missing_health_percent * scaling)"
                    },
                    "max_health_damage": {
                        "description": "Deals damage based on maximum health",
                        "calculation": "base_damage + (max_health_percent * scaling)",
                        "damage_type": "usually_magic_or_true"
                    },
                    "current_health_damage": {
                        "description": "Deals damage based on current health",
                        "calculation": "base_damage + (current_health_percent * scaling)"
                    }
                }
            }
            
            # Merge provided config with defaults
            if damage_config:
                default_config.update(damage_config)
            
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            logger.info(f"Creating damage system with config: {default_config}")
            response = unreal.send_command("create_damage_system", default_config)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Damage system creation response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error creating damage system: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def create_crowd_control_system(
        ctx: Context,
        cc_config: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Create the crowd control (CC) system with all CC types.
        
        Args:
            ctx: MCP Context
            cc_config: CC configuration as JSON object containing:
                - enable_hard_cc: Enable hard crowd control effects (default: True)
                - enable_soft_cc: Enable soft crowd control effects (default: True)
                - enable_tenacity: Enable tenacity system (default: True)
        
        Returns:
            Dict[str, Any]: Operation result
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            # Default configuration for Crowd Control System
            default_config = {
                "system_name": "crowd_control_system",
                "hard_cc_types": {
                    "stun": {
                        "name": "Atordoamento",
                        "enabled": True,
                        "description": "Impede movimento, ataques e habilidades",
                        "affected_by_tenacity": True,
                        "can_be_cleansed": True,
                        "prevents": ["movement", "attacks", "abilities", "summoner_spells"]
                    },
                    "root": {
                        "name": "Enraizamento",
                        "enabled": True,
                        "description": "Impede movimento mas permite ataques e habilidades",
                        "affected_by_tenacity": True,
                        "can_be_cleansed": True,
                        "prevents": ["movement"],
                        "allows": ["attacks", "abilities", "summoner_spells"]
                    },
                    "suppress": {
                        "name": "Supressão",
                        "enabled": True,
                        "description": "CC mais forte que não pode ser removido",
                        "affected_by_tenacity": False,
                        "can_be_cleansed": False,
                        "prevents": ["movement", "attacks", "abilities", "summoner_spells"],
                        "special": "Cannot be reduced or removed"
                    },
                    "knockup": {
                        "name": "Levantamento",
                        "enabled": True,
                        "description": "Lança o alvo no ar",
                        "affected_by_tenacity": False,
                        "can_be_cleansed": False,
                        "prevents": ["movement", "attacks", "abilities"],
                        "special": "Airborne state"
                    },
                    "fear": {
                        "name": "Medo",
                        "enabled": True,
                        "description": "Força movimento aleatório",
                        "affected_by_tenacity": True,
                        "can_be_cleansed": True,
                        "prevents": ["controlled_movement", "attacks", "abilities"],
                        "forces": ["random_movement"]
                    },
                    "charm": {
                        "name": "Encanto",
                        "enabled": True,
                        "description": "Força movimento em direção ao conjurador",
                        "affected_by_tenacity": True,
                        "can_be_cleansed": True,
                        "prevents": ["controlled_movement", "attacks", "abilities"],
                        "forces": ["movement_toward_caster"]
                    },
                    "taunt": {
                        "name": "Provocação",
                        "enabled": True,
                        "description": "Força ataques contra o conjurador",
                        "affected_by_tenacity": True,
                        "can_be_cleansed": True,
                        "prevents": ["ability_usage", "movement_away"],
                        "forces": ["attacks_on_caster"]
                    }
                },
                "soft_cc_types": {
                    "slow": {
                        "name": "Lentidão",
                        "enabled": True,
                        "description": "Reduz velocidade de movimento",
                        "affected_by_tenacity": True,
                        "can_be_cleansed": True,
                        "effect": "movement_speed_reduction",
                        "stacks": "multiplicatively"
                    },
                    "blind": {
                        "name": "Cegueira",
                        "enabled": True,
                        "description": "Faz ataques errarem",
                        "affected_by_tenacity": True,
                        "can_be_cleansed": True,
                        "effect": "auto_attacks_miss"
                    },
                    "silence": {
                        "name": "Silêncio",
                        "enabled": True,
                        "description": "Impede uso de habilidades",
                        "affected_by_tenacity": True,
                        "can_be_cleansed": True,
                        "prevents": ["abilities"],
                        "allows": ["movement", "attacks"]
                    },
                    "disarm": {
                        "name": "Desarmar",
                        "enabled": True,
                        "description": "Impede ataques básicos",
                        "affected_by_tenacity": True,
                        "can_be_cleansed": True,
                        "prevents": ["attacks"],
                        "allows": ["movement", "abilities"]
                    },
                    "cripple": {
                        "name": "Aleijamento",
                        "enabled": True,
                        "description": "Reduz velocidade de ataque",
                        "affected_by_tenacity": True,
                        "can_be_cleansed": True,
                        "effect": "attack_speed_reduction"
                    }
                }
            }
            
            # Merge provided config with defaults
            if cc_config:
                default_config.update(cc_config)
            
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            logger.info(f"Creating crowd control system with config: {default_config}")
            response = unreal.send_command("create_crowd_control_system", default_config)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Crowd control system creation response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error creating crowd control system: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}

    @mcp.tool()
    def create_healing_system(
        ctx: Context,
        healing_config: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Create the healing and sustain system.

        Args:
            ctx: MCP Context
            healing_config: Healing configuration as JSON object containing:
                - enable_health_regeneration: Enable health regeneration (default: True)
                - enable_mana_regeneration: Enable mana regeneration (default: True)
                - enable_lifesteal: Enable lifesteal mechanics (default: True)
                - enable_spell_vamp: Enable spell vamp mechanics (default: True)

        Returns:
            Dict[str, Any]: Operation result
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            # Default configuration for Healing System
            default_config = {
                "system_name": "healing_sustain_system",
                "regeneration_types": {
                    "health_regeneration": {
                        "enabled": True,
                        "base_regen": 1.5,
                        "per_level": 0.11,
                        "calculation": "base_regen + (level * per_level) + bonus_regen",
                        "affected_by": ["grievous_wounds", "healing_amplification"],
                        "out_of_combat_bonus": 0.0,
                        "in_combat_reduction": 0.0
                    },
                    "mana_regeneration": {
                        "enabled": True,
                        "base_regen": 2.0,
                        "per_level": 0.13,
                        "calculation": "base_regen + (level * per_level) + bonus_regen",
                        "affected_by": ["mana_regen_items", "abilities"],
                        "out_of_combat_bonus": 1.0,
                        "meditation_bonus": 0.5
                    }
                },
                "lifesteal_mechanics": {
                    "physical_lifesteal": {
                        "enabled": True,
                        "description": "Heals based on physical damage dealt",
                        "calculation": "physical_damage * lifesteal_percent",
                        "applies_to": ["auto_attacks", "physical_abilities"],
                        "reduced_by": ["grievous_wounds"],
                        "aoe_effectiveness": 0.33
                    },
                    "spell_vamp": {
                        "enabled": True,
                        "description": "Heals based on magic damage dealt",
                        "calculation": "magic_damage * spell_vamp_percent",
                        "applies_to": ["abilities", "magic_damage"],
                        "reduced_by": ["grievous_wounds"],
                        "aoe_effectiveness": 0.33
                    },
                    "omnivamp": {
                        "enabled": True,
                        "description": "Heals based on all damage dealt",
                        "calculation": "total_damage * omnivamp_percent",
                        "applies_to": ["all_damage_types"],
                        "reduced_by": ["grievous_wounds"],
                        "aoe_effectiveness": 0.33
                    }
                },
                "healing_reduction": {
                    "grievous_wounds": {
                        "name": "Ferimentos Graves",
                        "enabled": True,
                        "reduction_percent": 0.4,
                        "enhanced_reduction_percent": 0.6,
                        "applies_to": ["all_healing", "regeneration", "lifesteal"],
                        "sources": ["items", "abilities", "runes"],
                        "duration_typical": 3.0
                    },
                    "healing_amplification": {
                        "name": "Amplificação de Cura",
                        "enabled": True,
                        "amplification_sources": [
                            "spirit_visage",
                            "revitalize_rune",
                            "healing_abilities"
                        ],
                        "typical_amplification": 0.25
                    }
                },
                "special_healing_mechanics": {
                    "shields": {
                        "description": "Temporary health that absorbs damage",
                        "types": ["physical_shield", "magic_shield", "all_damage_shield"],
                        "decay": "some_shields_decay_over_time",
                        "stacking": "shields_stack_additively"
                    },
                    "true_healing": {
                        "description": "Healing that cannot be reduced",
                        "sources": ["specific_abilities", "fountain"],
                        "ignores": ["grievous_wounds", "healing_reduction"]
                    },
                    "percentage_healing": {
                        "description": "Healing based on max health percentage",
                        "calculation": "max_health * percentage",
                        "common_percentages": [0.02, 0.05, 0.10, 0.15]
                    }
                }
            }

            # Merge provided config with defaults
            if healing_config:
                default_config.update(healing_config)

            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            logger.info(f"Creating healing system with config: {default_config}")
            response = unreal.send_command("create_healing_system", default_config)

            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}

            logger.info(f"Healing system creation response: {response}")
            return response

        except Exception as e:
            error_msg = f"Error creating healing system: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}

    @mcp.tool()
    def create_tenacity_system(
        ctx: Context,
        tenacity_config: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Create the tenacity system for CC reduction.

        Args:
            ctx: MCP Context
            tenacity_config: Tenacity configuration as JSON object containing:
                - base_tenacity: Base tenacity value (default: 0.0)
                - max_tenacity: Maximum tenacity cap (default: 0.95)
                - enable_stacking: Enable tenacity stacking (default: True)

        Returns:
            Dict[str, Any]: Operation result
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            # Default configuration for Tenacity System
            default_config = {
                "system_name": "tenacity_system",
                "base_tenacity": 0.0,
                "max_tenacity": 0.95,
                "enable_stacking": True,
                "tenacity_mechanics": {
                    "calculation": "1 - ((1 - tenacity1) * (1 - tenacity2) * ...)",
                    "description": "Reduces duration of crowd control effects",
                    "applies_to": [
                        "stun", "root", "fear", "charm", "taunt",
                        "slow", "blind", "silence", "disarm", "cripple"
                    ],
                    "does_not_apply_to": [
                        "suppress", "knockup", "displacement"
                    ]
                },
                "tenacity_sources": {
                    "items": {
                        "mercury_treads": 0.30,
                        "legend_tenacity": 0.30,
                        "steraks_gage": 0.30,
                        "silvermere_dawn": 0.50
                    },
                    "runes": {
                        "legend_tenacity": 0.30,
                        "unflinching": 0.15
                    },
                    "abilities": {
                        "champion_specific": "varies_by_champion",
                        "temporary_tenacity": "some_abilities_grant_temporary"
                    },
                    "masteries": {
                        "tenacity_mastery": 0.15
                    }
                },
                "special_interactions": {
                    "diminishing_returns": {
                        "enabled": False,
                        "description": "Tenacity stacks multiplicatively, not additively"
                    },
                    "cc_immunity": {
                        "description": "Some abilities grant temporary CC immunity",
                        "examples": ["olaf_ultimate", "malzahar_passive", "morgana_black_shield"]
                    },
                    "unstoppable": {
                        "description": "Unstoppable effects ignore all CC",
                        "examples": ["sion_ultimate", "malphite_ultimate"]
                    }
                }
            }

            # Merge provided config with defaults
            if tenacity_config:
                default_config.update(tenacity_config)

            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            logger.info(f"Creating tenacity system with config: {default_config}")
            response = unreal.send_command("create_tenacity_system", default_config)

            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}

            logger.info(f"Tenacity system creation response: {response}")
            return response

        except Exception as e:
            error_msg = f"Error creating tenacity system: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
