LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "ping", "params": {}}
LogTemp: Display: UnrealMCPBridge: Executing command: ping
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "success",
    "result":
    {
        "message": "pong"
    }
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 69
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "get_engine_version", "params": {}}
LogTemp: Display: UnrealMCPBridge: Executing command: get_engine_version
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "success",
    "result":
    {
        "version": "5.6.1-44394996+++UE5+Release-5.6",
        "branch": "//UE5/Release-5.6",
        "major": 5,
        "minor": 6,
        "patch": 1
    }
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 0
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "get_project_name", "params": {}}
LogTemp: Display: UnrealMCPBridge: Executing command: get_project_name
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "success",
    "result":
    {
        "project_name": "Auracron",
        "game_name": "Auracron"
    }
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 106
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "list_available_commands", "params": {}}
LogTemp: Display: UnrealMCPBridge: Executing command: list_available_commands
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "success",
    "result":
    {
        "commands": [
            "ping",
            "get_engine_version",
            "get_project_name",
            "list_available_commands",
            "get_world_info",
            "get_level_info",
            "get_actors_in_level",
            "spawn_actor",
            "get_selected_actors",
            "set_actor_location",
            "create_blueprint_class",
            "create_actor_blueprint"
        ]
    }
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 0
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "get_world_info", "params": {}}
LogTemp: Display: UnrealMCPBridge: Executing command: get_world_info
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "success",
    "result":
    {
        "world_name": "AURACRON",
        "world_type": "Editor",
        "is_game_world": false,
        "is_editor_world": true,
        "time_seconds": 175.64460765197873,
        "real_time_seconds": 179.71153472736478
    }
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 0
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "get_level_info", "params": {}}
LogTemp: Display: UnrealMCPBridge: Executing command: get_level_info
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "success",
    "result":
    {
        "level_name": "PersistentLevel",
        "package_name": "/Game/AURACRON",
        "actor_count": 8,
        "is_persistent_level": true
    }
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 0
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "get_selected_actors", "params": {}}
LogTemp: Display: UnrealMCPBridge: Executing command: get_selected_actors
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "success",
    "result":
    {
        "actors": [],
        "count": 0
    }
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 79
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "spawn_actor", "params": {"type": "StaticMeshActor", "name": "TestActor_Robust_001", "location": [200, 0, 100]}}
LogTemp: Display: UnrealMCPBridge: Executing command: spawn_actor
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "success",
    "result":
    {
        "name": "TestActor_Robust_001",
        "class": "StaticMeshActor",
        "location": [ 200, 0, 100 ],
        "rotation": [ 0, 0, 0 ],
        "scale": [ 1, 1, 1 ]
    }
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 0
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "spawn_actor", "params": {"type": "PointLight", "name": "TestLight_Robust_001", "location": [300, 0, 150]}}
LogTemp: Display: UnrealMCPBridge: Executing command: spawn_actor
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "success",
    "result":
    {
        "name": "TestLight_Robust_001",
        "class": "PointLight",
        "location": [ 300, 0, 150 ],
        "rotation": [ 0, 0, 0 ],
        "scale": [ 1, 1, 1 ]
    }
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 0
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "create_blueprint_class", "params": {"name": "BP_TestClass_Robust", "parent_class": "Actor", "package_path": "/Game/Blueprints/"}}
LogTemp: Display: UnrealMCPBridge: Executing command: create_blueprint_class
LogEditorAssetSubsystem: Error: SaveAsset failed: Could not load asset: The AssetData '/Game/Blueprints/BP_TestClass_Robust.BP_TestClass_Robust' could not be found in the Asset Registry.
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "success",
    "result":
    {
        "name": "BP_TestClass_Robust",
        "path": "/Game/Blueprints/BP_TestClass_Robust",
        "parent_class": "Actor",
        "saved": false
    }
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 1
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "create_actor_blueprint", "params": {"name": "BP_TestActor_Robust", "package_path": "/Game/Blueprints/Actors/"}}
LogTemp: Display: UnrealMCPBridge: Executing command: create_actor_blueprint
LogEditorAssetSubsystem: Error: SaveAsset failed: Could not load asset: The AssetData '/Game/Blueprints/Actors/BP_TestActor_Robust.BP_TestActor_Robust' could not be found in the Asset Registry.
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "success",
    "result":
    {
        "name": "BP_TestActor_Robust",
        "path": "/Game/Blueprints/Actors/BP_TestActor_Robust",
        "parent_class": "Actor",
        "saved": false
    }
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 6
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)