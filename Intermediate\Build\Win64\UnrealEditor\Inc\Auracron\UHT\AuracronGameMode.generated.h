// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronGameMode.h"

#ifdef AURACRON_AuracronGameMode_generated_h
#error "AuracronGameMode.generated.h already included, missing '#pragma once' in AuracronGameMode.h"
#endif
#define AURACRON_AuracronGameMode_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

// ********** Begin Class AAuracronGameMode ********************************************************
#define FID_Game_Auracron_Source_Auracron_Public_AuracronGameMode_h_10_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execGetCurrentGamePhase); \
	DECLARE_FUNCTION(execTransitionToNextPhase); \
	DECLARE_FUNCTION(execInitializeMultilayerSystem);


AURACRON_API UClass* Z_Construct_UClass_AAuracronGameMode_NoRegister();

#define FID_Game_Auracron_Source_Auracron_Public_AuracronGameMode_h_10_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesAAuracronGameMode(); \
	friend struct Z_Construct_UClass_AAuracronGameMode_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRON_API UClass* Z_Construct_UClass_AAuracronGameMode_NoRegister(); \
public: \
	DECLARE_CLASS2(AAuracronGameMode, AGameModeBase, COMPILED_IN_FLAGS(0 | CLASS_Transient | CLASS_Config), CASTCLASS_None, TEXT("/Script/Auracron"), Z_Construct_UClass_AAuracronGameMode_NoRegister) \
	DECLARE_SERIALIZER(AAuracronGameMode)


#define FID_Game_Auracron_Source_Auracron_Public_AuracronGameMode_h_10_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	AAuracronGameMode(AAuracronGameMode&&) = delete; \
	AAuracronGameMode(const AAuracronGameMode&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, AAuracronGameMode); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(AAuracronGameMode); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(AAuracronGameMode) \
	NO_API virtual ~AAuracronGameMode();


#define FID_Game_Auracron_Source_Auracron_Public_AuracronGameMode_h_7_PROLOG
#define FID_Game_Auracron_Source_Auracron_Public_AuracronGameMode_h_10_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Game_Auracron_Source_Auracron_Public_AuracronGameMode_h_10_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Game_Auracron_Source_Auracron_Public_AuracronGameMode_h_10_INCLASS_NO_PURE_DECLS \
	FID_Game_Auracron_Source_Auracron_Public_AuracronGameMode_h_10_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class AAuracronGameMode;

// ********** End Class AAuracronGameMode **********************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Game_Auracron_Source_Auracron_Public_AuracronGameMode_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
