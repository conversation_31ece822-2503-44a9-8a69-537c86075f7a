#include "Commands/UnrealMCPCommonUtils.h"
#include "GameFramework/Actor.h"
#include "Engine/Blueprint.h"
#include "EdGraph/EdGraph.h"
#include "EdGraph/EdGraphNode.h"
#include "EdGraph/EdGraphPin.h"
#include "K2Node_Event.h"
#include "K2Node_CallFunction.h"
#include "K2Node_VariableGet.h"
#include "K2Node_VariableSet.h"
#include "K2Node_InputAction.h"
#include "K2Node_Self.h"
#include "EdGraphSchema_K2.h"
#include "Kismet2/BlueprintEditorUtils.h"
#include "Components/StaticMeshComponent.h"
#include "Components/SkeletalMeshComponent.h"
#include "Components/BoxComponent.h"
#include "Components/SphereComponent.h"
#include "Components/CapsuleComponent.h"
#include "Components/LightComponent.h"
#include "Components/PointLightComponent.h"
#include "Components/SpotLightComponent.h"
#include "Components/DirectionalLightComponent.h"
#include "Components/PrimitiveComponent.h"
#include "Components/SceneComponent.h"
#include "Engine/Engine.h"
#include "Engine/World.h"
#include "Engine/StaticMeshActor.h"

#include "Engine/Light.h"
#include "Engine/PointLight.h"
#include "Engine/SpotLight.h"
#include "Engine/DirectionalLight.h"
#include "Engine/TriggerVolume.h"
#include "Engine/BlockingVolume.h"
#include "Engine/StaticMesh.h"
#include "Engine/SkeletalMesh.h"
#include "GameFramework/Character.h"
#include "GameFramework/Pawn.h"
#include "AIController.h"
#include "BehaviorTree/BehaviorTree.h"
#include "BehaviorTree/BlackboardComponent.h"
#include "BehaviorTree/BlackboardData.h"
#include "Materials/MaterialInterface.h"
#include "UObject/UObjectIterator.h"
#include "Engine/Selection.h"
#include "EditorAssetLibrary.h"
#include "AssetRegistry/AssetRegistryModule.h"
#include "Engine/BlueprintGeneratedClass.h"
#include "BlueprintNodeSpawner.h"
#include "BlueprintActionDatabase.h"
#include "Dom/JsonObject.h"
#include "Dom/JsonValue.h"
#include "UObject/ConstructorHelpers.h"
#include "Kismet/GameplayStatics.h"

// JSON Utilities
TSharedPtr<FJsonObject> FUnrealMCPCommonUtils::CreateErrorResponse(const FString& Message)
{
    TSharedPtr<FJsonObject> ResponseObject = MakeShared<FJsonObject>();
    ResponseObject->SetBoolField(TEXT("success"), false);
    ResponseObject->SetStringField(TEXT("error"), Message);
    return ResponseObject;
}

TSharedPtr<FJsonObject> FUnrealMCPCommonUtils::CreateSuccessResponse(const TSharedPtr<FJsonObject>& Data)
{
    TSharedPtr<FJsonObject> ResponseObject = MakeShared<FJsonObject>();
    ResponseObject->SetBoolField(TEXT("success"), true);
    
    if (Data.IsValid())
    {
        ResponseObject->SetObjectField(TEXT("data"), Data);
    }
    
    return ResponseObject;
}

void FUnrealMCPCommonUtils::GetIntArrayFromJson(const TSharedPtr<FJsonObject>& JsonObject, const FString& FieldName, TArray<int32>& OutArray)
{
    OutArray.Reset();
    
    if (!JsonObject->HasField(FieldName))
    {
        return;
    }
    
    const TArray<TSharedPtr<FJsonValue>>* JsonArray;
    if (JsonObject->TryGetArrayField(FieldName, JsonArray))
    {
        for (const TSharedPtr<FJsonValue>& Value : *JsonArray)
        {
            OutArray.Add((int32)Value->AsNumber());
        }
    }
}

void FUnrealMCPCommonUtils::GetFloatArrayFromJson(const TSharedPtr<FJsonObject>& JsonObject, const FString& FieldName, TArray<float>& OutArray)
{
    OutArray.Reset();
    
    if (!JsonObject->HasField(FieldName))
    {
        return;
    }
    
    const TArray<TSharedPtr<FJsonValue>>* JsonArray;
    if (JsonObject->TryGetArrayField(FieldName, JsonArray))
    {
        for (const TSharedPtr<FJsonValue>& Value : *JsonArray)
        {
            OutArray.Add((float)Value->AsNumber());
        }
    }
}

FVector2D FUnrealMCPCommonUtils::GetVector2DFromJson(const TSharedPtr<FJsonObject>& JsonObject, const FString& FieldName)
{
    FVector2D Result(0.0f, 0.0f);
    
    if (!JsonObject->HasField(FieldName))
    {
        return Result;
    }
    
    const TArray<TSharedPtr<FJsonValue>>* JsonArray;
    if (JsonObject->TryGetArrayField(FieldName, JsonArray) && JsonArray->Num() >= 2)
    {
        Result.X = (float)(*JsonArray)[0]->AsNumber();
        Result.Y = (float)(*JsonArray)[1]->AsNumber();
    }
    
    return Result;
}

FVector FUnrealMCPCommonUtils::GetVectorFromJson(const TSharedPtr<FJsonObject>& JsonObject, const FString& FieldName)
{
    FVector Result(0.0f, 0.0f, 0.0f);
    
    if (!JsonObject->HasField(FieldName))
    {
        return Result;
    }
    
    const TArray<TSharedPtr<FJsonValue>>* JsonArray;
    if (JsonObject->TryGetArrayField(FieldName, JsonArray) && JsonArray->Num() >= 3)
    {
        Result.X = (float)(*JsonArray)[0]->AsNumber();
        Result.Y = (float)(*JsonArray)[1]->AsNumber();
        Result.Z = (float)(*JsonArray)[2]->AsNumber();
    }
    
    return Result;
}

FRotator FUnrealMCPCommonUtils::GetRotatorFromJson(const TSharedPtr<FJsonObject>& JsonObject, const FString& FieldName)
{
    FRotator Result(0.0f, 0.0f, 0.0f);
    
    if (!JsonObject->HasField(FieldName))
    {
        return Result;
    }
    
    const TArray<TSharedPtr<FJsonValue>>* JsonArray;
    if (JsonObject->TryGetArrayField(FieldName, JsonArray) && JsonArray->Num() >= 3)
    {
        Result.Pitch = (float)(*JsonArray)[0]->AsNumber();
        Result.Yaw = (float)(*JsonArray)[1]->AsNumber();
        Result.Roll = (float)(*JsonArray)[2]->AsNumber();
    }
    
    return Result;
}

// Blueprint Utilities
UBlueprint* FUnrealMCPCommonUtils::FindBlueprint(const FString& BlueprintName)
{
    return FindBlueprintByName(BlueprintName);
}

UBlueprint* FUnrealMCPCommonUtils::FindBlueprintByName(const FString& BlueprintName)
{
    FString AssetPath = TEXT("/Game/Blueprints/") + BlueprintName;
    return LoadObject<UBlueprint>(nullptr, *AssetPath);
}

UEdGraph* FUnrealMCPCommonUtils::FindOrCreateEventGraph(UBlueprint* Blueprint)
{
    if (!Blueprint)
    {
        return nullptr;
    }
    
    // Try to find the event graph
    for (UEdGraph* Graph : Blueprint->UbergraphPages)
    {
        if (Graph->GetName().Contains(TEXT("EventGraph")))
        {
            return Graph;
        }
    }
    
    // Create a new event graph if none exists
    UEdGraph* NewGraph = FBlueprintEditorUtils::CreateNewGraph(Blueprint, FName(TEXT("EventGraph")), UEdGraph::StaticClass(), UEdGraphSchema_K2::StaticClass());
    FBlueprintEditorUtils::AddUbergraphPage(Blueprint, NewGraph);
    return NewGraph;
}

// Blueprint node utilities
UK2Node_Event* FUnrealMCPCommonUtils::CreateEventNode(UEdGraph* Graph, const FString& EventName, const FVector2D& Position)
{
    if (!Graph)
    {
        return nullptr;
    }
    
    UBlueprint* Blueprint = FBlueprintEditorUtils::FindBlueprintForGraph(Graph);
    if (!Blueprint)
    {
        return nullptr;
    }
    
    // Check for existing event node with this exact name
    for (UEdGraphNode* Node : Graph->Nodes)
    {
        UK2Node_Event* EventNode = Cast<UK2Node_Event>(Node);
        if (EventNode && EventNode->EventReference.GetMemberName() == FName(*EventName))
        {
            UE_LOG(LogTemp, Display, TEXT("Using existing event node with name %s (ID: %s)"), 
                *EventName, *EventNode->NodeGuid.ToString());
            return EventNode;
        }
    }

    // No existing node found, create a new one
    UK2Node_Event* EventNode = nullptr;
    
    // Find the function to create the event
    UClass* BlueprintClass = Blueprint->GeneratedClass;
    UFunction* EventFunction = BlueprintClass->FindFunctionByName(FName(*EventName));
    
    if (EventFunction)
    {
        EventNode = NewObject<UK2Node_Event>(Graph);
        EventNode->EventReference.SetExternalMember(FName(*EventName), BlueprintClass);
        EventNode->NodePosX = Position.X;
        EventNode->NodePosY = Position.Y;
        Graph->AddNode(EventNode, true);
        EventNode->PostPlacedNewNode();
        EventNode->AllocateDefaultPins();
        UE_LOG(LogTemp, Display, TEXT("Created new event node with name %s (ID: %s)"), 
            *EventName, *EventNode->NodeGuid.ToString());
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("Failed to find function for event name: %s"), *EventName);
    }
    
    return EventNode;
}

UK2Node_CallFunction* FUnrealMCPCommonUtils::CreateFunctionCallNode(UEdGraph* Graph, UFunction* Function, const FVector2D& Position)
{
    if (!Graph || !Function)
    {
        return nullptr;
    }
    
    UK2Node_CallFunction* FunctionNode = NewObject<UK2Node_CallFunction>(Graph);
    FunctionNode->SetFromFunction(Function);
    FunctionNode->NodePosX = Position.X;
    FunctionNode->NodePosY = Position.Y;
    Graph->AddNode(FunctionNode, true);
    FunctionNode->CreateNewGuid();
    FunctionNode->PostPlacedNewNode();
    FunctionNode->AllocateDefaultPins();
    
    return FunctionNode;
}

UK2Node_VariableGet* FUnrealMCPCommonUtils::CreateVariableGetNode(UEdGraph* Graph, UBlueprint* Blueprint, const FString& VariableName, const FVector2D& Position)
{
    if (!Graph || !Blueprint)
    {
        return nullptr;
    }
    
    UK2Node_VariableGet* VariableGetNode = NewObject<UK2Node_VariableGet>(Graph);
    
    FName VarName(*VariableName);
    FProperty* Property = FindFProperty<FProperty>(Blueprint->GeneratedClass, VarName);
    
    if (Property)
    {
        VariableGetNode->VariableReference.SetFromField<FProperty>(Property, false);
        VariableGetNode->NodePosX = Position.X;
        VariableGetNode->NodePosY = Position.Y;
        Graph->AddNode(VariableGetNode, true);
        VariableGetNode->PostPlacedNewNode();
        VariableGetNode->AllocateDefaultPins();
        
        return VariableGetNode;
    }
    
    return nullptr;
}

UK2Node_VariableSet* FUnrealMCPCommonUtils::CreateVariableSetNode(UEdGraph* Graph, UBlueprint* Blueprint, const FString& VariableName, const FVector2D& Position)
{
    if (!Graph || !Blueprint)
    {
        return nullptr;
    }
    
    UK2Node_VariableSet* VariableSetNode = NewObject<UK2Node_VariableSet>(Graph);
    
    FName VarName(*VariableName);
    FProperty* Property = FindFProperty<FProperty>(Blueprint->GeneratedClass, VarName);
    
    if (Property)
    {
        VariableSetNode->VariableReference.SetFromField<FProperty>(Property, false);
        VariableSetNode->NodePosX = Position.X;
        VariableSetNode->NodePosY = Position.Y;
        Graph->AddNode(VariableSetNode, true);
        VariableSetNode->PostPlacedNewNode();
        VariableSetNode->AllocateDefaultPins();
        
        return VariableSetNode;
    }
    
    return nullptr;
}

UK2Node_InputAction* FUnrealMCPCommonUtils::CreateInputActionNode(UEdGraph* Graph, const FString& ActionName, const FVector2D& Position)
{
    if (!Graph)
    {
        return nullptr;
    }
    
    UK2Node_InputAction* InputActionNode = NewObject<UK2Node_InputAction>(Graph);
    InputActionNode->InputActionName = FName(*ActionName);
    InputActionNode->NodePosX = Position.X;
    InputActionNode->NodePosY = Position.Y;
    Graph->AddNode(InputActionNode, true);
    InputActionNode->CreateNewGuid();
    InputActionNode->PostPlacedNewNode();
    InputActionNode->AllocateDefaultPins();
    
    return InputActionNode;
}

UK2Node_Self* FUnrealMCPCommonUtils::CreateSelfReferenceNode(UEdGraph* Graph, const FVector2D& Position)
{
    if (!Graph)
    {
        return nullptr;
    }
    
    UK2Node_Self* SelfNode = NewObject<UK2Node_Self>(Graph);
    SelfNode->NodePosX = Position.X;
    SelfNode->NodePosY = Position.Y;
    Graph->AddNode(SelfNode, true);
    SelfNode->CreateNewGuid();
    SelfNode->PostPlacedNewNode();
    SelfNode->AllocateDefaultPins();
    
    return SelfNode;
}

bool FUnrealMCPCommonUtils::ConnectGraphNodes(UEdGraph* Graph, UEdGraphNode* SourceNode, const FString& SourcePinName, 
                                           UEdGraphNode* TargetNode, const FString& TargetPinName)
{
    if (!Graph || !SourceNode || !TargetNode)
    {
        return false;
    }
    
    UEdGraphPin* SourcePin = FindPin(SourceNode, SourcePinName, EGPD_Output);
    UEdGraphPin* TargetPin = FindPin(TargetNode, TargetPinName, EGPD_Input);
    
    if (SourcePin && TargetPin)
    {
        SourcePin->MakeLinkTo(TargetPin);
        return true;
    }
    
    return false;
}

UEdGraphPin* FUnrealMCPCommonUtils::FindPin(UEdGraphNode* Node, const FString& PinName, EEdGraphPinDirection Direction)
{
    if (!Node)
    {
        return nullptr;
    }
    
    // Log all pins for debugging
    UE_LOG(LogTemp, Display, TEXT("FindPin: Looking for pin '%s' (Direction: %d) in node '%s'"), 
           *PinName, (int32)Direction, *Node->GetName());
    
    for (UEdGraphPin* Pin : Node->Pins)
    {
        UE_LOG(LogTemp, Display, TEXT("  - Available pin: '%s', Direction: %d, Category: %s"), 
               *Pin->PinName.ToString(), (int32)Pin->Direction, *Pin->PinType.PinCategory.ToString());
    }
    
    // First try exact match
    for (UEdGraphPin* Pin : Node->Pins)
    {
        if (Pin->PinName.ToString() == PinName && (Direction == EGPD_MAX || Pin->Direction == Direction))
        {
            UE_LOG(LogTemp, Display, TEXT("  - Found exact matching pin: '%s'"), *Pin->PinName.ToString());
            return Pin;
        }
    }
    
    // If no exact match and we're looking for a component reference, try case-insensitive match
    for (UEdGraphPin* Pin : Node->Pins)
    {
        if (Pin->PinName.ToString().Equals(PinName, ESearchCase::IgnoreCase) && 
            (Direction == EGPD_MAX || Pin->Direction == Direction))
        {
            UE_LOG(LogTemp, Display, TEXT("  - Found case-insensitive matching pin: '%s'"), *Pin->PinName.ToString());
            return Pin;
        }
    }
    
    // If we're looking for a component output and didn't find it by name, try to find the first data output pin
    if (Direction == EGPD_Output && Cast<UK2Node_VariableGet>(Node) != nullptr)
    {
        for (UEdGraphPin* Pin : Node->Pins)
        {
            if (Pin->Direction == EGPD_Output && Pin->PinType.PinCategory != UEdGraphSchema_K2::PC_Exec)
            {
                UE_LOG(LogTemp, Display, TEXT("  - Found fallback data output pin: '%s'"), *Pin->PinName.ToString());
                return Pin;
            }
        }
    }
    
    UE_LOG(LogTemp, Warning, TEXT("  - No matching pin found for '%s'"), *PinName);
    return nullptr;
}

// Actor utilities
TSharedPtr<FJsonValue> FUnrealMCPCommonUtils::ActorToJson(AActor* Actor)
{
    if (!Actor)
    {
        return MakeShared<FJsonValueNull>();
    }
    
    TSharedPtr<FJsonObject> ActorObject = MakeShared<FJsonObject>();
    ActorObject->SetStringField(TEXT("name"), Actor->GetName());
    ActorObject->SetStringField(TEXT("class"), Actor->GetClass()->GetName());
    
    FVector Location = Actor->GetActorLocation();
    TArray<TSharedPtr<FJsonValue>> LocationArray;
    LocationArray.Add(MakeShared<FJsonValueNumber>(Location.X));
    LocationArray.Add(MakeShared<FJsonValueNumber>(Location.Y));
    LocationArray.Add(MakeShared<FJsonValueNumber>(Location.Z));
    ActorObject->SetArrayField(TEXT("location"), LocationArray);
    
    FRotator Rotation = Actor->GetActorRotation();
    TArray<TSharedPtr<FJsonValue>> RotationArray;
    RotationArray.Add(MakeShared<FJsonValueNumber>(Rotation.Pitch));
    RotationArray.Add(MakeShared<FJsonValueNumber>(Rotation.Yaw));
    RotationArray.Add(MakeShared<FJsonValueNumber>(Rotation.Roll));
    ActorObject->SetArrayField(TEXT("rotation"), RotationArray);
    
    FVector Scale = Actor->GetActorScale3D();
    TArray<TSharedPtr<FJsonValue>> ScaleArray;
    ScaleArray.Add(MakeShared<FJsonValueNumber>(Scale.X));
    ScaleArray.Add(MakeShared<FJsonValueNumber>(Scale.Y));
    ScaleArray.Add(MakeShared<FJsonValueNumber>(Scale.Z));
    ActorObject->SetArrayField(TEXT("scale"), ScaleArray);
    
    return MakeShared<FJsonValueObject>(ActorObject);
}

TSharedPtr<FJsonObject> FUnrealMCPCommonUtils::ActorToJsonObject(AActor* Actor, bool bDetailed)
{
    if (!Actor)
    {
        return nullptr;
    }

    TSharedPtr<FJsonObject> ActorObject = MakeShared<FJsonObject>();

    // Sanitize strings to avoid encoding issues
    FString ActorName = Actor->GetName();
    FString ClassName = Actor->GetClass()->GetName();

    // Remove any non-ASCII characters that might cause encoding issues
    ActorName = ActorName.Replace(TEXT("ç"), TEXT("c"));
    ActorName = ActorName.Replace(TEXT("ã"), TEXT("a"));
    ActorName = ActorName.Replace(TEXT("õ"), TEXT("o"));
    ActorName = ActorName.Replace(TEXT("á"), TEXT("a"));
    ActorName = ActorName.Replace(TEXT("é"), TEXT("e"));
    ActorName = ActorName.Replace(TEXT("í"), TEXT("i"));
    ActorName = ActorName.Replace(TEXT("ó"), TEXT("o"));
    ActorName = ActorName.Replace(TEXT("ú"), TEXT("u"));

    ActorObject->SetStringField(TEXT("name"), ActorName);
    ActorObject->SetStringField(TEXT("class"), ClassName);

    FVector Location = Actor->GetActorLocation();
    TArray<TSharedPtr<FJsonValue>> LocationArray;
    LocationArray.Add(MakeShared<FJsonValueNumber>(Location.X));
    LocationArray.Add(MakeShared<FJsonValueNumber>(Location.Y));
    LocationArray.Add(MakeShared<FJsonValueNumber>(Location.Z));
    ActorObject->SetArrayField(TEXT("location"), LocationArray);

    FRotator Rotation = Actor->GetActorRotation();
    TArray<TSharedPtr<FJsonValue>> RotationArray;
    RotationArray.Add(MakeShared<FJsonValueNumber>(Rotation.Pitch));
    RotationArray.Add(MakeShared<FJsonValueNumber>(Rotation.Yaw));
    RotationArray.Add(MakeShared<FJsonValueNumber>(Rotation.Roll));
    ActorObject->SetArrayField(TEXT("rotation"), RotationArray);

    FVector Scale = Actor->GetActorScale3D();
    TArray<TSharedPtr<FJsonValue>> ScaleArray;
    ScaleArray.Add(MakeShared<FJsonValueNumber>(Scale.X));
    ScaleArray.Add(MakeShared<FJsonValueNumber>(Scale.Y));
    ScaleArray.Add(MakeShared<FJsonValueNumber>(Scale.Z));
    ActorObject->SetArrayField(TEXT("scale"), ScaleArray);

    return ActorObject;
}

UK2Node_Event* FUnrealMCPCommonUtils::FindExistingEventNode(UEdGraph* Graph, const FString& EventName)
{
    if (!Graph)
    {
        return nullptr;
    }

    // Look for existing event nodes
    for (UEdGraphNode* Node : Graph->Nodes)
    {
        UK2Node_Event* EventNode = Cast<UK2Node_Event>(Node);
        if (EventNode && EventNode->EventReference.GetMemberName() == FName(*EventName))
        {
            UE_LOG(LogTemp, Display, TEXT("Found existing event node with name: %s"), *EventName);
            return EventNode;
        }
    }

    return nullptr;
}

bool FUnrealMCPCommonUtils::SetObjectProperty(UObject* Object, const FString& PropertyName, 
                                     const TSharedPtr<FJsonValue>& Value, FString& OutErrorMessage)
{
    if (!Object)
    {
        OutErrorMessage = TEXT("Invalid object");
        return false;
    }

    FProperty* Property = Object->GetClass()->FindPropertyByName(*PropertyName);
    if (!Property)
    {
        OutErrorMessage = FString::Printf(TEXT("Property not found: %s"), *PropertyName);
        return false;
    }

    void* PropertyAddr = Property->ContainerPtrToValuePtr<void>(Object);
    
    // Handle different property types
    if (FBoolProperty* BoolProp = CastField<FBoolProperty>(Property))
    {
        BoolProp->SetPropertyValue(PropertyAddr, Value->AsBool());
        return true;
    }
    else if (FIntProperty* IntProp = CastField<FIntProperty>(Property))
    {
        int32 IntValue = static_cast<int32>(Value->AsNumber());
        IntProp->SetPropertyValue_InContainer(Object, IntValue);
        return true;
    }
    else if (FFloatProperty* FloatProp = CastField<FFloatProperty>(Property))
    {
        FloatProp->SetPropertyValue(PropertyAddr, Value->AsNumber());
        return true;
    }
    else if (FStrProperty* StrProp = CastField<FStrProperty>(Property))
    {
        StrProp->SetPropertyValue(PropertyAddr, Value->AsString());
        return true;
    }
    else if (FByteProperty* ByteProp = CastField<FByteProperty>(Property))
    {
        UEnum* EnumDef = ByteProp ? ByteProp->GetIntPropertyEnum() : nullptr;
        
        // If this is a TEnumAsByte property (has associated enum)
        if (EnumDef)
        {
            // Handle numeric value
            if (Value->Type == EJson::Number)
            {
                uint8 ByteValue = static_cast<uint8>(Value->AsNumber());
                ByteProp->SetPropertyValue(PropertyAddr, ByteValue);
                
                UE_LOG(LogTemp, Display, TEXT("Setting enum property %s to numeric value: %d"), 
                      *PropertyName, ByteValue);
                return true;
            }
            // Handle string enum value
            else if (Value->Type == EJson::String)
            {
                FString EnumValueName = Value->AsString();
                
                // Try to convert numeric string to number first
                if (EnumValueName.IsNumeric())
                {
                    uint8 ByteValue = FCString::Atoi(*EnumValueName);
                    ByteProp->SetPropertyValue(PropertyAddr, ByteValue);
                    
                    UE_LOG(LogTemp, Display, TEXT("Setting enum property %s to numeric string value: %s -> %d"), 
                          *PropertyName, *EnumValueName, ByteValue);
                    return true;
                }
                
                // Handle qualified enum names (e.g., "Player0" or "EAutoReceiveInput::Player0")
                if (EnumValueName.Contains(TEXT("::")))
                {
                    EnumValueName.Split(TEXT("::"), nullptr, &EnumValueName);
                }
                
                int64 EnumValue = EnumDef->GetValueByNameString(EnumValueName);
                if (EnumValue == INDEX_NONE)
                {
                    // Try with full name as fallback
                    EnumValue = EnumDef->GetValueByNameString(Value->AsString());
                }
                
                if (EnumValue != INDEX_NONE)
                {
                    ByteProp->SetPropertyValue(PropertyAddr, static_cast<uint8>(EnumValue));
                    
                    UE_LOG(LogTemp, Display, TEXT("Setting enum property %s to name value: %s -> %lld"), 
                          *PropertyName, *EnumValueName, EnumValue);
                    return true;
                }
                else
                {
                    // Log all possible enum values for debugging
                    UE_LOG(LogTemp, Warning, TEXT("Could not find enum value for '%s'. Available options:"), *EnumValueName);
                    for (int32 i = 0; i < EnumDef->NumEnums(); i++)
                    {
                        UE_LOG(LogTemp, Warning, TEXT("  - %s (value: %d)"), 
                               *EnumDef->GetNameStringByIndex(i), EnumDef->GetValueByIndex(i));
                    }
                    
                    OutErrorMessage = FString::Printf(TEXT("Could not find enum value for '%s'"), *EnumValueName);
                    return false;
                }
            }
        }
        else
        {
            // Regular byte property
            uint8 ByteValue = static_cast<uint8>(Value->AsNumber());
            ByteProp->SetPropertyValue(PropertyAddr, ByteValue);
            return true;
        }
    }
    else if (FEnumProperty* EnumProp = CastField<FEnumProperty>(Property))
    {
        UEnum* EnumDef = EnumProp ? EnumProp->GetEnum() : nullptr;
        FNumericProperty* UnderlyingNumericProp = EnumProp ? EnumProp->GetUnderlyingProperty() : nullptr;
        
        if (EnumDef && UnderlyingNumericProp)
        {
            // Handle numeric value
            if (Value->Type == EJson::Number)
            {
                int64 EnumValue = static_cast<int64>(Value->AsNumber());
                UnderlyingNumericProp->SetIntPropertyValue(PropertyAddr, EnumValue);
                
                UE_LOG(LogTemp, Display, TEXT("Setting enum property %s to numeric value: %lld"), 
                      *PropertyName, EnumValue);
                return true;
            }
            // Handle string enum value
            else if (Value->Type == EJson::String)
            {
                FString EnumValueName = Value->AsString();
                
                // Try to convert numeric string to number first
                if (EnumValueName.IsNumeric())
                {
                    int64 EnumValue = FCString::Atoi64(*EnumValueName);
                    UnderlyingNumericProp->SetIntPropertyValue(PropertyAddr, EnumValue);
                    
                    UE_LOG(LogTemp, Display, TEXT("Setting enum property %s to numeric string value: %s -> %lld"), 
                          *PropertyName, *EnumValueName, EnumValue);
                    return true;
                }
                
                // Handle qualified enum names
                if (EnumValueName.Contains(TEXT("::")))
                {
                    EnumValueName.Split(TEXT("::"), nullptr, &EnumValueName);
                }
                
                int64 EnumValue = EnumDef->GetValueByNameString(EnumValueName);
                if (EnumValue == INDEX_NONE)
                {
                    // Try with full name as fallback
                    EnumValue = EnumDef->GetValueByNameString(Value->AsString());
                }
                
                if (EnumValue != INDEX_NONE)
                {
                    UnderlyingNumericProp->SetIntPropertyValue(PropertyAddr, EnumValue);
                    
                    UE_LOG(LogTemp, Display, TEXT("Setting enum property %s to name value: %s -> %lld"), 
                          *PropertyName, *EnumValueName, EnumValue);
                    return true;
                }
                else
                {
                    // Log all possible enum values for debugging
                    UE_LOG(LogTemp, Warning, TEXT("Could not find enum value for '%s'. Available options:"), *EnumValueName);
                    for (int32 i = 0; i < EnumDef->NumEnums(); i++)
                    {
                        UE_LOG(LogTemp, Warning, TEXT("  - %s (value: %d)"), 
                               *EnumDef->GetNameStringByIndex(i), EnumDef->GetValueByIndex(i));
                    }
                    
                    OutErrorMessage = FString::Printf(TEXT("Could not find enum value for '%s'"), *EnumValueName);
                    return false;
                }
            }
        }
    }
    
    OutErrorMessage = FString::Printf(TEXT("Unsupported property type: %s for property %s"),
                                    *Property->GetClass()->GetName(), *PropertyName);
    return false;
}

// ========================================================================
// Advanced Actor Creation System Implementation
// ========================================================================

AActor* FUnrealMCPCommonUtils::CreateActor(UWorld* World, const FMCPActorCreationParams& Params)
{
    if (!World)
    {
        UE_LOG(LogTemp, Error, TEXT("CreateActor: Invalid World"));
        return nullptr;
    }

    if (!ValidateActorName(Params.ActorName))
    {
        UE_LOG(LogTemp, Error, TEXT("CreateActor: Invalid actor name: %s"), *Params.ActorName);
        return nullptr;
    }

    if (!IsValidLocation(Params.Location) || !IsValidRotation(Params.Rotation) || !IsValidScale(Params.Scale))
    {
        UE_LOG(LogTemp, Error, TEXT("CreateActor: Invalid transform parameters"));
        return nullptr;
    }

    AActor* CreatedActor = nullptr;

    // Create actor based on type
    if (Params.ActorType == TEXT("StaticMeshActor"))
    {
        CreatedActor = CreateStaticMeshActor(World, Params);
    }
    else if (Params.ActorType == TEXT("SkeletalMeshActor"))
    {
        CreatedActor = CreateSkeletalMeshActor(World, Params);
    }
    else if (Params.ActorType == TEXT("PointLight"))
    {
        CreatedActor = CreatePointLight(World, Params);
    }
    else if (Params.ActorType == TEXT("SpotLight"))
    {
        CreatedActor = CreateSpotLight(World, Params);
    }
    else if (Params.ActorType == TEXT("DirectionalLight"))
    {
        CreatedActor = CreateDirectionalLight(World, Params);
    }
    else if (Params.ActorType == TEXT("TriggerVolume"))
    {
        CreatedActor = CreateTriggerVolume(World, Params);
    }
    else if (Params.ActorType == TEXT("BlockingVolume"))
    {
        CreatedActor = CreateBlockingVolume(World, Params);
    }
    else if (Params.ActorType == TEXT("Character"))
    {
        CreatedActor = CreateCharacter(World, Params);
    }
    else if (Params.ActorType == TEXT("Pawn"))
    {
        CreatedActor = CreatePawn(World, Params);
    }
    else if (!Params.BlueprintPath.IsEmpty())
    {
        // Create from Blueprint
        UBlueprint* Blueprint = LoadBlueprint(Params.BlueprintPath);
        if (Blueprint && Blueprint->GeneratedClass)
        {
            FActorSpawnParameters SpawnParams;
            SpawnParams.Name = FName(*Params.ActorName);
            SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;

            FTransform SpawnTransform;
            SpawnTransform.SetLocation(Params.Location);
            SpawnTransform.SetRotation(FQuat(Params.Rotation));
            SpawnTransform.SetScale3D(Params.Scale);

            CreatedActor = World->SpawnActor<AActor>(Blueprint->GeneratedClass, SpawnTransform, SpawnParams);
        }
    }
    else
    {
        // Create generic Actor
        FActorSpawnParameters SpawnParams;
        SpawnParams.Name = FName(*Params.ActorName);
        SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;

        CreatedActor = World->SpawnActor<AActor>(AActor::StaticClass(), Params.Location, Params.Rotation, SpawnParams);
        if (CreatedActor)
        {
            CreatedActor->SetActorScale3D(Params.Scale);
        }
    }

    if (CreatedActor)
    {
        // Configure actor properties
        ConfigureActorComponents(CreatedActor, Params);
        ConfigureActorProperties(CreatedActor, Params);
        ConfigureActorPhysics(CreatedActor, Params);
        ConfigureActorRendering(CreatedActor, Params);
        ConfigureActorAI(CreatedActor, Params);

        // Mark as modified for editor
        CreatedActor->MarkPackageDirty();

        // Refresh editor viewport
        if (GEditor)
        {
            GEditor->RedrawLevelEditingViewports();
        }

        UE_LOG(LogTemp, Log, TEXT("Successfully created actor: %s of type: %s"), *Params.ActorName, *Params.ActorType);
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("Failed to create actor: %s of type: %s"), *Params.ActorName, *Params.ActorType);
    }

    return CreatedActor;
}

AActor* FUnrealMCPCommonUtils::CreateActorFromJson(UWorld* World, const TSharedPtr<FJsonObject>& JsonParams)
{
    if (!JsonParams.IsValid())
    {
        UE_LOG(LogTemp, Error, TEXT("CreateActorFromJson: Invalid JSON parameters"));
        return nullptr;
    }

    FMCPActorCreationParams Params = ParseActorCreationParams(JsonParams);
    return CreateActor(World, Params);
}

FMCPActorCreationParams FUnrealMCPCommonUtils::ParseActorCreationParams(const TSharedPtr<FJsonObject>& JsonParams)
{
    FMCPActorCreationParams Params;

    if (!JsonParams.IsValid())
    {
        return Params;
    }

    // Basic properties
    JsonParams->TryGetStringField(TEXT("name"), Params.ActorName);
    JsonParams->TryGetStringField(TEXT("type"), Params.ActorType);

    // Transform
    Params.Location = GetVectorFromJson(JsonParams, TEXT("location"));
    Params.Rotation = GetRotatorFromJson(JsonParams, TEXT("rotation"));
    Params.Scale = GetVectorFromJson(JsonParams, TEXT("scale"));
    if (Params.Scale.IsZero())
    {
        Params.Scale = FVector::OneVector;
    }

    // Asset paths
    JsonParams->TryGetStringField(TEXT("static_mesh_path"), Params.StaticMeshPath);
    JsonParams->TryGetStringField(TEXT("skeletal_mesh_path"), Params.SkeletalMeshPath);
    JsonParams->TryGetStringField(TEXT("material_path"), Params.MaterialPath);
    JsonParams->TryGetStringField(TEXT("blueprint_path"), Params.BlueprintPath);
    JsonParams->TryGetStringField(TEXT("behavior_tree_path"), Params.BehaviorTreePath);
    JsonParams->TryGetStringField(TEXT("blackboard_path"), Params.BlackboardPath);

    // AI properties
    JsonParams->TryGetBoolField(TEXT("enable_ai"), Params.bEnableAI);
    JsonParams->TryGetStringField(TEXT("ai_controller_class"), Params.AIControllerClass);

    // Physics properties
    JsonParams->TryGetBoolField(TEXT("enable_physics"), Params.bEnablePhysics);
    JsonParams->TryGetBoolField(TEXT("enable_collision"), Params.bEnableCollision);
    JsonParams->TryGetNumberField(TEXT("mass"), Params.Mass);

    // Rendering properties
    JsonParams->TryGetBoolField(TEXT("cast_shadow"), Params.bCastShadow);
    JsonParams->TryGetBoolField(TEXT("receives_decals"), Params.bReceivesDecals);
    JsonParams->TryGetNumberField(TEXT("render_priority"), Params.RenderPriority);

    return Params;
}

// ========================================================================
// Specialized Actor Creation Functions
// ========================================================================

AStaticMeshActor* FUnrealMCPCommonUtils::CreateStaticMeshActor(UWorld* World, const FMCPActorCreationParams& Params)
{
    if (!World)
    {
        return nullptr;
    }

    FActorSpawnParameters SpawnParams;
    SpawnParams.Name = FName(*Params.ActorName);
    SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;

    AStaticMeshActor* StaticMeshActor = World->SpawnActor<AStaticMeshActor>(AStaticMeshActor::StaticClass(),
                                                                           Params.Location, Params.Rotation, SpawnParams);
    if (StaticMeshActor)
    {
        StaticMeshActor->SetActorScale3D(Params.Scale);

        UStaticMeshComponent* MeshComponent = StaticMeshActor->GetStaticMeshComponent();
        if (MeshComponent && !Params.StaticMeshPath.IsEmpty())
        {
            UStaticMesh* StaticMesh = LoadStaticMesh(Params.StaticMeshPath);
            if (StaticMesh)
            {
                MeshComponent->SetStaticMesh(StaticMesh);
                UE_LOG(LogTemp, Log, TEXT("Applied static mesh: %s"), *Params.StaticMeshPath);
            }
        }

        if (MeshComponent && !Params.MaterialPath.IsEmpty())
        {
            UMaterialInterface* Material = LoadMaterial(Params.MaterialPath);
            if (Material)
            {
                MeshComponent->SetMaterial(0, Material);
                UE_LOG(LogTemp, Log, TEXT("Applied material: %s"), *Params.MaterialPath);
            }
        }
    }

    return StaticMeshActor;
}

ASkeletalMeshActor* FUnrealMCPCommonUtils::CreateSkeletalMeshActor(UWorld* World, const FMCPActorCreationParams& Params)
{
    // Note: ASkeletalMeshActor doesn't exist in UE5.6, using regular Actor with SkeletalMeshComponent
    if (!World)
    {
        return nullptr;
    }

    FActorSpawnParameters SpawnParams;
    SpawnParams.Name = FName(*Params.ActorName);
    SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;

    ASkeletalMeshActor* SkeletalMeshActor = World->SpawnActor<ASkeletalMeshActor>(ASkeletalMeshActor::StaticClass(),
                                                                                 Params.Location, Params.Rotation, SpawnParams);
    if (SkeletalMeshActor)
    {
        SkeletalMeshActor->SetActorScale3D(Params.Scale);

        USkeletalMeshComponent* MeshComponent = SkeletalMeshActor->GetSkeletalMeshComponent();
        if (MeshComponent && !Params.SkeletalMeshPath.IsEmpty())
        {
            USkeletalMesh* SkeletalMesh = LoadSkeletalMesh(Params.SkeletalMeshPath);
            if (SkeletalMesh)
            {
                MeshComponent->SetSkeletalMesh(SkeletalMesh);
                UE_LOG(LogTemp, Log, TEXT("Applied skeletal mesh: %s"), *Params.SkeletalMeshPath);
            }
        }

        if (MeshComponent && !Params.MaterialPath.IsEmpty())
        {
            UMaterialInterface* Material = LoadMaterial(Params.MaterialPath);
            if (Material)
            {
                MeshComponent->SetMaterial(0, Material);
                UE_LOG(LogTemp, Log, TEXT("Applied material: %s"), *Params.MaterialPath);
            }
        }
    }

    return Cast<ASkeletalMeshActor>(Actor); // This will return nullptr but that's expected
}

APointLight* FUnrealMCPCommonUtils::CreatePointLight(UWorld* World, const FMCPActorCreationParams& Params)
{
    if (!World)
    {
        return nullptr;
    }

    FActorSpawnParameters SpawnParams;
    SpawnParams.Name = FName(*Params.ActorName);
    SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;

    APointLight* PointLight = World->SpawnActor<APointLight>(APointLight::StaticClass(),
                                                            Params.Location, Params.Rotation, SpawnParams);
    if (PointLight)
    {
        PointLight->SetActorScale3D(Params.Scale);

        UPointLightComponent* LightComponent = PointLight->GetPointLightComponent();
        if (LightComponent)
        {
            // Configure light properties from Params
            if (Params.FloatProperties.Contains(TEXT("Intensity")))
            {
                LightComponent->SetIntensity(Params.FloatProperties[TEXT("Intensity")]);
            }
            if (Params.FloatProperties.Contains(TEXT("AttenuationRadius")))
            {
                LightComponent->SetAttenuationRadius(Params.FloatProperties[TEXT("AttenuationRadius")]);
            }
            if (Params.ColorProperties.Contains(TEXT("LightColor")))
            {
                LightComponent->SetLightColor(Params.ColorProperties[TEXT("LightColor")]);
            }
        }
    }

    return PointLight;
}

ASpotLight* FUnrealMCPCommonUtils::CreateSpotLight(UWorld* World, const FMCPActorCreationParams& Params)
{
    if (!World)
    {
        return nullptr;
    }

    FActorSpawnParameters SpawnParams;
    SpawnParams.Name = FName(*Params.ActorName);
    SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;

    ASpotLight* SpotLight = World->SpawnActor<ASpotLight>(ASpotLight::StaticClass(),
                                                         Params.Location, Params.Rotation, SpawnParams);
    if (SpotLight)
    {
        SpotLight->SetActorScale3D(Params.Scale);

        ULightComponent* LightComponent = SpotLight->GetLightComponent();
        if (LightComponent)
        {
            // Configure spot light properties
            if (Params.FloatProperties.Contains(TEXT("Intensity")))
            {
                LightComponent->SetIntensity(Params.FloatProperties[TEXT("Intensity")]);
            }
            if (Params.ColorProperties.Contains(TEXT("LightColor")))
            {
                LightComponent->SetLightColor(Params.ColorProperties[TEXT("LightColor")]);
            }
        }
    }

    return SpotLight;
}

// ========================================================================
// Asset Loading Utilities
// ========================================================================

UStaticMesh* FUnrealMCPCommonUtils::LoadStaticMesh(const FString& AssetPath)
{
    if (AssetPath.IsEmpty() || !ValidateAssetPath(AssetPath))
    {
        return nullptr;
    }

    UStaticMesh* StaticMesh = LoadObject<UStaticMesh>(nullptr, *AssetPath);
    if (!StaticMesh)
    {
        UE_LOG(LogTemp, Warning, TEXT("Failed to load static mesh: %s"), *AssetPath);
    }
    return StaticMesh;
}

USkeletalMesh* FUnrealMCPCommonUtils::LoadSkeletalMesh(const FString& AssetPath)
{
    if (AssetPath.IsEmpty() || !ValidateAssetPath(AssetPath))
    {
        return nullptr;
    }

    USkeletalMesh* SkeletalMesh = LoadObject<USkeletalMesh>(nullptr, *AssetPath);
    if (!SkeletalMesh)
    {
        UE_LOG(LogTemp, Warning, TEXT("Failed to load skeletal mesh: %s"), *AssetPath);
    }
    return SkeletalMesh;
}

UMaterialInterface* FUnrealMCPCommonUtils::LoadMaterial(const FString& AssetPath)
{
    if (AssetPath.IsEmpty() || !ValidateAssetPath(AssetPath))
    {
        return nullptr;
    }

    UMaterialInterface* Material = LoadObject<UMaterialInterface>(nullptr, *AssetPath);
    if (!Material)
    {
        UE_LOG(LogTemp, Warning, TEXT("Failed to load material: %s"), *AssetPath);
    }
    return Material;
}

UBlueprint* FUnrealMCPCommonUtils::LoadBlueprint(const FString& AssetPath)
{
    if (AssetPath.IsEmpty() || !ValidateAssetPath(AssetPath))
    {
        return nullptr;
    }

    UBlueprint* Blueprint = LoadObject<UBlueprint>(nullptr, *AssetPath);
    if (!Blueprint)
    {
        UE_LOG(LogTemp, Warning, TEXT("Failed to load blueprint: %s"), *AssetPath);
    }
    return Blueprint;
}

UBehaviorTree* FUnrealMCPCommonUtils::LoadBehaviorTree(const FString& AssetPath)
{
    if (AssetPath.IsEmpty() || !ValidateAssetPath(AssetPath))
    {
        return nullptr;
    }

    UBehaviorTree* BehaviorTree = LoadObject<UBehaviorTree>(nullptr, *AssetPath);
    if (!BehaviorTree)
    {
        UE_LOG(LogTemp, Warning, TEXT("Failed to load behavior tree: %s"), *AssetPath);
    }
    return BehaviorTree;
}

UBlackboardData* FUnrealMCPCommonUtils::LoadBlackboard(const FString& AssetPath)
{
    if (AssetPath.IsEmpty() || !ValidateAssetPath(AssetPath))
    {
        return nullptr;
    }

    UBlackboardData* BlackboardData = LoadObject<UBlackboardData>(nullptr, *AssetPath);
    if (!BlackboardData)
    {
        UE_LOG(LogTemp, Warning, TEXT("Failed to load blackboard: %s"), *AssetPath);
    }
    return BlackboardData;
}

// ========================================================================
// Validation Utilities
// ========================================================================

bool FUnrealMCPCommonUtils::ValidateActorName(const FString& ActorName)
{
    if (ActorName.IsEmpty())
    {
        return false;
    }

    // Check for invalid characters
    if (ActorName.Contains(TEXT(" ")) || ActorName.Contains(TEXT("\t")) || ActorName.Contains(TEXT("\n")))
    {
        return false;
    }

    // Check length
    if (ActorName.Len() > 64)
    {
        return false;
    }

    return true;
}

bool FUnrealMCPCommonUtils::ValidateAssetPath(const FString& AssetPath)
{
    if (AssetPath.IsEmpty())
    {
        return false;
    }

    // Basic path validation
    if (!AssetPath.StartsWith(TEXT("/")) && !AssetPath.StartsWith(TEXT("/")))
    {
        return false;
    }

    return true;
}

bool FUnrealMCPCommonUtils::IsValidLocation(const FVector& Location)
{
    // Check for NaN or infinite values
    if (!FMath::IsFinite(Location.X) || !FMath::IsFinite(Location.Y) || !FMath::IsFinite(Location.Z))
    {
        return false;
    }

    // Check for reasonable bounds (within 10km)
    const float MaxDistance = 1000000.0f; // 10km in cm
    if (Location.Size() > MaxDistance)
    {
        return false;
    }

    return true;
}

bool FUnrealMCPCommonUtils::IsValidRotation(const FRotator& Rotation)
{
    // Check for NaN or infinite values
    if (!FMath::IsFinite(Rotation.Pitch) || !FMath::IsFinite(Rotation.Yaw) || !FMath::IsFinite(Rotation.Roll))
    {
        return false;
    }

    return true;
}

bool FUnrealMCPCommonUtils::IsValidScale(const FVector& Scale)
{
    // Check for NaN or infinite values
    if (!FMath::IsFinite(Scale.X) || !FMath::IsFinite(Scale.Y) || !FMath::IsFinite(Scale.Z))
    {
        return false;
    }

    // Check for zero or negative scale
    if (Scale.X <= 0.0f || Scale.Y <= 0.0f || Scale.Z <= 0.0f)
    {
        return false;
    }

    // Check for reasonable scale bounds
    const float MaxScale = 100.0f;
    const float MinScale = 0.01f;
    if (Scale.X > MaxScale || Scale.Y > MaxScale || Scale.Z > MaxScale ||
        Scale.X < MinScale || Scale.Y < MinScale || Scale.Z < MinScale)
    {
        return false;
    }

    return true;
}

// ========================================================================
// Actor Configuration Functions
// ========================================================================

bool FUnrealMCPCommonUtils::ConfigureActorComponents(AActor* Actor, const FMCPActorCreationParams& Params)
{
    if (!Actor)
    {
        return false;
    }

    // Add components based on parameters
    for (const auto& ComponentProp : Params.ComponentProperties)
    {
        const FString& ComponentName = ComponentProp.Key;
        const FString& ComponentType = ComponentProp.Value;

        if (ComponentType == TEXT("StaticMeshComponent"))
        {
            AddStaticMeshComponent(Actor, ComponentName, Params.StaticMeshPath);
        }
        else if (ComponentType == TEXT("SkeletalMeshComponent"))
        {
            AddSkeletalMeshComponent(Actor, ComponentName, Params.SkeletalMeshPath);
        }
        else if (ComponentType == TEXT("BoxComponent"))
        {
            FVector BoxExtent = FVector(50.0f);
            if (Params.VectorProperties.Contains(ComponentName + TEXT("_Extent")))
            {
                BoxExtent = Params.VectorProperties[ComponentName + TEXT("_Extent")];
            }
            AddBoxCollisionComponent(Actor, ComponentName, BoxExtent);
        }
        else if (ComponentType == TEXT("SphereComponent"))
        {
            float SphereRadius = 50.0f;
            if (Params.FloatProperties.Contains(ComponentName + TEXT("_Radius")))
            {
                SphereRadius = Params.FloatProperties[ComponentName + TEXT("_Radius")];
            }
            AddSphereCollisionComponent(Actor, ComponentName, SphereRadius);
        }
        else if (ComponentType == TEXT("CapsuleComponent"))
        {
            float CapsuleRadius = 50.0f;
            float CapsuleHalfHeight = 100.0f;
            if (Params.FloatProperties.Contains(ComponentName + TEXT("_Radius")))
            {
                CapsuleRadius = Params.FloatProperties[ComponentName + TEXT("_Radius")];
            }
            if (Params.FloatProperties.Contains(ComponentName + TEXT("_HalfHeight")))
            {
                CapsuleHalfHeight = Params.FloatProperties[ComponentName + TEXT("_HalfHeight")];
            }
            AddCapsuleCollisionComponent(Actor, ComponentName, CapsuleRadius, CapsuleHalfHeight);
        }
    }

    return true;
}

bool FUnrealMCPCommonUtils::ConfigureActorProperties(AActor* Actor, const FMCPActorCreationParams& Params)
{
    if (!Actor)
    {
        return false;
    }

    // Set float properties
    for (const auto& FloatProp : Params.FloatProperties)
    {
        FString ErrorMessage;
        TSharedPtr<FJsonValue> Value = MakeShared<FJsonValueNumber>(FloatProp.Value);
        SetObjectProperty(Actor, FloatProp.Key, Value, ErrorMessage);
    }

    // Set bool properties
    for (const auto& BoolProp : Params.BoolProperties)
    {
        FString ErrorMessage;
        TSharedPtr<FJsonValue> Value = MakeShared<FJsonValueBoolean>(BoolProp.Value);
        SetObjectProperty(Actor, BoolProp.Key, Value, ErrorMessage);
    }

    // Set vector properties
    for (const auto& VectorProp : Params.VectorProperties)
    {
        FString ErrorMessage;
        TArray<TSharedPtr<FJsonValue>> VectorArray;
        VectorArray.Add(MakeShared<FJsonValueNumber>(VectorProp.Value.X));
        VectorArray.Add(MakeShared<FJsonValueNumber>(VectorProp.Value.Y));
        VectorArray.Add(MakeShared<FJsonValueNumber>(VectorProp.Value.Z));
        TSharedPtr<FJsonValue> Value = MakeShared<FJsonValueArray>(VectorArray);
        SetObjectProperty(Actor, VectorProp.Key, Value, ErrorMessage);
    }

    return true;
}

bool FUnrealMCPCommonUtils::ConfigureActorPhysics(AActor* Actor, const FMCPActorCreationParams& Params)
{
    if (!Actor)
    {
        return false;
    }

    // Configure physics for primitive components
    TArray<UPrimitiveComponent*> PrimitiveComponents;
    Actor->GetComponents<UPrimitiveComponent>(PrimitiveComponents);

    for (UPrimitiveComponent* PrimComp : PrimitiveComponents)
    {
        if (PrimComp)
        {
            // Set collision
            PrimComp->SetCollisionEnabled(Params.bEnableCollision ? ECollisionEnabled::QueryAndPhysics : ECollisionEnabled::NoCollision);

            // Set physics simulation
            if (Params.bEnablePhysics)
            {
                PrimComp->SetSimulatePhysics(true);
                PrimComp->SetMassOverrideInKg(NAME_None, Params.Mass, true);
            }
        }
    }

    return true;
}

bool FUnrealMCPCommonUtils::ConfigureActorRendering(AActor* Actor, const FMCPActorCreationParams& Params)
{
    if (!Actor)
    {
        return false;
    }

    // Configure rendering for primitive components
    TArray<UPrimitiveComponent*> PrimitiveComponents;
    Actor->GetComponents<UPrimitiveComponent>(PrimitiveComponents);

    for (UPrimitiveComponent* PrimComp : PrimitiveComponents)
    {
        if (PrimComp)
        {
            PrimComp->SetCastShadow(Params.bCastShadow);
            PrimComp->bReceivesDecals = Params.bReceivesDecals;
            PrimComp->SetTranslucencySortPriority(Params.RenderPriority);
        }
    }

    return true;
}

bool FUnrealMCPCommonUtils::ConfigureActorAI(AActor* Actor, const FMCPActorCreationParams& Params)
{
    if (!Actor || !Params.bEnableAI)
    {
        return false;
    }

    // Configure AI for Pawn actors
    APawn* Pawn = Cast<APawn>(Actor);
    if (Pawn)
    {
        // Set AI controller class if specified
        if (!Params.AIControllerClass.IsEmpty())
        {
            UClass* AIControllerClass = FindObject<UClass>(ANY_PACKAGE, *Params.AIControllerClass);
            if (AIControllerClass)
            {
                Pawn->AIControllerClass = AIControllerClass;
            }
        }

        // Spawn AI controller
        if (UWorld* World = Actor->GetWorld())
        {
            Pawn->SpawnDefaultController();

            AAIController* AIController = Cast<AAIController>(Pawn->GetController());
            if (AIController)
            {
                // Set behavior tree
                if (!Params.BehaviorTreePath.IsEmpty())
                {
                    UBehaviorTree* BehaviorTree = LoadBehaviorTree(Params.BehaviorTreePath);
                    if (BehaviorTree)
                    {
                        AIController->RunBehaviorTree(BehaviorTree);
                    }
                }

                // Set blackboard
                if (!Params.BlackboardPath.IsEmpty())
                {
                    UBlackboardData* BlackboardData = LoadBlackboard(Params.BlackboardPath);
                    if (BlackboardData)
                    {
                        UBlackboardComponent* BlackboardComp = AIController->GetBlackboardComponent();
                        if (BlackboardComp)
                        {
                            BlackboardComp->InitializeBlackboard(*BlackboardData);
                        }
                    }
                }
            }
        }
    }

    return true;
}

// ========================================================================
// Component Creation Utilities
// ========================================================================

UStaticMeshComponent* FUnrealMCPCommonUtils::AddStaticMeshComponent(AActor* Actor, const FString& ComponentName, const FString& MeshPath)
{
    if (!Actor)
    {
        return nullptr;
    }

    UStaticMeshComponent* MeshComponent = NewObject<UStaticMeshComponent>(Actor, FName(*ComponentName));
    if (MeshComponent)
    {
        Actor->AddInstanceComponent(MeshComponent);
        MeshComponent->AttachToComponent(Actor->GetRootComponent(), FAttachmentTransformRules::KeepWorldTransform);

        if (!MeshPath.IsEmpty())
        {
            UStaticMesh* StaticMesh = LoadStaticMesh(MeshPath);
            if (StaticMesh)
            {
                MeshComponent->SetStaticMesh(StaticMesh);
            }
        }

        MeshComponent->RegisterComponent();
        UE_LOG(LogTemp, Log, TEXT("Added StaticMeshComponent: %s to actor: %s"), *ComponentName, *Actor->GetName());
    }

    return MeshComponent;
}

USkeletalMeshComponent* FUnrealMCPCommonUtils::AddSkeletalMeshComponent(AActor* Actor, const FString& ComponentName, const FString& MeshPath)
{
    if (!Actor)
    {
        return nullptr;
    }

    USkeletalMeshComponent* MeshComponent = NewObject<USkeletalMeshComponent>(Actor, FName(*ComponentName));
    if (MeshComponent)
    {
        Actor->AddInstanceComponent(MeshComponent);
        MeshComponent->AttachToComponent(Actor->GetRootComponent(), FAttachmentTransformRules::KeepWorldTransform);

        if (!MeshPath.IsEmpty())
        {
            USkeletalMesh* SkeletalMesh = LoadSkeletalMesh(MeshPath);
            if (SkeletalMesh)
            {
                MeshComponent->SetSkeletalMesh(SkeletalMesh);
            }
        }

        MeshComponent->RegisterComponent();
        UE_LOG(LogTemp, Log, TEXT("Added SkeletalMeshComponent: %s to actor: %s"), *ComponentName, *Actor->GetName());
    }

    return MeshComponent;
}

UBoxComponent* FUnrealMCPCommonUtils::AddBoxCollisionComponent(AActor* Actor, const FString& ComponentName, const FVector& BoxExtent)
{
    if (!Actor)
    {
        return nullptr;
    }

    UBoxComponent* BoxComponent = NewObject<UBoxComponent>(Actor, FName(*ComponentName));
    if (BoxComponent)
    {
        Actor->AddInstanceComponent(BoxComponent);
        BoxComponent->AttachToComponent(Actor->GetRootComponent(), FAttachmentTransformRules::KeepWorldTransform);
        BoxComponent->SetBoxExtent(BoxExtent);
        BoxComponent->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
        BoxComponent->SetCollisionResponseToAllChannels(ECR_Block);
        BoxComponent->RegisterComponent();

        UE_LOG(LogTemp, Log, TEXT("Added BoxComponent: %s to actor: %s with extent: %s"),
               *ComponentName, *Actor->GetName(), *BoxExtent.ToString());
    }

    return BoxComponent;
}

USphereComponent* FUnrealMCPCommonUtils::AddSphereCollisionComponent(AActor* Actor, const FString& ComponentName, float SphereRadius)
{
    if (!Actor)
    {
        return nullptr;
    }

    USphereComponent* SphereComponent = NewObject<USphereComponent>(Actor, FName(*ComponentName));
    if (SphereComponent)
    {
        Actor->AddInstanceComponent(SphereComponent);
        SphereComponent->AttachToComponent(Actor->GetRootComponent(), FAttachmentTransformRules::KeepWorldTransform);
        SphereComponent->SetSphereRadius(SphereRadius);
        SphereComponent->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
        SphereComponent->SetCollisionResponseToAllChannels(ECR_Block);
        SphereComponent->RegisterComponent();

        UE_LOG(LogTemp, Log, TEXT("Added SphereComponent: %s to actor: %s with radius: %f"),
               *ComponentName, *Actor->GetName(), SphereRadius);
    }

    return SphereComponent;
}

UCapsuleComponent* FUnrealMCPCommonUtils::AddCapsuleCollisionComponent(AActor* Actor, const FString& ComponentName, float CapsuleRadius, float CapsuleHalfHeight)
{
    if (!Actor)
    {
        return nullptr;
    }

    UCapsuleComponent* CapsuleComponent = NewObject<UCapsuleComponent>(Actor, FName(*ComponentName));
    if (CapsuleComponent)
    {
        Actor->AddInstanceComponent(CapsuleComponent);
        CapsuleComponent->AttachToComponent(Actor->GetRootComponent(), FAttachmentTransformRules::KeepWorldTransform);
        CapsuleComponent->SetCapsuleSize(CapsuleRadius, CapsuleHalfHeight);
        CapsuleComponent->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
        CapsuleComponent->SetCollisionResponseToAllChannels(ECR_Block);
        CapsuleComponent->RegisterComponent();

        UE_LOG(LogTemp, Log, TEXT("Added CapsuleComponent: %s to actor: %s with radius: %f, half-height: %f"),
               *ComponentName, *Actor->GetName(), CapsuleRadius, CapsuleHalfHeight);
    }

    return CapsuleComponent;
}